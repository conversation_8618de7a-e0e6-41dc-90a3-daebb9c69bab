import {
  logoGithub,
  heart,
  link,
  rocket,
  search,
  // logoAndroid
} from 'ionicons/icons'
import telegram from '../assets/telegram.svg'
// import { RewardCardProps } from '../types/sharedTypes'
// import { constructWhatsAppLink } from '../helpers/helpers'
// import mediumCoins from '../assets/medium_coins.svg'
// import lowCoins from '../assets/low_coins.svg'
// import largeCoins from '../assets/large_coins_1.svg'

export const usefullLinks = [
  {
    link: 'Support US',
    value: 'https://www.buymeacoffee.com/bharathganji',
    icon: heart,
  },
  {
    link: 'PikPak Invitation (premium)',
    value:
      'https://mypikpak.com/drive/activity/invited?invitation-code=47295398',
    icon: rocket,
  },
  {
    link: 'Github',
    value: 'https://github.com/bharathganji/pikpak-plus',
    icon: logoGithub,
  },

  // {
  //   link: 'PikPak-Plus APK',
  //   value: 'https://icedrive.net/s/agT5yQPkkCGSuTfit4fPgZCFC9Y9',
  //   icon: logoAndroid,
  // },

  {
    link: 'Telegram',
    value: 'https://t.me/pikpak_plus',
    icon: telegram,
  },

  {
    link: 'Navi Downloader Android',
    value:
      'https://github.com/TachibanaGeneralLaboratories/download-navi/releases/',
    icon: link,
  },
  {
    link: 'VLC Media Player',
    value: 'https://www.videolan.org/vlc/',
    icon: link,
  },
  {
    link: 'Torent Search',
    value: 'https://jackett.pikpak-plus.com/',
    icon: search,
  },
]
export const help = [
  'Download torrent links to Cloud ⚡ - 80 TB/mon',
  'Cumulative download quota - 8 TB/mon',
  'Storage capacity of 10TB',
  'Search multiple Torrent Indexers',
  'Share files with your friends',
]

export const WEBDAV = [
  '500GB storage for 1 year',
  'webDAV username and password',
  'NO File restriction',
  'Premium support',
]

export const oneDollarPlan = [
  '100GB storage for 1 year',
  'webDAV username and password',
  'No file restriction',
  'premium support',
]

export const ANNOUNCEMENT_OF_END = [
  'We proudly supported our community with a free service for over 1 year.',
  'As of December 1st, 2024, a premium subscription is required',
  'Please consider purchasing premium to support us.',
]

// export const PURCHASE_COINS: RewardCardProps[] = [
//   {
//     noOfCoins: 100,
//     BuyLink: constructWhatsAppLink('HI, I want to buy 100 COINS'),
//     BuyText: '0.5$',
//     coinsImage: lowCoins,
//   },
//   {
//     noOfCoins: 250,
//     BuyLink: constructWhatsAppLink('HI, I want to buy 250 COINS'),
//     BuyText: '1$',
//     coinsImage: mediumCoins,
//   },
//   {
//     noOfCoins: 500,
//     BuyLink: constructWhatsAppLink('HI, I want to buy 500 COINS'),
//     BuyText: '2$',
//     coinsImage: largeCoins,
//   },
// ]

export const MAX_PLAY_SIZE_LIMIT_IN_BYTES = '4294967296'


import DiscontinuationNotice from '../../login/DiscontinuationNotice'

// eslint-disable-next-line @typescript-eslint/no-unused-vars, no-empty-pattern
export default function SignUpCard({ }: { callbackFunc: any }) {
  return (
    <>
      {/* <AuthCard
        titleHeading="Sign Up"
        nextTitle={{ text: 'Login', redirect: '/login' }}
        callbackFunc={callbackFunc}
      /> */}
      <DiscontinuationNotice />
    </>
  )
}

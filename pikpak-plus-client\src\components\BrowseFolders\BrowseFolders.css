ion-item::part(native) {
  border-color: #fff;
  border-width: 2px;
  border-radius: 15px;
}

.browse-list {
  padding-bottom: 6rem;
}

.ion-text {
  margin: 1rem 0.125rem 1rem 0.125rem;
  text-wrap: wrap;
  text-align: justify;
  color: var(--color-primary);
}

.thumbnail {
  margin: 0px 10px 0px 0px;
  height: 2rem;
  width: 2rem;
}
.thumbnail-img {
  object-fit: cover;
}

ion-thumbnail {
  --border-radius: 8px;
}

.player-wrapper {
  position: relative;
  padding-top: 56.25%; /* Player ratio: 100 / (1280 / 720) */
}

.react-player {
  position: absolute;
  top: 0;
  left: 0;
}


import json
from pikpak.pikpakapi import Pik<PERSON>ak<PERSON><PERSON>

def test():
    client = PikPakApi(
        username="",
        password="",
        
    )
    client.login()
    # client.refresh_access_token()
    # print(json.dumps(client.get_download_url("-gZnidrEXsMvo1")))
    print("=" * 30, end="\n\n")

    print(json.dumps(client.get_quota_info(), indent=4))
    print("=" * 30, end="\n\n")

if __name__ == "__main__":
    test()
import React, { useMemo } from 'react'
import { IonCard, IonCardContent, IonIcon, IonText, IonButton } from '@ionic/react'
import '../AuthCard/AuthCard.css'
import { warningOutline, rocket } from 'ionicons/icons'
// @ts-expect-error Description of why the @ts-expect-error is necessary
import { ReactComponent as Logo } from '../../../assets/pikpkak_plus.svg'

const DiscontinuationNotice: React.FC = () => {
  const MemoizedLogo = useMemo(() => <Logo />, [])

  return (
    <div className="backg">
      {MemoizedLogo}
      <IonCard color="light" className="custom-auth-container">
        <IonCardContent className="content-container">
          <div style={{ backgroundColor: '#fff3cd', border: '1px solid #ffeaa7', borderRadius: '8px', padding: '16px', marginBottom: '16px', textAlign: 'center' }}>
            <IonIcon icon={warningOutline} color="warning" size="large" />
            <IonText color="dark">
              <h3 style={{ margin: '8px 0', color: '#856404' }}>Service Discontinued</h3>
              <p style={{ margin: '8px 0' }}>PikPak-Plus discontinued July 2025. Thank you for 2 years of support!</p>
            </IonText>
          </div>

          <div style={{ backgroundColor: '#e7f3ff', border: '1px solid #b3d9ff', borderRadius: '8px', padding: '16px', textAlign: 'center' }}>
            <IonText color="primary">
              <h4 style={{ margin: '0 0 8px 0', color: '#0066cc' }}>Continue with PikPak</h4>
              <p style={{ margin: '0 0 12px 0', fontSize: '14px' }}>Use PikPak directly with our premium invitation!</p>
            </IonText>
            <IonButton fill="solid" color="primary" href="https://mypikpak.com/drive/activity/invited?invitation-code=47295398" target="_blank">
              <IonIcon icon={rocket} slot="start" />
              PikPak Invitation (Premium)
            </IonButton>
          </div>
        </IonCardContent>
      </IonCard>
    </div>
  )
}

export default DiscontinuationNotice

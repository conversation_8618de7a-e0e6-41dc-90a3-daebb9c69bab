import React, { useMemo } from 'react'
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonText,
} from '@ionic/react'
import '../AuthCard/AuthCard.css'
import { warningOutline } from 'ionicons/icons'
// @ts-expect-error Description of why the @ts-expect-error is necessary
import { ReactComponent as Logo } from '../../../assets/pikpkak_plus.svg'

const DiscontinuationNotice: React.FC = () => {
  const MemoizedLogo = useMemo(() => <Logo />, [])

  return (
    <div className="backg">
      {MemoizedLogo}

      {/* Main Discontinuation Notice */}
      <IonCard color="light" className="custom-auth-container">
        <IonCardHeader className="content-container">
          <IonCardTitle className="content-container-title">
            Service Discontinuation
            <IonIcon
              size="default"
              icon={warningOutline}
              color="warning"
            ></IonIcon>
          </IonCardTitle>
        </IonCardHeader>

        <IonCardContent className="content-container">
          <div
            style={{
              backgroundColor: '#fff3cd',
              border: '1px solid #ffeaa7',
              borderRadius: '8px',
              padding: '16px',
              marginBottom: '16px',
            }}
          >
            <IonText color="dark">
              <h3
                style={{
                  margin: '0 0 12px 0',
                  color: '#856404',
                  textAlign: 'center',
                }}
              >
                Service Discontinued
              </h3>
              <p
                style={{
                  margin: '0 0 12px 0',
                  lineHeight: '1.5',
                  textAlign: 'center',
                }}
              >
                PikPak-Plus service has been permanently discontinued as of July
                2025.
              </p>
              <p
                style={{
                  margin: '12px 0 0 0',
                  fontWeight: 'bold',
                  color: '#856404',
                  textAlign: 'center',
                }}
              >
                Thank you for your support over the past 2 years.
              </p>
            </IonText>
          </div>
        </IonCardContent>
      </IonCard>
    </div>
  )
}

export default DiscontinuationNotice

import React, { useMemo } from 'react'
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonIcon,
  IonText,
  IonButton,
} from '@ionic/react'
import '../AuthCard/AuthCard.css'
import { warningOutline, personCircleOutline } from 'ionicons/icons'
// @ts-expect-error Description of why the @ts-expect-error is necessary
import { ReactComponent as Logo } from '../../../assets/pikpkak_plus.svg'

const DiscontinuationNotice: React.FC = () => {
  const MemoizedLogo = useMemo(() => <Logo />, [])

  return (
    <div className="backg">
      {MemoizedLogo}
      
      {/* Main Discontinuation Notice */}
      <IonCard color="light" className="custom-auth-container">
        <IonCardHeader className="content-container">
          <IonCardTitle className="content-container-title">
            Service Discontinuation
            <IonIcon size="default" icon={warningOutline} color="warning"></IonIcon>
          </IonCardTitle>
        </IonCardHeader>

        <IonCardContent className="content-container">
          <div style={{ 
            backgroundColor: '#fff3cd', 
            border: '1px solid #ffeaa7', 
            borderRadius: '8px', 
            padding: '16px', 
            marginBottom: '16px'
          }}>
            <IonText color="dark">
              <h3 style={{ margin: '0 0 12px 0', color: '#856404' }}>
                Important Announcement: Discontinuation of PikPak-Plus Service
              </h3>
              <p style={{ margin: '0 0 12px 0', lineHeight: '1.5' }}>
                After 2 years of running PikPak-Plus, we regret to announce that the service will be 
                <strong> permanently discontinued effective July 30, 2025</strong>.
              </p>
              
              <h4 style={{ margin: '12px 0 8px 0', color: '#856404' }}>Why this decision?</h4>
              <ul style={{ margin: '0 0 12px 0', paddingLeft: '20px' }}>
                <li><strong>Unstable PikPak Infrastructure:</strong> Recent instability from PikPak's end has made it impossible for us to maintain reliable service.</li>
                <li><strong>Resource Constraints:</strong> Limited time due to personal commitments prevents us from dedicating necessary efforts.</li>
                <li><strong>High Operational Costs:</strong> Rising expenses (servers, domains, monitoring tools) make the service unsustainable without compromising quality.</li>
              </ul>

              <h4 style={{ margin: '12px 0 8px 0', color: '#856404' }}>Key Details:</h4>
              <ul style={{ margin: '0 0 12px 0', paddingLeft: '20px' }}>
                <li>⚠️ <strong>Service End Date:</strong> July 30, 2025 (All instances will close permanently)</li>
                <li>⚠️ <strong>Data Backup:</strong> Download all important data before this date. No access will be possible afterward</li>
                <li>⚠️ <strong>Strict Policy:</strong> No extensions/negotiations, No refunds (except specified compensations), All user data and logs will be permanently erased</li>
              </ul>

              <p style={{ margin: '12px 0 0 0', fontWeight: 'bold', color: '#856404' }}>
                Thank you for your incredible support, collaboration, and trust over the past 2 years. 
                Act now: Secure your data before July 30, 2025.
              </p>
            </IonText>
          </div>

          <IonButton
            shape="round"
            expand="full"
            color="medium"
            disabled
          >
            Login Disabled - Service Discontinued
          </IonButton>
        </IonCardContent>
      </IonCard>
    </div>
  )
}

export default DiscontinuationNotice

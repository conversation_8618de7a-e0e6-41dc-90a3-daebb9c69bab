'use client'

import { IonButton, IonIcon } from '@ionic/react'
import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'
import { moonOutline, sunnyOutline } from 'ionicons/icons'

export function ThemeSwitcher() {
  const [mounted, setMounted] = useState(false)
  const { theme, setTheme } = useTheme()

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    document.body.classList.toggle('dark', theme === 'dark')
  }, [theme])

  if (!mounted) return null

  const isDark = theme === 'dark'

  const handelDarkMode = () => {
    setTheme(isDark ? 'light' : 'dark')
    document.body.classList.toggle('dark', !isDark)
  }

  return (
    <IonButton slot="end" fill="clear" onClick={handelDarkMode}>
      <IonIcon
        color={isDark ? 'warning' : 'danger'}
        icon={isDark ? moonOutline : sunnyOutline}
        slot="end"
        size="icon-only"
      />
    </IonButton>
  )
}

const f=/(?:https:\/\/)?(?:player\.)?vimeo(?:\.com)?\/(?:video\/)?(\d+)(?:\?hash=(.*))?/,t=new Map,i=new Map;function g(n){const o=n.match(f);return{videoId:o==null?void 0:o[1],hash:o==null?void 0:o[2]}}async function b(n,o){if(t.has(n))return t.get(n);if(i.has(n))return i.get(n);const p=`https://vimeo.com/api/oembed.json?url=https://player.vimeo.com/video/${n}`,c=window.fetch(p,{mode:"cors",signal:o.signal}).then(e=>e.json()).then(e=>{var m,h;const l=/vimeocdn.com\/video\/(.*)?_/,s=(h=(m=e==null?void 0:e.thumbnail_url)==null?void 0:m.match(l))==null?void 0:h[1],u=s?`https://i.vimeocdn.com/video/${s}_1920x1080.webp`:"",r={title:(e==null?void 0:e.title)??"",duration:(e==null?void 0:e.duration)??0,poster:u,pro:e.account_type!=="basic"};return t.set(n,r),r}).finally(()=>i.delete(n));return i.set(n,c),c}export{b as getVimeoVideoInfo,g as resolveVimeoVideoId};

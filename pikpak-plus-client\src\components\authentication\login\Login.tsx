import { useState } from 'react'
import {
  deleteLocalStorage,
  makeRequest,
  setCookie,
  setEmailandDirectory,
  setPremiumStatus,
} from '../../../helpers/helpers'
import LoginCard from './LoginCard.tsx/LoginCard'
import { IonToast } from '@ionic/react'
import BlockUiLoader from '../../BlockUiLoader/BlockUiLoader'
import AuthButtons from '../AuthButtons/AuthButtons'
import { premium_status } from '../../../types/sharedTypes'

function Login() {
  const [showToast, setShowToast] = useState<{
    message: string
    color: string
  } | null>(null)
  const [signInLoading, setSignInLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(false)

  const handleSignInSuccess = (data, email) => {
    const { redirect, auth, dir, premium_status } = data

    if (
      !(premium_status as premium_status).email_exists ||
      (premium_status as premium_status).is_expired
    ) {
      setShowToast({
        message: 'You are not a premium user',
        color: 'danger',
      })
      setTimeout(() => {
        window.location.href = '/payment'
      }, 2000)
    } else {
      setCookie('auth', auth, 6) // Set the cookie to expire in 6 hours
      setEmailandDirectory(email, dir)
      setPremiumStatus(premium_status)
      fetchServers(redirect)
    }
  }

  async function signIn(email: string, password: string) {
    try {
      setSignInLoading(true)
      const response = await makeRequest('login', 'POST', { email, password })
      if (response.status !== 200) {
        setShowToast({
          message: response.data.error,
          color: 'danger',
        })
      } else {
        const data = response.data
        setShowToast({
          message: 'Sign-in successful',
          color: 'success',
        })
        handleSignInSuccess(data, email)
      }
    } catch (error) {
      setShowToast({
        message: `wrong password: ${error}`,
        color: 'danger',
      })
    } finally {
      setSignInLoading(false)
    }
  }

  const fetchServers = async (redirect) => {
    try {
      setFetchLoading(true)
      const response = await makeRequest('getServers', 'GET', {})
      const data = response.data
      localStorage.setItem('serverOptions', JSON.stringify(data))

      const values = Object.values(data)
      const firstElement = values[0] as any
      const selectedServer = localStorage.getItem('selectedServer')
      if (!selectedServer) {
        firstElement &&
          localStorage.setItem('selectedServer', firstElement.server_id)
      }
      window.location.href = redirect
    } catch (error) {
      deleteLocalStorage()
      setShowToast({
        message: 'Fetching server details failed, contact admin',
        color: 'danger',
      })
    } finally {
      setFetchLoading(false)
    }
  }

  return (
    <>
      <BlockUiLoader
        loading={signInLoading || fetchLoading}
        message={
          signInLoading
            ? 'Signing in, please wait'
            : 'Fetching server details, please wait'
        }
      >
        <AuthButtons />
        <LoginCard callbackFunc={signIn} />
      </BlockUiLoader>
      <IonToast
        isOpen={!!showToast}
        onDidDismiss={() => setShowToast(null)}
        message={showToast?.message}
        duration={3000}
        color={showToast?.color}
      />
    </>
  )
}

export default Login

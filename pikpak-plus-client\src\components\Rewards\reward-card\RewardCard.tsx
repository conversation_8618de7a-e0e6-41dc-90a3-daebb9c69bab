// import {
//   IonCard,
//   IonCardHeader,
//   IonCardTitle,
//   IonCardContent,
//   IonCardSubtitle,
//   IonButton,
//   IonIcon,
// } from '@ionic/react'
// import { RewardCardProps } from '../../../types/sharedTypes'

// export default function RewardCard({
//   noOfCoins,
//   BuyLink,
//   BuyText,
//   coinsImage,
// }: RewardCardProps) {
//   return (
//     <div style={{ width: '100%' }}>
//       <IonCard>
//         <IonCardHeader>
//           <IonCardTitle>
//             <IonIcon
//               src={coinsImage}
//               style={{ width: '50px', height: '50px' }}
//             />
//           </IonCardTitle>
//         </IonCardHeader>

//         <IonCardSubtitle color={'dark'}>
//           {noOfCoins} {'coins'}
//         </IonCardSubtitle>
//         <IonCardContent>
//           <IonButton
//             href={BuyLink}
//             target="_blank"
//             fill="solid"
//             color={'success'}
//             expand="block"
//           >
//             {BuyText}
//           </IonButton>
//         </IonCardContent>
//       </IonCard>
//     </div>
//   )
// }

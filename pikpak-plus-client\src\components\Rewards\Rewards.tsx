// import { useEffect, useState } from 'react'
// import { useLocation, useHistory } from 'react-router-dom'
// import { useLocalStorage } from '@rehooks/local-storage'
// import {
//   IonButton,
//   IonContent,
//   IonText,
//   IonImg,
//   IonIcon,
//   IonCard,
//   IonCardContent,
//   IonCardHeader,
//   IonCardTitle,
//   IonToast,
//   IonItemDivider,
// } from '@ionic/react'
// import {
//   getEmailandDirectory,
//   makeRequest,
//   writeToClipboard,
// } from '../../helpers/helpers'
// import BlockUiLoader from '../BlockUiLoader/BlockUiLoader'

// import { constructWhatsAppLink } from '../../helpers/helpers'
// import RewardsCard from '../../assets/coins-background.svg'
// import treasureBanner from '../../assets/treasure-background.svg'
// import CoinsBanner from '../AddURL/coins-banner/CoinsBanner'
// import { diamondOutline, cashOutline } from 'ionicons/icons'

// import './Rewards.css'
// import RewardCard from './reward-card/RewardCard'
// import { PURCHASE_COINS, WEBDAV } from '../../constants/constants'
// import HelperCard from '../HelperCard/HelperCard'
// import NoteComponent from '../note-component/RewardsTenNote'

// const Rewards = () => {
//   // eslint-disable-next-line @typescript-eslint/no-unused-vars
//   const [, setCoinCount] = useLocalStorage('coins')
//   const [email, setEmail] = useState<string | null>(null)
//   const [showToast, setShowToast] = useState<{
//     message: string
//     color: string
//   } | null>(null)
//   const [isRewardLinkAvailable, setIsRewardLinkAvailable] = useState(false)
//   const [rewardLink, setRewardLink] = useState<string | null>(null)
//   const [loading, setLoading] = useState(false)
//   const location = useLocation()
//   const history = useHistory()

//   const params = new URLSearchParams(location.search)
//   const id = params.get('id')

//   const webdavList = WEBDAV.map((item, index) => (
//     <div className="usefull-links" key={index}>
//       <IonIcon icon={diamondOutline} />
//       &nbsp;
//       <IonText color={'dark'} key={index}>
//         {item}
//       </IonText>
//     </div>
//   ))

//   const handleParamsDelete = () => {
//     params.delete('id')
//     history.replace({
//       pathname: location.pathname,
//       search: params.toString(),
//     })
//   }

//   const handleReward = async (id: string) => {
//     const local_email = localStorage.getItem('email')
//     console.log('id', id)
//     console.log('email', local_email)

//     setLoading(true)
//     try {
//       const response = await makeRequest('redeem-reward', 'POST', {
//         id,
//         email: local_email,
//       })
//       const newCoinCount = response.data.new_coin_count
//       setCoinCount(newCoinCount)
//       console.log(response.data.message)
//       setShowToast({
//         message: 'Reward redeemed successfully',
//         color: 'success',
//       })
//       // Remove the id parameter from the URL
//       handleParamsDelete()
//     } catch (error: any) {
//       setShowToast({
//         message: error.response?.data?.error,
//         color: 'danger',
//       })
//       console.error(error.response?.data?.error)
//       if (error.response?.data?.error === 'Reward already redeemed') {
//         handleParamsDelete()
//       }
//     } finally {
//       setLoading(false)
//     }
//   }

//   useEffect(() => {
//     const { email } = getEmailandDirectory()
//     setEmail(email)
//     if (id) {
//       handleReward(id)
//     }
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, [id, location.pathname, location.search]) // Only run when these dependencies change

//   const handleGenerateRewardLink = async () => {
//     setLoading(true)
//     try {
//       const response = await makeRequest('create-reward', 'POST', {
//         email,
//       })
//       setRewardLink(response.data.shortenedUrl)
//       setIsRewardLinkAvailable(true)
//       setShowToast({
//         message: 'Reward link generated successfully',
//         color: 'success',
//       })
//       writeToClipboard(response.data.shortenedUrl)
//     } catch (error: any) {
//       setShowToast({
//         message: error.response?.data?.error || error.message,
//         color: 'danger',
//       })
//     } finally {
//       setLoading(false)
//     }
//   }

//   const handleOpenRewardLink = () => {
//     if (rewardLink) {
//       window.open(rewardLink, '_blank')
//     } else {
//       setShowToast({
//         message: 'Reward link is not available',
//         color: 'warning',
//       })
//     }
//   }

//   const Purchase_Link = constructWhatsAppLink(
//     "I'm interested in Purchasing PikPak-Plus WebDav",
//   )

//   return (
//     <IonContent fullscreen={true}>
//       <BlockUiLoader loading={loading} message="Processing...">
//         <div style={{ margin: '1rem', paddingBottom: '5rem' }}>
//           <h2>
//             <IonText color={'tertiary'}>Pikpak-Plus Rewards</IonText>
//           </h2>
//           <div className="rewards-card">
//             <IonImg
//               src={Math.random() < 0.5 ? RewardsCard : treasureBanner}
//               alt="reward"
//               style={{ width: 500, height: 200 }}
//             />
//             <CoinsBanner showLink={false} />
//           </div>
//           <div className="rewards-button">
//             <IonButton
//               expand="block"
//               shape="round"
//               onClick={handleGenerateRewardLink}
//             >
//               Generate link
//             </IonButton>
//             <IonItemDivider />
//             {isRewardLinkAvailable && (
//               <IonButton
//                 expand="block"
//                 color={'success'}
//                 shape="round"
//                 onClick={handleOpenRewardLink}
//               >
//                 Open Link
//               </IonButton>
//             )}
//             <div style={{ textAlign: 'center', marginTop: '1rem' }}>
//               <IonText color={'tertiary'}> Purchase Coins </IonText>
//               <hr />

//               <div className="coins-rewards-cards">
//                 {PURCHASE_COINS.map((reward, index) => (
//                   <RewardCard key={index} {...reward} />
//                 ))}
//               </div>
//             </div>

//             <IonItemDivider />
//             <div style={{ textAlign: 'center', marginTop: '1rem' }}>
//               <IonText color={'danger'}> Purchase Premium </IonText>
//               <hr />
//             </div>

//             <HelperCard
//               cardTitle="Purchase [WEBDAV]"
//               cardSubtitle={webdavList}
//               cardSubTitleStyle={{
//                 display: 'flex',
//                 flexDirection: 'column',
//               }}
//               icon={diamondOutline}
//               titleColor="tertiary"
//               cardContent={
//                 <>
//                   <IonButton
//                     fill="outline"
//                     href={Purchase_Link}
//                     target="_blank"
//                     color={'tertiary'}
//                   >
//                     <IonIcon icon={cashOutline} />
//                     &nbsp;
//                     <IonText>
//                       <strong> Purchase 6$</strong>
//                     </IonText>
//                   </IonButton>
//                   <br />
//                   {/* <IonButton
//                     fill="outline"
//                     href={chatlink}
//                     target="_blank"
//                     color={'tertiary'}
//                   >
//                     <IonIcon icon={chatboxEllipsesOutline} />
//                   </IonButton> */}
//                 </>
//               }
//             />
//             <NoteComponent />

//             <IonCard color="light">
//               <IonCardHeader>
//                 <IonCardTitle>Guidance</IonCardTitle>
//               </IonCardHeader>

//               <IonCardContent>
//                 <p>Follow the steps below to get your rewards:</p>
//                 <ol>
//                   <li>
//                     Click on the "Generate link" button to generate a URL
//                     containing your unique referral ID.
//                   </li>
//                   <li>Open the generated URL and follow the steps.</li>
//                 </ol>
//                 <p>
//                   That's it! At the end of the process, you will be redirected
//                   to our site, and coins will be added to your account.
//                 </p>
//                 <p>
//                   <b>
//                     Note: Coins will be useful to:
//                     <ul>
//                       <li>Create tasks</li>
//                       <li>Download</li>
//                       <li>Play videos</li>
//                     </ul>
//                     1 coin = 1 action
//                   </b>
//                 </p>
//                 <br />
//                 <IonText color={'secondary'}>
//                   Thank you for your support!
//                 </IonText>
//               </IonCardContent>
//             </IonCard>
//           </div>

//           {/* <div style={{ marginTop: '2rem' }}>
//           <IonCard color="light">
//             <IonCardHeader>
//               <IonCardTitle>Guidance</IonCardTitle>
//             </IonCardHeader>

//             <IonCardContent>
            

//               <p>
//                 Coins will be useful to:
//                 <ul>
//                   <li>Create tasks</li>
//                   <li>Download</li>
//                   <li>Play videos</li>
//                 </ul>
//                 1 coin = 1 action
//               </p>
//               <IonText color={'secondary'}>Thank you for your support!</IonText>
//             </IonCardContent>
//           </IonCard>
//         </div> */}
//         </div>

//         <IonToast
//           isOpen={!!showToast}
//           onDidDismiss={() => setShowToast(null)}
//           message={showToast?.message}
//           duration={3000}
//           color={showToast?.color}
//         />
//       </BlockUiLoader>
//     </IonContent>
//   )
// }

// export default Rewards

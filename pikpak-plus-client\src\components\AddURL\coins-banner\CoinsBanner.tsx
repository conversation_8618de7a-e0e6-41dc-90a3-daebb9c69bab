// import { useHistory } from 'react-router-dom'
// import { arrowForwardSharp } from 'ionicons/icons'
// import { IonChip, IonAvatar, IonLabel, IonIcon } from '@ionic/react'
// import gameCoin from '../../../assets/coin-pikpak-plus.svg' // Adjust the path
// import useLocalStorage from '@rehooks/local-storage'

// const CoinsBanner = ({ showLink = true }) => {
//   const navigate = useHistory()
//   const [coinCount] = useLocalStorage('coins', 10)

//   const handleNavigate = () => {
//     navigate.push('/rewards', { replace: true })
//   }

//   return (
//     <IonChip
//       style={{ margin: '1rem 0', cursor: 'pointer' }}
//       onClick={handleNavigate}
//     >
//       <IonAvatar>
//         <img
//           alt="Silhouette of a person's head"
//           src={gameCoin}
//           style={{ objectFit: 'cover' }}
//         />
//       </IonAvatar>
//       <IonLabel>
//         Coins available: <strong>{coinCount}</strong>
//         {showLink && (
//           <>
//             <br />
//             <span
//               style={{
//                 color: '#007bff',
//                 textDecoration: 'underline',
//                 display: 'flex',
//                 alignItems: 'center',
//               }}
//             >
//               Click to earn more{' '}
//               <IonIcon
//                 icon={arrowForwardSharp}
//                 style={{ marginLeft: '0.5rem' }}
//               />
//             </span>
//           </>
//         )}
//       </IonLabel>
//     </IonChip>
//   )
// }

// export default CoinsBanner

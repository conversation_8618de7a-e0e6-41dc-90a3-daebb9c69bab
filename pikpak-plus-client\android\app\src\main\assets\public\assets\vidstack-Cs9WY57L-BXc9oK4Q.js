import{H as i,s as r,a as s}from"./BrowseFolders-B5zrTqBv.js";import"./index-YQZYyPkh.js";import"./CustomIonHeader-RqF7V-cv.js";import"./HelperCard-t_yvTUPv.js";class h extends i{constructor(t,e){super(t,e),this.$$PROVIDER_TYPE="AUDIO",r(()=>{this.airPlay=new s(this.media,e)},this.scope)}get type(){return"audio"}setup(){super.setup(),this.type==="audio"&&this.b.delegate.c("provider-setup",this)}get audio(){return this.a}}export{h as AudioProvider};

.custom-container {
  margin: 0rem 0.5rem 0rem 0.5rem;
}

.container-welcome {
  display: flex;
  padding: 1.25rem;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.email-welcome {
  font-size: xx-large;
}

.usefull-links {
  display: flex;
  margin-bottom: 0.275rem;
  align-items: center;
  text-align: justify;
}

a {
  color: unset;
  text-decoration: unset;
}

.text-flex-style {
  display: flex;
  align-items: center;
}

/* Media query for mobile devices */
@media only screen and (max-width: 600px) {
  .text-flex-style {
    flex-direction: column; /* Change direction to column */
    align-items: flex-start; /* Adjust alignment if needed */
  }
}

.github-btn-container {
  display: flex;
  justify-content: center;
  flex-direction: row;
  column-gap: 0.5rem;
  margin-top: 1rem;
}

.carousel-container{
  position: relative; 
  margin-bottom: 1.75rem;
  margin-right: 1.75rem;
  margin-left: 1.75rem;
}
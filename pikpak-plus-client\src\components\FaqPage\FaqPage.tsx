import { IonAccordionGroup, IonContent, IonText } from '@ionic/react'
import './FaqPage.css'
import AccordionGroup from '../AccordionGroup/AccordionGroup'

const faqList = [
  {
    title: 'What is PikPak Plus?',
    type: 'text',
    value: 'first',
    answer: `Unofficial implementation of PikPak cloud and it's services.<br/>
    pikpak-plus provides ability to host and share your PikPak [Premium] with your friends and family!
    `,
  },
  {
    title: 'How to create pikpak-plus new account?',
    type: 'video',
    value: 'second',
    link: 'https://www.youtube.com/embed/gnkqv3YTnGA',
  },
  {
    title: 'Benifits of pikpak-plus premium?',
    type: 'text',
    value: 'eleventh',
    answer: ` <h1 style="color: #333; font-size: 24px; font-family: Arial, sans-serif;">Purchase PikPak-plus Premium</h1>
    <p style="font-size: 18px; font-weight: bold; font-family: Arial, sans-serif;">
        Price: 5 USD ($) or 400 INR (₹)
    </p>
    <ul style="list-style-type: disc; margin-left: 20px; font-family: Arial, sans-serif;">
        <li>Dedicated 100GB space for 1 year</li>
        <li>Dedicated WebDAV username and password</li>
        <li>No file size limit</li>
        <li>Premium support</li>
    </ul>
    <small>
        See the <a href="https://pikpak-plus.com/faq" target="_blank" style="color: #1a73e8; text-decoration: none;">FAQ page</a> for how to use WebDAV.
    </small>
    <br/>
    <br/>
     <small>
        Service discontinued as of July 2025.
    </small>
    <br><p style="font-size: 14px; font-style: italic; color: #555;">
     NOTE: By purchasing this plan, you are directly helping us maintain and improve our website, allowing us to continue providing quality service to you and others 😊. Your support is invaluable, and we sincerely appreciate it.
    </p>

    `,
  },
  {
    title: 'How to use pikpak webdav with Nova video player[android]?',
    type: 'video',
    value: 'third',
    link: 'https://www.youtube.com/embed/o7I87uHANcQ',
  },
  {
    title: 'How to use pikpak webdav in windows [RaiDrive]?',
    type: 'video',
    value: 'fourth',
    link: 'https://www.youtube.com/embed/eJ2xr4H2cDA',
  },
  {
    title: 'In case of any problem with pikpak-plus',
    type: 'text',
    value: 'fifth',
    answer: `<strong>Clear browser cookies,
    then try again. </strong> else Please contact us through 
    <a href="https://github.com/bharathganji/pikpak-plus/issues">GitHub</a> or
    <a href="https://t.me/pikpak_plus">Telegram</a> or
    <a href="mailto:<EMAIL>">Mail</a> .`,
  },
  {
    title: 'Can i purchase WebDav from pikpak-plus',
    type: 'text',
    value: 'sixth',
    answer: `<strong>yes, you can  <a href="/payment">purchase</a>.</strong><br/>
    contact us through
    <a href="https://t.me/pikpak_plus">Telegram</a> or
    <a href="mailto:<EMAIL>">Mail</a>.
    <li>Note: currently webdav is readonly by pikpak</li>
    `,
  },
  {
    title: 'Anroid App Avialable?',
    type: 'text',
    value: 'seventh',
    answer: `<strong>yes..</strong><br/>
    download link
    <a href='https://github.com/bharathganji/pikpak-plus/raw/main/pikpak-plus-client/android/app/release/app-release.apk'>Download</a>.
    `,
  },
  {
    title: 'Is it harm to use pikpak-plus',
    type: 'text',
    value: 'eighth',
    answer: `<strong>NO..</strong>
    Nothing more than a free service.😉<br/>
    <h3>Do's and Dont's</h3>
    <li>pikpak-plus never store your login credentials.</li>
    <li>pikpak-plus never share your data and information.</li>
    <li>pikpak-plus never ask for money.</li>
    `,
  },
  {
    title: 'pikpak-plus monitoring service ',
    type: 'text',
    value: 'ninth',
    answer: `<a href="https://status.pikpak-plus.com" target='_blank'>
    Open Service Monitor
  </a>
    `,
  },
  {
    title: 'Why my content is missing from Folders?',
    type: 'text',
    value: 'tenth',
    answer: `
    <p style="font-size: 16px; color: #333;">
    As you know, <strong>PikPak-plus</strong> offers a <strong>free service</strong>, allowing users to store large amounts of data. However, we have noticed that many users are storing <strong>terabytes of data</strong> without accessing or using them for extended periods. This puts a strain on our resources and affects our ability to continue providing free services to everyone.
</p>

<p style="font-size: 16px; color: #333;">
    To manage our storage capacity, we will begin <strong>deleting any folders larger than 25 GB</strong> if we find our storage is nearing capacity and those folders have not been accessed for a long time. We kindly ask for your understanding and support in this matter.
</p>

<p style="font-size: 16px; color: #333;">
    You can help us maintain our service by purchasing a <strong>premium membership</strong>. Not only will this ensure your data is safe, but it will also support us in keeping PikPak-plus running smoothly for all users. We appreciate your cooperation and thank you for supporting our community.
</p>
    `,
  },
  // {
  //   title: 'About coins',
  //   type: 'text',
  //   value: 'tweleventh',
  //   answer: `<strong>Coins</strong> will be useful to:
  //                 <ul>
  //                   <li>Create tasks</li>
  //                   <li>Download</li>
  //                   <li>Play videos</li>
  //                 </ul>
  //                 1 coin = 1 action
  //                 `,
  // },
]
function FaqPage() {
  return (
    <>
      <IonContent fullscreen={true}>
        <div className="custom-container">
          <IonText color={'primary'}>
            <h1>Frequently Asked Questions</h1>
          </IonText>
          <IonAccordionGroup>
            {faqList.map((item, index) => (
              <AccordionGroup key={index} {...item} />
            ))}
          </IonAccordionGroup>
        </div>
      </IonContent>
    </>
  )
}

export default FaqPage

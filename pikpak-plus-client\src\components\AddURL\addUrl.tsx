import React, { useEffect, useMemo, useState } from 'react'
import {
  IonToast,
  IonContent,
  IonIcon,
  IonText,
  IonButton,
  IonSpinner,
  IonProgressBar,
  IonCol,
  IonGrid,
  IonRow,
} from '@ionic/react'
import {
  addSharp,
  informationCircleOutline,
  sparklesOutline,
  star,
  flash,
  stats<PERSON>hart,
  cloudOutline,
  calendarClearOutline,
  helpCircleOutline,
  arrowRedoOutline,
  logoWebComponent,
  copyOutline,
  walletOutline,
  // diamondOutline,
  // cashOutline,
  // logoUsd,
  // megaphoneOutline,
  // warningOutline,
  // arrowForwardCircleOutline,
  // chatboxEllipsesOutline,
} from 'ionicons/icons'
import './addUrlForm.css'
import CustomInput from '../CustomInput/CustomInput'
import {
  bytesToGB,
  bytesToTiB,
  calculateDriveInfo,
  // constructWhatsAppLink,
  getEmailandDirectory,
  getPremiumStatus,
  // getSelectedServer,
  // getServerExpireDate,
  makeRequest,
  writeToClipboard,
} from '../../helpers/helpers'
import GitHubButton from 'react-github-btn'
import CustomIonHeader from '../CustomIonHeader/CustomIonHeader'
import HelperCard from '../HelperCard/HelperCard'
import BlockUiLoader from '../BlockUiLoader/BlockUiLoader'
import {
  // ANNOUNCEMENT_OF_END,
  // WEBDAV,
  help,
  // oneDollarPlan,
  usefullLinks,
} from '../../constants/constants'
import { BaseResponseObjectType, premium_status } from '../../types/sharedTypes'
// import CarouselSlider from '../carousel-slider/carousel-slider'
// import CoinsBanner from './coins-banner/CoinsBanner'
// import useLocalStorage from '@rehooks/local-storage'
// import NoteComponent from '../note-component/RewardsTenNote'

const AddUrlForm: React.FC = () => {
  const [email, setEmail] = useState<string | null>(null)
  // const [coins, setCoinCount] = useLocalStorage<number>('coins')
  const [directory, setDirectory] = useState<string | null>(null)
  const [showToast, setShowToast] = useState<{
    message: string
    color: string
  } | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const [serverstats, setServerstats] = useState<BaseResponseObjectType | null>(
    null,
  )
  const [isLoadingStats, setIsLoadingStats] = useState(false)
  const premium_status: premium_status | null = useMemo(() => {
    const premiumStatusString = getPremiumStatus()
    return premiumStatusString ? JSON.parse(premiumStatusString) : null
  }, [])

  const webdav_path = '/' + email?.split('@')[0]

  useEffect(() => {
    const { email, dir } = getEmailandDirectory()
    setEmail(email)
    setDirectory(dir)
  }, [])

  const handleSubmit = async (text: string) => {
    // if (coins! < 1) {
    //   setShowToast({
    //     message: 'Oops! You need more coins to continue..',
    //     color: 'danger',
    //   })
    //   return
    // }

    setIsLoading(true)

    // Regular expressions for different link formats
    const magnetRegex = /magnet:\?xt=urn:btih:[a-zA-Z0-9]*/
    const twitterRegex = /https?:\/\/(www\.)?twitter\.com\/.*/
    const tiktokRegex = /https?:\/\/(www\.)?tiktok\.com\/.*/
    const facebookRegex = /https?:\/\/(www\.)?facebook\.com\/.*/

    // Split the text into an array of links and remove empty values and duplicates
    const links = Array.from(
      new Set(
        text
          .split('\n')
          .map((link) => link.trim())
          .filter((link) => link !== ''),
      ),
    )

    let isValidLinks = true

    // Validate all links
    for (let i = 0; i < links.length; i++) {
      const link = links[i]
      let isValid = false

      if (
        magnetRegex.test(link) ||
        twitterRegex.test(link) ||
        tiktokRegex.test(link) ||
        facebookRegex.test(link)
      ) {
        isValid = true
      }

      if (!isValid) {
        setShowToast({
          message: 'Invalid link format at link number ' + (i + 1),
          color: 'danger',
        })
        isValidLinks = false
        break // Exit the loop if any link is invalid
      }
    }

    if (isValidLinks) {
      // All links are valid, proceed to make API calls
      for (let i = 0; i < links.length; i++) {
        try {
          const response = await makeRequest('addURL', 'POST', {
            url: links[i],
            email: email,
            user_dir: directory,
          })
          const data = response.data.result

          if (data && data.upload_type === 'UPLOAD_TYPE_URL') {
            setShowToast({
              message: 'Task Created for link number ' + (i + 1),
              color: 'success',
            })
            // coins && setCoinCount(coins - 1)
          } else {
            setShowToast({
              message: 'Error adding task for link number ' + (i + 1),
              color: 'danger',
            })
          }
        } catch (error: any) {
          console.error('Error adding task for link number', i + 1, ':', error)
          // setShowToast({
          //   message:
          //     'Error adding task for link number ' +
          //     (i + 1) +
          //     ' : ' +
          //     error.response?.data?.error,
          //   color: 'danger',
          // })
          setShowToast({
            message: error.response?.data?.error,
            color: 'danger',
          })
        }
      }
    }

    setIsLoading(false)
  }

  // const renderCardList = (items: any[], Icon: any) => {
  //   return items.map((item, index) => (
  //     <div className="usefull-links" key={index}>
  //       <IonIcon color={'dark'} icon={Icon} />
  //       &nbsp;
  //       <IonText color={'dark'} key={index}>
  //         {item}
  //       </IonText>
  //     </div>
  //   ))
  // }

  // const chatlink = constructWhatsAppLink(
  //   "I'm interested in PikPak-Plus WebDav, need more info",
  // )
  // const Purchase_Link = constructWhatsAppLink(
  //   "I'm interested in Purchasing PikPak-Plus WebDav",
  // )

  const usefullLinksList = usefullLinks.map((item, index) => (
    <div className="usefull-links" key={index}>
      <IonIcon color={'dark'} icon={item.icon} />
      <IonText key={index} color={'dark'}>
        <a href={item?.value} rel="noopener noreferrer" target="_blank">
          &nbsp; {item?.link}
        </a>
      </IonText>
    </div>
  ))

  const helpList = help.map((item, index) => (
    <div className="usefull-links" key={index}>
      <IonText color={'dark'} key={index}>
        <IonIcon icon={star} /> {item}
      </IonText>
    </div>
  ))

  const colors = [
    'success',
    'tertiary',
    'primary',
    'secondary',
    'warning',
    'danger',
  ]
  const randomColor = colors[Math.floor(Math.random() * colors.length)]

  const ProgressBar = ({ value, size }: { value: number; size: number }) => (
    <>
      <IonProgressBar color={'secondary'} value={value / size} />
      <IonText>
        {value.toFixed(2)} GB / {size} GB
      </IonText>
    </>
  )

  const quotaDetails = (
    <>
      {isLoadingStats && serverstats ? (
        <IonSpinner name="lines"></IonSpinner>
      ) : (
        <>
          <IonText color={'dark'}>Cloud Download Traffic 40 TB / Month</IonText>
          <ProgressBar
            value={bytesToGB(serverstats?.offline?.size ?? 0)}
            size={40000}
          />
          <br />
          <IonText color={'dark'}>Downstream Traffic 4 TB / Month</IonText>
          <ProgressBar
            value={bytesToGB(serverstats?.download?.size ?? 0)}
            size={4000}
          />
        </>
      )}
    </>
  )

  const fetchServerstats = async () => {
    try {
      setIsLoadingStats(true)
      const response = await makeRequest('serverstats', 'POST', {})
      const data = response.data
      setServerstats(data)
    } catch (error: any) {
      console.error('Error:', error)
      setShowToast({
        message:
          'Error fetching server stats' + ' : ' + error.response?.data?.error,
        color: 'danger',
      })
    } finally {
      setIsLoadingStats(false)
    }
  }

  return (
    <>
      <CustomIonHeader title="Create Cloud Task" />
      <IonContent fullscreen={true}>
        <BlockUiLoader loading={isLoading}>
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              paddingBottom: '6rem',
            }}
          >
            <div className="custom-container">
              <div className="container-welcome">
                <span className="email-welcome">
                  <IonText>
                    <span>
                      Welcome..
                      <IonIcon
                        color={randomColor}
                        size="default"
                        icon={sparklesOutline}
                      />
                    </span>
                  </IonText>
                </span>
                {email}
                {/* <CoinsBanner /> */}
                <IonText className="text-flex-style">
                  <span>
                    <IonIcon color={'dark'} icon={cloudOutline} /> &nbsp;
                    {bytesToTiB(calculateDriveInfo()?.available || 0) +
                      ' / ' +
                      bytesToTiB(calculateDriveInfo()?.limit || 0)}
                    &nbsp;
                    {'used'} &nbsp;
                  </span>
                  {/* <span>
                    <IonIcon color={'dark'} icon={calendarClearOutline} />
                    &nbsp;
                    {'Expiry: ' +
                      (getServerExpireDate(getSelectedServer()) ||
                        'Contact Admin')}
                  </span> */}
                  <span>
                    <IonIcon color={'dark'} icon={calendarClearOutline} />
                    &nbsp;
                    {'Expiry: ' +
                      (new Date(
                        premium_status?.premium_info.expiry_date || 0,
                      ).toDateString() || 'Contact Admin')}
                  </span>
                  <span>
                    <IonIcon color={'dark'} icon={walletOutline} />
                    &nbsp;
                    {'Plan: ' +
                      `$${premium_status?.premium_info.amount || '0.00'} - ${
                        (premium_status?.premium_info.amount || 0) * 100
                      }GB`}
                  </span>
                </IonText>
                <div className="github-btn-container">
                  <GitHubButton
                    href="https://github.com/sponsors/bharathganji"
                    data-color-scheme="no-preference: light; light: light; dark: dark;"
                    data-icon="octicon-heart"
                    data-size="large"
                    aria-label="Sponsor @bharathganji on GitHub"
                  >
                    Sponsor
                  </GitHubButton>
                  <GitHubButton
                    href="https://github.com/bharathganji/pikpak-plus"
                    data-color-scheme="no-preference: light; light: light; dark: dark;"
                    data-icon="octicon-star"
                    data-size="large"
                    data-show-count="true"
                    aria-label="Star bharathganji/pikpak-plus on GitHub"
                  >
                    Star
                  </GitHubButton>
                </div>
              </div>
              {/* <div className="carousel-container">
                <CarouselSlider>
                  <div>
                    <HelperCard
                      cardTitle={
                        <IonText
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          <b>1</b>
                          <IonIcon icon={logoUsd} size="small" />
                          &nbsp;
                          <b>Basic Plan</b>(100gb)
                        </IonText>
                      }
                      cardSubtitle={renderCardList(
                        oneDollarPlan,
                        diamondOutline,
                      )}
                      cardSubTitleStyle={{
                        display: 'flex',
                        flexDirection: 'column',
                        textAlign: 'justify',
                      }}
                      titleColor="success"
                      cardContent={
                        <IonButton
                          fill="outline"
                          href={'/payment'}
                          target="_blank"
                          color={'tertiary'}
                        >
                          <IonIcon icon={cashOutline} />
                          &nbsp;
                          <IonText>
                            <strong>Purchase 1$</strong>
                          </IonText>
                        </IonButton>
                      }
                    />
                  </div>
                  <div>
                    <HelperCard
                      cardTitle="PRO PLAN (500gb)"
                      cardSubtitle={renderCardList(WEBDAV, diamondOutline)}
                      cardSubTitleStyle={{
                        display: 'flex',
                        flexDirection: 'column',
                        textAlign: 'justify',
                      }}
                      icon={diamondOutline}
                      titleColor="warning"
                      cardContent={
                        <>
                          <IonButton
                            fill="outline"
                            href={'/payment'}
                            target="_blank"
                            color={'tertiary'}
                          >
                            <IonIcon icon={cashOutline} />
                            &nbsp;
                            <IonText>
                              <strong> Purchase 5$</strong>
                            </IonText>
                          </IonButton>
                        </>
                      }
                    />
                  </div>
                  <div>
                    <HelperCard
                      cardTitle="Anouncement"
                      cardSubtitle={renderCardList(
                        ANNOUNCEMENT_OF_END,
                        warningOutline,
                      )}
                      cardSubTitleStyle={{
                        display: 'flex',
                        flexDirection: 'column',
                        textAlign: 'justify',
                      }}
                      icon={megaphoneOutline}
                      titleColor="danger"
                      cardContent={
                        <IonButton
                          fill="outline"
                          href={'/faq'}
                          target="_blank"
                          color={'tertiary'}
                        >
                          <IonText>
                            <strong> FAQ </strong>
                          </IonText>
                          &nbsp;
                          <IonIcon icon={arrowForwardCircleOutline} />
                        </IonButton>
                      }
                    />
                  </div>
                </CarouselSlider>
              </div> */}
              <CustomInput
                handleSubmit={handleSubmit}
                inputStyle={{
                  minHeight: '184px',
                }}
                buttonText="Create Task"
                onSubmitClearInput={true}
                icon={addSharp}
                customPlaceholder={`Supported link formats:
- Magnet URI (magnet:?xt=urn:btih)
- X(Twitter)
- TikTok
- Facebook      

Multiple links can be added at once by line break.`}
              />
            </div>
            <div className="container">
              {/* <NoteComponent /> */}
              {/* <HelperCard
                cardTitle="Purchase [WEBDAV]"
                cardSubtitle={renderCardList(WEBDAV, diamondOutline)}
                cardSubTitleStyle={{
                  display: 'flex',
                  flexDirection: 'column',
                  textAlign: 'justify',
                }}
                icon={diamondOutline}
                titleColor="danger"
                cardContent={
                  <>
                    <IonButton
                      fill="outline"
                      href={'/payment'}
                      target="_blank"
                      color={'tertiary'}
                    >
                      <IonIcon icon={cashOutline} />
                      &nbsp;
                      <IonText>
                        <strong> Purchase 5$</strong>
                      </IonText>
                    </IonButton>
                  </>
                }
              /> */}
              <HelperCard
                cardTitle="Helper Card"
                cardSubtitle={helpList}
                cardSubTitleStyle={{
                  display: 'flex',
                  flexDirection: 'column',
                  textAlign: 'justify',
                }}
                icon={informationCircleOutline}
                titleColor="primary"
              />
              <HelperCard
                cardTitle="Useful Links"
                cardSubtitle={usefullLinksList}
                cardSubTitleStyle={{
                  display: 'flex',
                  flexDirection: 'column',
                  textAlign: 'justify',
                }}
                icon={flash}
                titleColor="success"
              />
              <HelperCard
                cardTitle="WebDAV"
                titleColor="primary"
                icon={logoWebComponent}
                cardContent={
                  <IonGrid>
                    <IonRow>
                      <IonCol>
                        <IonText color={'dark'}>username</IonText>
                      </IonCol>
                      <IonCol>
                        <IonText color={'dark'}>passowrd</IonText>
                      </IonCol>
                      <IonCol>
                        <IonText color={'dark'}>action</IonText>
                      </IonCol>
                    </IonRow>
                    <IonRow>
                      <IonCol>
                        <IonText color={'dark'}>
                          {premium_status?.premium_info.webdav_username}
                        </IonText>
                      </IonCol>
                      <IonCol>
                        <IonText color={'dark'}>
                          {premium_status?.premium_info.webdav_password}
                        </IonText>
                      </IonCol>
                      <IonCol>
                        <IonButton
                          fill="clear"
                          size="small"
                          onClick={() =>
                            writeToClipboard(
                              `
username : ${premium_status?.premium_info.webdav_username}
password : ${premium_status?.premium_info.webdav_password}
path: ${webdav_path}
endpoint : dav.mypikpak.com`,
                            )
                          }
                        >
                          <IonIcon
                            slot="icon-only"
                            icon={copyOutline}
                          ></IonIcon>
                        </IonButton>
                      </IonCol>
                    </IonRow>
                  </IonGrid>
                }
              />
              <HelperCard
                cardTitle="Transfer Quota Details"
                titleColor="tertiary"
                icon={statsChart}
                cardContent={
                  serverstats !== null ? (
                    quotaDetails
                  ) : (
                    <IonButton fill="outline" onClick={fetchServerstats}>
                      Load stats . . .
                    </IonButton>
                  )
                }
              />
              <HelperCard
                cardTitle="Frequently Asked Questions"
                titleColor="tertiary"
                icon={helpCircleOutline}
                cardContent={
                  <IonButton fill="outline" routerLink="/faq">
                    FAQ PAGE &nbsp; <IonIcon icon={arrowRedoOutline}></IonIcon>
                  </IonButton>
                }
              />
              <div
                style={{
                  textAlign: 'center',
                  fontSize: '0.75rem',
                  marginTop: '1rem',
                }}
              >
                <h3>Thank you for your support!</h3>
                <div data-v-e2a4e784="">
                  <strong data-v-e2a4e784="">PikPak-plus Declaration: </strong>{' '}
                  The url size and other information are provided by the third
                  party website{' '}
                  <a
                    data-v-e2a4e784=""
                    style={{ color: '#1a73e8', textDecoration: 'none' }}
                    href="https://whatslink.info"
                    target="_blank"
                    data-new_landing_page_click_item="whatslink"
                  >
                    whatslink.info
                  </a>
                  , only for you to check the content of the links in advance.
                  files greater than 25GB are not allowed, for free users.
                </div>
              </div>
            </div>

            <IonToast
              isOpen={!!showToast}
              onDidDismiss={() => setShowToast(null)}
              message={showToast?.message}
              duration={3000}
              color={showToast?.color}
            />
          </div>
        </BlockUiLoader>
      </IonContent>
    </>
  )
}

export default AddUrlForm

{"name": "pikpak-plus", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "scan": "next dev & npx react-scan@latest localhost:3001", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "ionic:build": "npm run build", "ionic:serve": "npm run dev"}, "dependencies": {"@availity/block-ui": "1.1.5", "@capacitor/android": "6.1.2", "@capacitor/app": "6.0.1", "@capacitor/cli": "6.1.2", "@capacitor/clipboard": "6.0.1", "@capacitor/core": "6.1.2", "@capacitor/haptics": "6.0.1", "@capacitor/keyboard": "6.0.2", "@capacitor/share": "6.0.2", "@capacitor/status-bar": "6.0.1", "@formkit/auto-animate": "0.8.2", "@ionic/react": "7.8.6", "@ionic/react-router": "7.8.6", "@million/lint": "^1.0.14", "@nabla/vite-plugin-eslint": "2.0.4", "@rehooks/local-storage": "^2.4.5", "@vidstack/react": "1.11.30", "ag-grid-community": "32.0.2", "ag-grid-react": "32.0.2", "axios": "1.7.8", "bootstrap": "5.3.3", "create-vite": "5.3.0", "dotenv": "16.4.5", "next-themes": "^0.4.4", "react": "18.3.1", "react-bootstrap": "2.10.4", "react-dom": "18.3.1", "react-github-btn": "^1.4.0", "react-loader-spinner": "^6.1.6", "react-photo-view": "1.2.6", "react-router-dom": "^5.3.4", "react-slick": "^0.30.2", "slick-carousel": "^1.8.1"}, "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@svgr/rollup": "^8.1.0", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/react-helmet": "^6.1.11", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "7.16.1", "@typescript-eslint/parser": "7.16.1", "@vitejs/plugin-react": "4.3.1", "eslint": "8.57.0", "eslint-plugin-react-hooks": "4.6.2", "eslint-plugin-react-refresh": "0.4.8", "semantic-release": "23.1.1", "sharp": "0.33.4", "svgo": "3.3.2", "typescript": "5.5.3", "vite": "^5.4.10", "vite-plugin-image-optimizer": "1.1.8", "vite-plugin-optimize-css-modules": "1.1.0", "vite-plugin-pages": "0.32.3", "vite-plugin-pages-sitemap": "1.7.1", "vite-plugin-pwa": "0.21.0", "vite-plugin-sitemap": "0.7.1", "vite-plugin-webfont-dl": "3.10.2"}, "proxy": "http://127.0.0.1:5000/"}
import DiscontinuationNotice from '../DiscontinuationNotice'

// eslint-disable-next-line @typescript-eslint/no-unused-vars, no-empty-pattern
export default function LoginCard({ }: { callbackFunc: any }) {
  return (
    <>
      {/* <AuthCard
        titleHeading="Login"
        callbackFunc={callbackFunc}
        nextTitle={{ text: 'Sign Up', redirect: '/signup' }}
      /> */}
      <DiscontinuationNotice />
    
    </>
  )
}

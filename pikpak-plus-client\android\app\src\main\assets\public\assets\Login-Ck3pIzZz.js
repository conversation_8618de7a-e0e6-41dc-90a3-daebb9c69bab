import{j as t,t as d,B as p,E as x,L as g,aD as h,aE as v,aF as j}from"./index-YQZYyPkh.js";import{A as y,a as E}from"./AuthButtons-axJc_ndf.js";import"./index-BLf7waug.js";function L({callbackFunc:s}){return t.jsx(t.Fragment,{children:t.jsx(y,{titleHeading:"Login",callbackFunc:s,nextTitle:{text:"Sign Up",redirect:"/signup"}})})}function k(){const[s,n]=d.useState(null),[u,c]=d.useState(!1),m=(a,o)=>{const{redirect:e,auth:r,dir:i}=a;h("auth",r,2),v(o,i),S(e)};async function f(a,o){try{c(!0);const e=await g("login","POST",{email:a,password:o});if(e.status!==200){const r=e.data;n({message:r.error,color:"danger"})}else{const r=e.data;n({message:"Sign-in successful",color:"success"}),m(r,a)}}catch(e){n({message:`'Error during sign-in:', ${e}`,color:"danger"})}finally{c(!1)}}const S=async a=>{try{c(!0);const e=(await g("getServers","GET",{})).data;localStorage.setItem("serverOptions",JSON.stringify(e));const i=Object.values(e)[0],l=localStorage.getItem("selectedServer");(l===null||l==="")&&i&&localStorage.setItem("selectedServer",i.server_id),window.location.href=a}catch{j(),n({message:"fetching server details failed, contact admin",color:"danger"})}finally{c(!1)}};return t.jsxs(t.Fragment,{children:[t.jsxs(p,{loading:u,children:[t.jsx(E,{}),t.jsx(L,{callbackFunc:f})]}),t.jsx(x,{isOpen:!!s,onDidDismiss:()=>n(null),message:s==null?void 0:s.message,duration:3e3,color:s==null?void 0:s.color})]})}export{k as default};

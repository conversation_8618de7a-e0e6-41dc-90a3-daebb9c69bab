//  import {
//   IonCard,
//   IonCardHeader,
//   IonCardSubtitle,
//   IonCardTitle,
//   IonCardContent,
//   IonIcon,
// } from '@ionic/react'
// import { checkmarkCircleOutline, starOutline } from 'ionicons/icons'

// const NoteComponent = () => {
//   return (
//     <IonCard>
//       <IonCardHeader>
//         <IonCardTitle>Unlock Today's Unlimited Actions</IonCardTitle>
//         <IonCardSubtitle>
//           Earn 10 Coins Today for Unlimited Benefits!
//         </IonCardSubtitle>
//       </IonCardHeader>

//       <IonCardContent>
//         <p>
//           <IonIcon icon={checkmarkCircleOutline} color="success" /> Complete
//           tasks to earn coins.
//         </p>
//         <p>
//           <IonIcon icon={starOutline} color="warning" /> After earning 10 coins
//           today, you will enjoy unlimited actions (download, create tasks, play online etc...) for the rest of the day!
//         </p>
//         <br /><p>
//           <i>
//             Keep up the great work and explore all the features available today.
//           </i>
//         </p>
//       </IonCardContent>
//     </IonCard>
//   )
// }

// export default NoteComponent

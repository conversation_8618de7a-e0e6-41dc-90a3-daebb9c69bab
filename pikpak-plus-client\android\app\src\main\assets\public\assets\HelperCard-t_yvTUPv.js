import{j as s,aG as o,aH as t,aI as c,b as x,aK as j,aL as I}from"./index-YQZYyPkh.js";function p({cardTitle:e,cardStyle:r,titleColor:n,cardSubtitle:l,cardSubTitleStyle:d,cardContent:a,icon:i}){return s.jsx("div",{className:"helper-card",children:s.jsxs(o,{style:r,children:[s.jsxs(t,{children:[s.jsxs(c,{color:n,style:{display:"flex",gap:5,alignItems:"center"},children:[e,s.jsx(x,{icon:i})]}),s.jsx(j,{style:d,children:l})]}),a&&s.jsx(I,{children:a})]})})}export{p as H};

import { useState } from 'react'
import {
  makeRequest,
  setCookie,
  setEmailandDirectory,
  setPremiumStatus,
} from '../../../helpers/helpers'
import TempLoginCard from './TempLoginCard'
import { IonToast } from '@ionic/react'
import BlockUiLoader from '../../BlockUiLoader/BlockUiLoader'
import AuthButtons from '../AuthButtons/AuthButtons'
import { premium_status } from '../../../types/sharedTypes'

function TempLogin() {
  const [showToast, setShowToast] = useState<{
    message: string
    color: string
  } | null>(null)
  const [signInLoading, setSignInLoading] = useState(false)
  const [fetchLoading, setFetchLoading] = useState(false)

  async function signIn(email: string, password: string) {
    try {
      setSignInLoading(true)
      const response = await makeRequest('signin', 'POST', {
        email: email,
        password: password,
      })

      if (response.status !== 200) {
        const errorData = response.data
        setShowToast({
          message: errorData.error,
          color: 'danger',
        })
      } else {
        const successData = await response.data
        setCookie('authToken', successData.token, 30)
        setEmailandDirectory(email, successData.directory)
        await fetchServerDetails()
      }
    } catch (error) {
      setShowToast({
        message: error as any,
        color: 'danger',
      })
    } finally {
      setSignInLoading(false)
    }
  }

  async function fetchServerDetails() {
    try {
      setFetchLoading(true)
      const response = await makeRequest('server-details', 'GET')

      if (response.status !== 200) {
        const errorData = response.data
        setShowToast({
          message: errorData.error,
          color: 'danger',
        })
      } else {
        const successData = await response.data
        setPremiumStatus(successData.premium_status as premium_status)
        window.location.href = '/create'
      }
    } catch (error) {
      setShowToast({
        message: error as any,
        color: 'danger',
      })
    } finally {
      setFetchLoading(false)
    }
  }

  return (
    <>
      <BlockUiLoader
        loading={signInLoading || fetchLoading}
        message={
          signInLoading
            ? 'Signing in, please wait'
            : 'Fetching server details, please wait'
        }
      >
        <AuthButtons />
        <TempLoginCard callbackFunc={signIn} />
      </BlockUiLoader>
      <IonToast
        isOpen={!!showToast}
        onDidDismiss={() => setShowToast(null)}
        message={showToast?.message}
        duration={3000}
        color={showToast?.color}
      />
    </>
  )
}

export default TempLogin

import React, { useState } from 'react'
import {
  IonList,
  IonItem,
  IonIcon,
  IonAlert,
  IonToast,
  isPlatform,
} from '@ionic/react'
import {
  shareSocialOutline,
  downloadOutline,
  copyOutline,
  trashBinOutline,
  playOutline,
} from 'ionicons/icons'
import { DownloadResponse, FileItem } from '../../../types/sharedTypes'
import {
  formatFileSize,
  getEmailandDirectory,
  makeRequest,
  writeToClipboard,
} from '../../../helpers/helpers'
import './ModalOptions.css'
import { MAX_PLAY_SIZE_LIMIT_IN_BYTES } from '../../../constants/constants'
import { Share } from '@capacitor/share'
// import useLocalStorage from '@rehooks/local-storage'

const ItemWithIcon: React.FC<ItemWithIconProps> = React.memo(
  ({ color, icon, onClick, text }) => (
    <IonItem button={true} detail={false} onClick={onClick}>
      <IonIcon slot="start" color={color} icon={icon} />
      {text}
    </IonItem>
  ),
)

interface ItemWithIconProps {
  color: string
  icon: string
  onClick: () => void
  text: string
}

interface ModalOptionsProps {
  item: FileItem | null
  setShowModal: (value: boolean) => void
  setIsLoading: (value: boolean) => void
  setShowVideoPlayer?: (value: boolean) => void
  setVideoDetails?: (value: any) => void
  scrollToTop?: () => void
  handleDeleteItem?: (itemId: string) => void
}

const ModalOptions: React.FC<ModalOptionsProps> = ({
  item,
  setShowModal,
  setIsLoading,
  setShowVideoPlayer,
  setVideoDetails,
  scrollToTop,
  handleDeleteItem,
}) => {
  const fileName = item?.name
  const fileSize = item?.size
  const [showAlert, setShowAlert] = useState(false)
  const [actionCode, setActionCode] = useState('')
  const [downloadData, setDownloadData] = useState<DownloadResponse | null>(
    null,
  )
  const [showToast, setShowToast] = useState<{
    message: string
    color: string
  } | null>(null)
  // const [coins, setCoinCount] = useLocalStorage<number>('coins')
  const actionDetails: Record<
    string,
    { header: string; message: string; icon: string; color: string }
  > = {
    share: {
      color: 'success',
      header: 'Share',
      message: 'Do you want to proceed with sharing?',
      icon: shareSocialOutline,
    },
    play: {
      color: 'secondary',
      header: 'Play',
      message: 'Do you want to proceed with playing?',
      icon: playOutline,
    },
    playInAnotherApp: {
      color: 'secondary',
      header: 'Play in Another App',
      message: 'Do you want to proceed with playing in another app?',
      icon: playOutline,
    },
    copyDownloadLink: {
      color: 'tertiary',
      header: 'Copy Download/Stream Link',
      message: `Are you sure you want to copy the download link for: ${fileName}?`,
      icon: copyOutline,
    },
    copyFileName: {
      color: 'tertiary',
      header: 'Copy File Name',
      message: `Are you sure you want to copy the file name: ${fileName}?`,
      icon: copyOutline,
    },
    download: {
      color: 'warning',
      header: 'Download to Device',
      message:
        'This will initiate the download process. Do you want to proceed?',
      icon: downloadOutline,
    },
    delete: {
      color: 'danger',
      header: 'Delete',
      message: 'This will delete the item. Are you sure you want to proceed?',
      icon: trashBinOutline,
    },
  }

  const { email } = getEmailandDirectory()

  const handleCopyFileName = async (fileName: string): Promise<any> => {
    writeToClipboard(fileName)
    setShowToast({
      message: 'File name - copied to clipboard',
      color: 'success',
    })
  }

  const copyValuesSequentially = async (valuesToCopy: string[]) => {
    for (const value of valuesToCopy) {
      await writeToClipboard(value)
      // Introduce a delay (e.g., 500 milliseconds) between operations
      await new Promise((resolve) => setTimeout(resolve, 500))
    }
  }

  const fetchDataIfNeeded = async (itemId: string, action: string) => {
    if (downloadData?.id === itemId) {
      return downloadData
    } else {
      const response = await makeRequest('download', 'POST', {
        email: email,
        id: itemId,
        action: action,
      })
      const data = response.data
      setDownloadData(data)
      return data
    }
  }

  const handlePlay = async (itemId: string): Promise<any> => {
   // if (coins! < 1) {
    //   setShowToast({
    //     message: 'Oops! You need more coins to continue..',
    //     color: 'danger',
    //   })
    //   return
    // }
    const maxLimit = parseInt(
      import.meta.env.VITE_MAX_PLAY_SIZE_LIMIT_IN_BYTES ||
        MAX_PLAY_SIZE_LIMIT_IN_BYTES,
    )

    if (maxLimit < parseInt(fileSize as any)) {
      setShowToast({
        message: 'File too large to play, limit ' + formatFileSize(maxLimit),
        color: 'danger',
      })
      return
    }
    try {
      setIsLoading(true)
      const data = await fetchDataIfNeeded(itemId, 'play')
      const downloadLink = data?.web_content_link
      const thumbnailLink = data?.thumbnail_link
      const downloadName = data?.name
      const videoType = data?.mime_type

      setVideoDetails &&
        setVideoDetails({
          videoUrl: downloadLink,
          thumbnailImg: thumbnailLink,
          videoTitle: downloadName,
          videoType: videoType,
        })
      setShowVideoPlayer && setShowVideoPlayer(true)
      scrollToTop && scrollToTop()
      // coins && setCoinCount(coins - 1)
    } catch (error: any) {
      setShowToast({
        message:
          'Play failed, try again later' + ' : ' + error.response?.data?.error,
        color: 'danger',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handlePlayInAnotherApp = async (itemId: string): Promise<any> => {
   // if (coins! < 1) {
    //   setShowToast({
    //     message: 'Oops! You need more coins to continue..',
    //     color: 'danger',
    //   })
    //   return
    // }
    try {
      setIsLoading(true)
      const data = await fetchDataIfNeeded(itemId, 'play')
      const downloadLink = data?.web_content_link

      const downloadName = data?.name

      writeToClipboard(downloadLink)

      await Share.share({
        title: downloadName,
        url: downloadLink,
        dialogTitle: 'Select Video Player',
      })
      // coins && setCoinCount(coins - 1)
    } catch (error: any) {
      setShowToast({
        message:
          error + ', Use Android App' + ' : ' + error.response?.data?.error,
        color: 'danger',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleActionWithDownload = async (
    itemId: string,
    actionLogic: (downloadLink: string, downloadName: string) => Promise<void>,
  ) => {
    try {
      setIsLoading(true)

      const data = await fetchDataIfNeeded(itemId, 'download')
      const downloadLink = data?.web_content_link
      const downloadName = data?.name

      if (downloadLink) {
        await actionLogic(downloadLink, downloadName)
        setShowToast({
          message: 'completed',
          color: 'success',
        })
      } else {
        console.error('Download link is not available')
      }
    } catch (error: any) {
      console.error(
        'Error handling action:',
        +' : ' + error.response?.data?.error,
      )
      setShowToast({
        message: error.response?.data?.error,
        color: 'danger',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCopyDownloadLink = async (itemId: string): Promise<any> => {
   // if (coins! < 1) {
    //   setShowToast({
    //     message: 'Oops! You need more coins to continue..',
    //     color: 'danger',
    //   })
    //   return
    // }
    const actionLogic = async (downloadLink: string, downloadName: string) => {
      // Replace this with your specific logic for copying to clipboard
      const valuesToCopy: (string | undefined)[] = [downloadName, downloadLink]
      const filteredValuesToCopy: string[] = valuesToCopy.filter(
        (value): value is string => value !== undefined,
      )
      // coins && setCoinCount(coins - 1)

      await copyValuesSequentially(filteredValuesToCopy)
    }

    await handleActionWithDownload(itemId, actionLogic)
  }

  const handleDownloadToDevice = async (itemId: string): Promise<any> => {
   // if (coins! < 1) {
    //   setShowToast({
    //     message: 'Oops! You need more coins to continue..',
    //     color: 'danger',
    //   })
    //   return
    // }
    await handleActionWithDownload(
      itemId,
      async (downloadLink, downloadName) => {
        // Replace this with your specific logic for downloading to the device

        // Copy values to clipboard
        const valuesToCopy = [downloadName, downloadLink]
        const filteredValuesToCopy: string[] = valuesToCopy.filter(
          (value): value is string => value !== undefined,
        )
        // coins && setCoinCount(coins - 1)

        await copyValuesSequentially(filteredValuesToCopy).then(() => {
          window.open(downloadLink, '_blank')
        })
      },
    )
  }

  const handleShare = async (itemId: string): Promise<any> => {
    try {
      const response = await makeRequest('share', 'POST', {
        email: email,
        id: itemId,
      })
      const data = response.data

      writeToClipboard(data.share_url)
      await Share.share({
        url: data.share_url,
      })
    } catch (error) {
      setShowToast({ message: 'Failed to share', color: 'danger' })
    }
  }

  const handleDelete = async (itemId: string): Promise<any> => {
    try {
      setIsLoading(true)
      const response = await makeRequest('delete', 'POST', {
        email: email,
        id: [itemId],
      })
      const data = response.data
      if (data.task_id) {
        handleDeleteItem && handleDeleteItem(itemId)
        setShowToast({ message: 'Deleted', color: 'success' })
      }
    } catch (error) {
      setShowToast({ message: 'Failed to delete ' + error, color: 'danger' })
    } finally {
      setIsLoading(false)
    }
  }

  const actionFunctionMap: Record<string, (itemId: string) => Promise<any>> = {
    copyDownloadLink: handleCopyDownloadLink,
    copyFileName: () => handleCopyFileName(fileName || ''),
    share: handleShare,
    download: handleDownloadToDevice,
    delete: handleDelete,
    play: handlePlay,
    playInAnotherApp: handlePlayInAnotherApp,
  }

  const directActions = ['copyFileName']
  // Common function to handle actions
  const handleAction = async (action: string) => {
    setActionCode(action)

    if (directActions.includes(action)) {
      const actionFunction = actionFunctionMap[action]
      if (actionFunction) {
        const success = await actionFunction(item?.id || '')
        if (success) {
          setShowToast({ message: `${action} successful`, color: 'success' })
        }
        setShowModal(false)
        setActionCode('')
        return
      }
    }
    setShowAlert(true)
  }

  // Common function to handle confirmed actions
  const handleConfirmedAction = () => {
    // Handle action logic here based on actionCode
    console.log(`${actionCode} confirmed`)

    const actionFunction = actionFunctionMap[actionCode]
    if (actionFunction) {
      actionFunction(item?.id || '')
    }

    // Close the modal or perform any other necessary actions
    setShowModal(false)
    // Reset actionCode
    setActionCode('')
    // Close the alert
    setShowAlert(false)
  }

  // Common function to handle cancelled actions
  const handleCancelledAction = () => {
    // Close the alert
    setShowAlert(false)
  }

  const getAlertButtons = () => {
    return [
      {
        text: 'Cancel',
        role: 'cancel',
        handler: handleCancelledAction,
        cssClass: 'hover-effect alert-button-cancel hover-effect',
      },
      {
        text: 'OK',
        handler: handleConfirmedAction,
        cssClass: 'hover-effect alert-button-confirm hover-effect',
      },
    ]
  }

  const confirmActions = [
    'delete',
    'download',
    'share',
    'copyDownloadLink',
    'play',
    'playInAnotherApp',
  ]

  const getAlertConfirmButtons = () => {
    return [
      {
        text: 'OK',
        handler: handleConfirmedAction,
      },
    ]
  }
  const skipActions = [
    'download',
    'copyDownloadLink',
    'play',
    'playInAnotherApp',
  ]

  const isPlatformSupported = (value: any) => {
    return value.map((platform) => isPlatform(platform)).some(Boolean)
  }

  return (
    <>
      <IonList>
        {Object.keys(actionDetails).map((action, index) => {
          if (action === 'play' && item && item.file_category !== 'VIDEO') {
            return ''
          }
          if (
            action === 'playInAnotherApp' &&
            item &&
            (item.file_category !== 'VIDEO' ||
              !isPlatformSupported(['capacitor', 'desktop']))
          ) {
            return ''
          } else if (
            item &&
            (item.kind !== 'drive#folder' ||
              (item.kind === 'drive#folder' && !skipActions.includes(action)))
          ) {
            return (
              <ItemWithIcon
                key={index}
                color={actionDetails[action].color}
                icon={actionDetails[action].icon}
                text={actionDetails[action].header}
                onClick={() => handleAction(action)}
              />
            )
          } else {
            return ''
          }
        })}
      </IonList>
      {/* Common Alert for Multiple Actions */}
      <IonAlert
        isOpen={showAlert}
        className="custom-alert"
        onDidDismiss={() => setShowAlert(false)}
        header={actionDetails[actionCode]?.header || 'Are you sure?'}
        message={
          actionDetails[actionCode]?.message ||
          'Do you want to proceed with this action?'
        }
        buttons={
          confirmActions.includes(actionCode)
            ? getAlertButtons()
            : getAlertConfirmButtons()
        }
      />

      <IonToast
        isOpen={!!showToast}
        onDidDismiss={() => setShowToast(null)}
        message={showToast?.message}
        duration={3000}
        position="top"
        color={showToast?.color}
      />
    </>
  )
}

export default ModalOptions

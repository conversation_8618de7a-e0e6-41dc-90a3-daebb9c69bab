import{j as s,I as C,a as t,b as a,i as H,s as L,c as v,d as F,e as A,f as G,g as U,h as w,k as r,l as V,m as M,n as q,o as S,p as h,q as p,w as Y,r as $,t as i,u as J,v as K,x as Q,B as W,y as X,z as E,A as Z,C as k,D as T,E as ss,F as es,G as ns,H as os,J as rs,K as is,L as ls}from"./index-YQZYyPkh.js";import{C as ts}from"./CustomIonHeader-RqF7V-cv.js";import{H as as}from"./HelperCard-t_yvTUPv.js";const cs=({handlePopoverButtonClick:e,selectedValue:c,selectedTask:d})=>s.jsxs(C,{inset:!0,children:[s.jsxs(t,{button:!0,detail:!1,onClick:()=>e("view details"),children:[s.jsx(a,{icon:H,slot:"start"}),"View Details"]}),c==="ongoing"&&(d==null?void 0:d.phase)==="PHASE_TYPE_ERROR"&&s.jsxs(t,{button:!0,detail:!1,onClick:()=>e("retry"),children:[s.jsx(a,{icon:L,slot:"start"}),"Retry"]})]}),ds=({selectedTask:e,setShowDetailsAlert:c})=>s.jsxs(v,{isOpen:!0,initialBreakpoint:1,onDidDismiss:()=>{c(!1)},breakpoints:[0,1],children:[s.jsx(F,{children:s.jsxs(A,{color:"primary",children:[s.jsx(G,{children:"Task Details"}),s.jsx(U,{slot:"end",children:s.jsx(w,{color:"light",onClick:()=>c(!1),children:"Close"})})]})}),s.jsxs(C,{lines:"full",children:[s.jsx(t,{children:s.jsxs(r,{children:[s.jsx("h2",{children:"Name:"}),s.jsx("p",{children:e==null?void 0:e.file_name})]})}),s.jsx(t,{children:s.jsxs(r,{children:[s.jsx("h2",{children:"Size:"}),s.jsx("p",{children:V(parseInt((e==null?void 0:e.file_size)||"0"))})]})}),s.jsx(t,{children:s.jsxs(r,{children:[s.jsx("h2",{children:"Creation Time:"}),s.jsx("p",{children:M(e==null?void 0:e.created_time)})]})}),s.jsx(t,{children:s.jsx(r,{children:s.jsxs(q,{children:[s.jsxs(S,{children:[s.jsx(h,{children:s.jsx(p,{children:"Resource Link:"})}),s.jsx(h,{children:s.jsxs(w,{fill:"clear",size:"small",onClick:()=>{Y((e==null?void 0:e.params.url)||"error copying")},children:[s.jsx(a,{icon:$}),"Copy"]})})]}),s.jsx(S,{children:s.jsx(h,{children:s.jsx(r,{children:s.jsx("p",{children:e==null?void 0:e.params.url})})})})]})})})]})]}),js=()=>{const[e,c]=i.useState(null),[d,m]=i.useState(!1),[l,b]=i.useState("ongoing"),[u,g]=i.useState(null),[j,f]=i.useState(null),[N,I]=i.useState(null),[O,y]=i.useState(!1),D=async()=>{m(!0);const n=l==="ongoing"?"tasks":"completedTasks",{email:o}=is();try{if(!o)throw new Error("Email not found");const x=await ls(n,"POST",{email:o});if(x.status===200){const _=x.data.tasks||[];c(_)}else throw new Error(`Request failed with status: ${x.status}`)}catch(x){f({message:"Error fetching browse data: "+x.message,color:"danger"})}finally{m(!1)}};i.useEffect(()=>{(async()=>{try{await D()}catch(o){f({message:"Error fetching browse data: "+o.message,color:"danger"})}finally{m(!1)}})()},[l]);const P=async n=>{b(n)},z=(n,o)=>{g(o),I(n)},R=n=>{n==="view details"&&y(!0),I(null)},B=()=>s.jsx(s.Fragment,{children:s.jsx(p,{color:"dark",children:"Check completed tasks or Create a new task"})});return s.jsxs(s.Fragment,{children:[s.jsx(ts,{title:"Cloud Tasks"}),s.jsx(J,{vertical:"bottom",horizontal:"end",children:s.jsx(K,{onClick:()=>D(),children:s.jsx(a,{icon:Q,size:"large"})})}),s.jsxs(W,{loading:d,children:[s.jsxs(X,{value:l,onIonChange:n=>{n.stopPropagation(),P(n.detail.value)},color:l==="ongoing"?"primary":"success",children:[s.jsxs(E,{value:"ongoing",layout:"icon-start",children:[s.jsx(a,{icon:Z}),s.jsx(r,{children:"Ongoing"})]}),s.jsxs(E,{value:"completed",layout:"icon-start",children:[s.jsx(a,{icon:k}),s.jsx(r,{children:"Completed"})]})]}),s.jsx(T,{className:"ion-padding",children:s.jsxs("div",{className:"custom-list-container",children:[j&&s.jsx(ss,{position:"top",isOpen:!!j,onDidDismiss:()=>f(null),message:j.message,color:j.color,duration:3e3}),s.jsx(C,{children:e&&(e==null?void 0:e.length)>0?e.map(n=>s.jsxs(t,{children:[s.jsxs(h,{size:"9",children:[s.jsx(r,{className:"label-text",children:n.name}),s.jsxs("div",{className:"flex-container",children:[s.jsx(p,{children:`${(parseInt(n.file_size)/1024/1024/1024).toFixed(1)}GB`}),s.jsx(p,{className:"progress-text",color:l==="completed"?"tertiary":n.phase==="PHASE_TYPE_RUNNING"?"success":"danger",children:n.message})]})]}),s.jsxs(h,{size:"2",children:[s.jsxs(r,{children:[n.progress,"%"]}),s.jsx(es,{color:l==="completed"?"success":n.phase==="PHASE_TYPE_RUNNING"?"primary":"danger",value:n.progress/100})]}),s.jsx(h,{size:"1",children:s.jsx(a,{className:"hover-effect",icon:ns,color:"tertiary",onClick:o=>{o.stopPropagation(),z(o,n)}})})]},n.id)):!d&&s.jsx(as,{cardTitle:"Nothing to show here",cardSubtitle:B(),cardSubTitleStyle:{display:"flex",flexDirection:"column",textAlign:"justify"},icon:os,titleColor:"primary"})})]})})]}),u&&s.jsx(rs,{keepContentsMounted:!0,event:N,isOpen:!!u,onDidDismiss:()=>{I(null),g(null)},children:s.jsx(cs,{handlePopoverButtonClick:R,selectedValue:l,selectedTask:u})}),s.jsx(v,{isOpen:O,initialBreakpoint:1,onDidDismiss:()=>{g(null),y(!1)},breakpoints:[0,1],children:s.jsx(ds,{selectedTask:u,setShowDetailsAlert:y})})]})};export{js as default};

import { IonButton, IonIcon } from '@ionic/react'
import { memo } from 'react'
import GitHubButton from 'react-github-btn'
import './AuthButtons.css'
import { openOutline } from 'ionicons/icons'
const AuthButtons = memo(function AuthButtons() {
  return (
    <>
      <div className="auth-github-button">
        <GitHubButton
          href="https://github.com/bharathganji/pikpak-plus"
          data-color-scheme="no-preference: light; light: light; dark: dark;"
          data-size="large"
          data-show-count="true"
          aria-label="Star bharathganji/pikpak-plus on GitHub"
        >
          Star
        </GitHubButton>
      </div>
      <div className="auth-button-container">
        {/* <IonButton routerLink="/donate" fill="clear">
          Contribute &nbsp;
          <IonIcon icon={openOutline} />
        </IonButton> */}

        {/* <div
          style={{
            marginTop: '0.5rem',
          }}
        >
          <a href="https://www.buymeacoffee.com/bharathganji" target="_blank">
            <img
              src="https://www.buymeacoffee.com/assets/img/custom_images/orange_img.png"
              alt="Buy Me A Coffee"
              style={{
                height: '41px !important',
                width: '174px !important',
                boxShadow:
                  '0px 3px 2px 0px rgba(190, 190, 190, 0.5) !important',
                WebkitBoxShadow:
                  '0px 3px 2px 0px rgba(190, 190, 190, 0.5) !important',
              }}
            />
          </a>
        </div> */}
        <IonButton
          href="https://jackett.pikpak-plus.com"
          target="_blank"
          fill="clear"
        >
          Search torrents &nbsp;
          <IonIcon icon={openOutline} />
        </IonButton>
        <IonButton routerLink="/faq" fill="clear">
          FAQ &nbsp;
          <IonIcon icon={openOutline} />
        </IonButton>
      </div>
    </>
  )
})

export default AuthButtons

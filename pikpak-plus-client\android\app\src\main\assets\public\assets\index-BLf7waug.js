import{t as s,a8 as e,ab as i}from"./index-YQZYyPkh.js";class c extends s.PureComponent{constructor(t){super(t),this.$=e.createRef(),this._=e.createRef()}render(){return e.createElement("span",{ref:this.$},e.createElement("a",{...this.props,ref:this._},this.props.children))}componentDidMount(){this.paint()}getSnapshotBeforeUpdate(){return this.reset(),null}componentDidUpdate(){this.paint()}componentWillUnmount(){this.reset()}paint(){const t=this.$.current.appendChild(document.createElement("span"));i(()=>import("./buttons.esm-B8a_CsNS.js"),[]).then(({render:r})=>{this._.current!=null&&r(t.appendChild(this._.current),function(n){try{t.parentNode.replaceChild(n,t)}catch{}})})}reset(){this.$.current.replaceChild(this._.current,this.$.current.lastChild)}}export{c as G};

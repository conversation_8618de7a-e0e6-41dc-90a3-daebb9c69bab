import React from 'react'
import Slider from 'react-slick'

import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'
import 'slick-carousel/slick/fonts/slick.woff'
import 'slick-carousel/slick/fonts/slick.ttf'
interface CarouselSliderProps {
  children: React.ReactNode
}

function CarouselSlider({ children }: CarouselSliderProps) {
  const settings = {
    dots: true,
    infinite: true,
    speed: 5000,
    autoplay: true,
    slidesToShow: 1,
    slidesToScroll: 1,
    pauseOnHover: true,
    initialSlide: 0,
  }

  return (
    <div className="slider-container">
      <Slider {...settings}>{children}</Slider>
    </div>
  )
}

export default CarouselSlider

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.eslintcache

# custom
# .env
.flaskenv
client.json
venv
run
__pycache__
temp.*
PP-server#*.json


down.bash

# self used virtual env
venv/
venv-*/
# config file
config.py

#================ Python3
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]

# temp or backup file
*.swp
*~

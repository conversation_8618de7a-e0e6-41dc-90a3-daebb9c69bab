import React, {
  useMemo,
  lazy,
  Suspense,
  //  useEffect
} from 'react'
import {
  IonTabs,
  IonRouterOutlet,
  IonTabBar,
  IonTabButton,
  IonIcon,
  IonLabel,
  IonText,
} from '@ionic/react'
import {
  listCircle,
  folderOpen,
  magnetOutline,
  search,
  ellipsisHorizontal,
} from 'ionicons/icons'
import { Redirect, Route } from 'react-router'
import { getauthCookie, isJWTValid } from './helpers/helpers'
import LoadingSpinner from './components/LoadingSpinner/LoadingSpinner'
// import DonationForm from './components/MoreOptionsPage/DonationForm/DonationForm'
import './App.css'
import FaqPage from './components/FaqPage/FaqPage'
// import { useIonRouter } from '@ionic/react'
// import { App as AppPlugin } from '@capacitor/app'
import PaymentCard from './components/payment-card/PaymentCard'

const DownloadList = lazy(() => import('./components/TasksList/taskslist'))
const AddUrlForm = lazy(() => import('./components/AddURL/addUrl'))
const BrowseFolders = lazy(
  () => import('./components/BrowseFolders/BrowseFolders'),
)
const Search = lazy(() => import('./components/Search/Search'))
const Login = lazy(() => import('./components/authentication/login/Login'))
const TempLogin = lazy(() => import('./components/authentication/temp/TempLogin'))
const SignUp = lazy(() => import('./components/authentication/signUp/SignUp'))
const MoreOptions = lazy(
  () => import('./components/MoreOptionsPage/MoreOptionsPage'),
)
// const Rewards = lazy(() => import('./components/Rewards/Rewards'))

const App: React.FC = () => {
  const isEnable = useMemo(() => isJWTValid(getauthCookie()), [])
  const isIgnoreList = ['/login', '/signup', '/temp']

  // useEffect(() => {
  //   getWindowIsDarkThemeMode() === 'true'
  //     ? setItemToLocalStorage('darkMode', 'true')
  //     : setItemToLocalStorage('darkMode', 'false')
  // }, [])

  // const ionRouter = useIonRouter()

  // const exitApp = () => {
  //   const onExitApp = () => !ionRouter.canGoBack() && AppPlugin.exitApp()

  //   const exit = (ev) => ev.detail.register(-1, onExitApp)
  //   document.addEventListener('ionBackButton', exit)

  //   const removeEventListener = () =>
  //     document.addEventListener('ionBackButton', onExitApp)
  //   return removeEventListener
  // }
  // useEffect(exitApp, [])

  const renderRoute = (path: string, component: React.FC, exact = true) => (
    <Route
      path={path}
      render={() =>
        isEnable !== isIgnoreList.includes(path) ? (
          <Suspense fallback={<LoadingSpinner />}>
            {React.createElement(component)}
          </Suspense>
        ) : isEnable ? (
          <Redirect to="/create" />
        ) : (
          <Redirect to="/login" />
        )
      }
      exact={exact}
    />
  )

  const renderTabButton = (tab: string, icon: string, label: string) => (
    <IonTabButton tab={tab} href={`/${tab}`}>
      <IonIcon icon={icon} color="black" />
      <IonLabel>{label}</IonLabel>
    </IonTabButton>
  )

  return (
    <>
      <IonText>
        <h1 className="header-text">Welcome to PikPak Plus</h1>
      </IonText>
      <IonTabs>
        <IonRouterOutlet>
          <Redirect exact path="/" to="/create" />
          {/* <Route path="/donate" component={DonationForm} /> */}
          <Route path="/faq" component={FaqPage} />
          <Route path="/payment" component={PaymentCard} />
          {renderRoute('/tasks', DownloadList)}
          {renderRoute('/create', AddUrlForm)}
          {renderRoute('/login', Login)}
          {renderRoute('/temp', TempLogin)}
          {renderRoute('/signup', SignUp)}
          {renderRoute('/browse', BrowseFolders)}
          {renderRoute('/search', Search)}
          {renderRoute('/config', MoreOptions)}
          {/* {renderRoute('/rewards', Rewards)} */}
        </IonRouterOutlet>

        <IonTabBar slot="bottom">
          {renderTabButton('tasks', listCircle, 'Tasks')}
          {renderTabButton('create', magnetOutline, 'Magnet')}
          {renderTabButton('browse', folderOpen, 'Folders')}
          {renderTabButton('search', search, 'Search')}
          {renderTabButton('config', ellipsisHorizontal, 'More')}
        </IonTabBar>
      </IonTabs>
    </>
  )
}

export default App

# Do not change this
FLASK_APP = api.py 
FLASK_DEBUG = 1 

# pikpak email, password (Required)
user= <EMAIL> 
passwd= example  

##in case of multiple pikpak accounts ,
##and in future u want to remove keep that position empty eg: username= , <EMAIL>
#user= <EMAIL>, <EMAIL>  
#passwd= password1, password2   

# Supabse Project URL, anon public-key (Required)
SUPABASE_URL = https://example.supabase.co
SUPABASE_KEY = example string key

# Jackett API key and domain (optional)
# if you want to use search Functionality (Required)
JACKETT_API_KEY = string key
JACKET_DOMAIN = https://jackett.example

# variable used to enable either "delete" or "trash" an item. Default is trash
DELETE_OR_TRASH = 'trash'

# optional 
proxy=

# api hostname/URL if deployed external else dont change eg:'https://api.example.com/'
VITE_PIKPAK_PLUS_API = 'http://server:5000'

#  UI Domain Name (optional)
VITE_HOSTNAME = 'https://example.com'

# Ports (optional)
VITE_DEVELOPMENT_PORT = "3001"
VITE_PRODUCTION_PORT = "3002"

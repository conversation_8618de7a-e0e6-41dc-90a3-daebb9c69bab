import{m,o as y,n as r,p as v,e as w,i as k,t as g,u as l,v as S}from"./BrowseFolders-B5zrTqBv.js";import{E as f,t as b}from"./vidstack-B4MOJc-J-B29ZAfIi.js";import{resolveYouTubeVideoId as T}from"./vidstack-DscYSLiW-CA6XwpqT.js";import"./index-YQZYyPkh.js";import"./CustomIonHeader-RqF7V-cv.js";import"./HelperCard-t_yvTUPv.js";const c={Pm:-1,qg:0,rg:1,hj:2,ij:3,jj:5};class E extends f{constructor(t,s){super(t),this.b=s,this.$$PROVIDER_TYPE="YOUTUBE",this.scope=m(),this.ia=y(""),this.Aa=-1,this.nd=-1,this.wc=!1,this.ha=0,this.ca=new r(0,0),this.L=null,this.J=null,this.S=null,this.language="en",this.color="red",this.cookies=!1}get c(){return this.b.delegate.c}get currentSrc(){return this.L}get type(){return"youtube"}get videoId(){return this.ia()}preconnect(){v(this.Ob())}setup(){super.setup(),w(this.xe.bind(this)),this.c("provider-setup",this)}async play(){const{paused:t}=this.b.$state;return this.J||(this.J=b(()=>{if(this.J=null,t())return"Timed out."}),this.u("playVideo")),this.J.promise}async pause(){const{paused:t}=this.b.$state;return this.S||(this.S=b(()=>{this.S=null,t()}),this.u("pauseVideo")),this.S.promise}setMuted(t){t?this.u("mute"):this.u("unMute")}setCurrentTime(t){this.wc=this.b.$state.paused(),this.u("seekTo",t),this.c("seeking",t)}setVolume(t){this.u("setVolume",t*100)}setPlaybackRate(t){this.u("setPlaybackRate",t)}async loadSource(t){if(!k(t.src)){this.L=null,this.ia.set("");return}const s=T(t.src);this.ia.set(s??""),this.L=t}Ob(){return this.cookies?"https://www.youtube.com":"https://www.youtube-nocookie.com"}xe(){this.A();const t=this.ia();if(!t){this.tc.set("");return}this.tc.set(`${this.Ob()}/embed/${t}`),this.c("load-start")}ng(){const{keyDisabled:t}=this.b.$props,{muted:s,playsInline:a,nativeControls:h}=this.b.$state,e=h();return{autoplay:0,cc_lang_pref:this.language,cc_load_policy:e?1:void 0,color:this.color,controls:e?1:0,disablekb:!e||t()?1:0,enablejsapi:1,fs:1,hl:this.language,iv_load_policy:e?1:3,mute:s()?1:0,playsinline:a()?1:0}}u(t,s){this.te({event:"command",func:t,args:s?[s]:void 0})}hd(){window.setTimeout(()=>this.te({event:"listening"}),100)}ld(t){this.c("loaded-metadata"),this.c("loaded-data"),this.b.delegate.Ha(void 0,t)}jb(t){var s;(s=this.S)==null||s.resolve(),this.S=null,this.c("pause",void 0,t)}nc(t,s){const{duration:a,realCurrentTime:h}=this.b.$state,e=this.Aa===c.qg,i=e?a():t,o={currentTime:i,played:this.vc(i)};this.c("time-update",o,s),!e&&Math.abs(i-h())>1&&this.c("seeking",i,s)}vc(t){return this.ha>=t?this.ca:this.ca=new r(0,this.ha=t)}ob(t,s,a){const h={buffered:new r(0,t),seekable:s};this.c("progress",h,a);const{seeking:e,realCurrentTime:i}=this.b.$state;e()&&t>i()&&this.pb(a)}pb(t){const{paused:s,realCurrentTime:a}=this.b.$state;window.clearTimeout(this.nd),this.nd=window.setTimeout(()=>{this.c("seeked",a(),t),this.nd=-1},s()?100:0),this.wc=!1}mc(t){const{seeking:s}=this.b.$state;s()&&this.pb(t),this.c("pause",void 0,t),this.c("end",void 0,t)}je(t,s){var n,d;const{started:a,paused:h,seeking:e}=this.b.$state,i=t===c.rg,o=t===c.ij,u=(h()||this.J)&&(o||i);if(o&&this.c("waiting",void 0,s),e()&&i&&this.pb(s),!a()&&u&&this.wc){(n=this.J)==null||n.reject("invalid internal play operation"),this.J=null,i&&(this.pause(),this.wc=!1);return}switch(u&&((d=this.J)==null||d.resolve(),this.J=null,this.c("play",void 0,s)),t){case c.jj:this.ld(s);break;case c.rg:this.c("playing",void 0,s);break;case c.hj:this.jb(s);break;case c.qg:this.mc(s);break}this.Aa=t}ue({info:t},s){var i;if(!t)return;const{title:a,intrinsicDuration:h,playbackRate:e}=this.b.$state;if(g(t.videoData)&&t.videoData.title!==a()&&this.c("title-change",t.videoData.title,s),l(t.duration)&&t.duration!==h()){if(l(t.videoLoadedFraction)){const o=((i=t.progressState)==null?void 0:i.loaded)??t.videoLoadedFraction*t.duration,u=new r(0,t.duration);this.ob(o,u,s)}this.c("duration-change",t.duration,s)}if(l(t.playbackRate)&&t.playbackRate!==e()&&this.c("rate-change",t.playbackRate,s),t.progressState){const{current:o,seekableStart:u,seekableEnd:n,loaded:d,duration:p}=t.progressState;this.nc(o,s),this.ob(d,new r(u,n),s),p!==h()&&this.c("duration-change",p,s)}if(l(t.volume)&&S(t.muted)){const o={muted:t.muted,volume:t.volume/100};this.c("volume-change",o,s)}l(t.playerState)&&t.playerState!==this.Aa&&this.je(t.playerState,s)}A(){this.Aa=-1,this.nd=-1,this.ha=0,this.ca=new r(0,0),this.J=null,this.S=null,this.wc=!1}}export{E as YouTubeProvider};

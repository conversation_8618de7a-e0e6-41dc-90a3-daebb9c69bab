import pytz
import os
import re
from urllib.parse import urlencode
from gotrue.errors import AuthApiError
import functools
from supabase_client import supabase
from flask import Flask, request, jsonify, abort
from flask_cors import CORS
from pikpak import client as pik
from pikpak import shell_cmds as cmd
import logging
import random
import string
import apiscrape
import requests
from datetime import datetime
# import json
# from datetime import date


app = Flask(__name__)
app.config['SECRET_KEY'] = ''.join(
    random.choice(string.ascii_uppercase + string.digits))
initialized_clients = {}

usernames_str = os.getenv("user", "")
usernamesArray = [email.strip()
                  for email in usernames_str.split(",")] if usernames_str else []

# VNSHORTENER_KEY = os.getenv("VNSHORTENER_KEY", "")
# shrinkforearn_KEY = os.getenv("shrinkforearn_KEY", "")
# CLKS_PRO_KEY = os.getenv("CLKS_PRO_KEY", "")

CORS(app)


DELETE_OR_TRASH = os.getenv("DELETE_OR_TRASH", "trash")


def user_route(enforce_login=False):
    def decorator(route):
        @functools.wraps(route)
        def route_wrapper(*args, **kwargs):
            jwt = request.headers.get('Authorization') or ''
            # print(jwt)
            if enforce_login and not jwt:
                return jsonify({"error": "token is missing"}), 401
            supabase_user = None
            if enforce_login and jwt and jwt.startswith('Bearer '):
                jwt = jwt.replace('Bearer ', '')
                # print(f"JWT: {jwt}")

                try:
                    supabase_user = supabase.auth.get_user(jwt)
                    # print("successfully got user")
                except Exception as e:

                    return jsonify({"error": "signIn failed"}), 401
            user = supabase_user.user if supabase_user else None
            return route(user, *args, **kwargs)
        return route_wrapper
    return decorator


def check_premium_status(email: str) -> dict:

    # Query the premium_paid table
    data = supabase.table("premium_paid").select(
        "*").eq("email", email).execute()

    # Check if email exists
    if data.data:
        # Get the expiry date
        expiry_date = data.data[0]["expiry_date"]
        premium_info = {
            key: data.data[0][key]
            for key in ["amount", "webdav_username", "webdav_password", "expiry_date"]
        }
        # Convert expiry date to datetime object
        expiry_date = datetime.strptime(expiry_date, "%Y-%m-%dT%H:%M:%S%z")

        # Get today's date
        today = datetime.now(pytz.utc)

        # Check if premium subscription has expired
        if expiry_date < today:
            return {
                "email_exists": True,
                "is_expired": True,
            }
        else:
            return {
                "email_exists": True,
                "is_expired": False,
                "premium_info": premium_info
            }
    else:
        return {
            "email_exists": False,
            "is_expired": None
        }

# def are_coins_available(email):
#     try:
#         response = supabase.table('pikpak_data').select('coins').eq('email', email).execute()
#         data = response.data
#         if data and data[0]['coins'] > 0:  # Assuming coins is an integer field
#             return True, data[0]['coins']
#         else:
#             return False, 0
#     except Exception as e:
#         # Log the error or handle it as appropriate
#         return False, 0

# def decrease_coins(email, amount):
#     new_coin_count = amount - 1
#     try:
#         coin_update_response = supabase.table('pikpak_data').update({'coins': new_coin_count}).eq('email', email).execute()

#         return True
#     except Exception as e:
#         # Log the error or handle it as appropriate
#         return False


def initialize_client_route(server_number):
    global initialized_clients

    try:
        # Obtain credential filename from request or use a default value
        cred_filename = f'PP-server#{server_number}.json'
        print(initialized_clients, "initialized_clients")

        # Initialize the client within the request context
        initialized_client = initialize_client(cred_filename, server_number)
        initialized_clients[server_number] = initialized_client

        return jsonify({"result": f"Client for PP-server#{server_number} initialized successfully"})

    except Exception as e:
        print(f"Error initializing client for PP-server#{server_number}: {e}")
        return jsonify({"error": str(e)}), 500


def initialize_client(cred_filename, server_number):
    try:
        users = usernamesArray

        passwords_str = os.getenv("passwd", "")
        passwords = [password.strip() for password in passwords_str.split(
            ",")] if passwords_str else []

        user_index = int(server_number) - 1
        print("user_index: ", user_index)
        print("users length: ", len(users))
        if user_index < 0 or user_index >= len(users):
            raise ValueError("Invalid server number")

        user = users[user_index]
        password = passwords[user_index] if user_index < len(
            passwords) else "default_password"  # Fetch password based on index

        client, conf = pik.client_from_credit(cred_filename, proxy=None)
        if not client:
            # Provide default value for proxy
            proxy = os.getenv("proxy", "")

            client, conf = pik.client_from_password(
                user, password, cred_filename, proxy)

        # Check if the client is successfully initialized
        if not client:
            raise ValueError("Invalid login credentials")

        return client

    except Exception as e:
        print(f"Error initializing client for PP-server#{server_number}: {e}")
        return jsonify({"error": str(e)}), 500


def filter_tasks_by_supabase_ids(tasks, supabase_ids):
    matching_tasks = []
    task_ids = {task['id'] for task in tasks}
    supabase_id_set = {item['id'] for item in supabase_ids}
    common_ids = task_ids.intersection(supabase_id_set)

    for task in tasks:
        if task['id'] in common_ids:
            matching_tasks.append(task)
    return matching_tasks


def process_tasks_route(command_key, email, server_number):
    if initialized_clients.get(server_number) is None:
        abort(401, description="Client not initialized. Call initialize_client first.")

    # Execute the tasks command using initialized_client
    res = cmd.cmds[command_key](
        initialized_clients.get(server_number), "param")
    supabase_res = supabase.table('user_actions').select('data').eq(
        'email', email).eq('actions', 'create_task').execute()

    # Extracting supabase IDs from supabase response
    supabase_ids = [item['data'] for item in supabase_res.data]

    # Filtering tasks based on supabase IDs
    matching_tasks = filter_tasks_by_supabase_ids(res['tasks'], supabase_ids)
    res['tasks'] = matching_tasks
    return res


@app.route('/tasks', methods=['POST'])
def get_tasks():
    data = request.get_json()
    email = data.get('email')
    print(email, "email")

    server_number = data.get('server_number')
    matching_tasks = process_tasks_route("tasks", email, server_number)

    # Assuming tasks command returns a dictionary, modify accordingly
    return jsonify(matching_tasks)


@app.route('/rename', methods=['POST'])
def post_rename():
    data = request.get_json()
    name = data.get('name')
    id = data.get('id')

    res = cmd.cmds['file_rename'](initialized_clients.get(2), id, name)

    # Assuming tasks command returns a dictionary, modify accordingly
    return jsonify(res)


@app.route('/completedTasks', methods=['POST'])
def get_tasks_completed():
    data = request.get_json()
    email = data.get('email')
    server_number = data.get('server_number')

    matching_tasks = process_tasks_route(
        "tasks_completed", email, server_number)

    # Assuming tasks command returns a dictionary, modify accordingly
    return jsonify(matching_tasks)


@app.route('/browse', methods=['POST'])
@user_route(enforce_login=False)
def browse(user):

    try:
        # Get JSON data from the request
        data = request.get_json()
        item_index = data.get('item_index')
        server_number = data.get('server_number')

        # Call the ls command with initialized client and item_index
        res = cmd.cmds["ls"](initialized_clients.get(
            server_number), "param", item_index)

        # Return the result as JSON
        return jsonify(res)

    except Exception as e:
        # Log error if an exception occurs
        logging.error("An error occurred during browse request: %s", str(e))
        return jsonify({"error": "An error occurred"}), 500


def create_supabase_task_action(user_email, action, data):

    if not user_email:
        return jsonify({"error": "user parameter is missing"}), 400

    try:
        res = supabase.table("user_actions").insert(
            {"email": user_email, "actions": action, "data": data}).execute()
        return res
    except Exception as e:

        return jsonify({"error": str(e)}), 500


def check_magnet_link(magnet_link, email):
    """
    Checks if a magnet link can be determined and if the provided email is allowed.

    Args:
        magnet_link: The magnet link to check.
        email: The email address to check.

    Returns:
        A dictionary with the following keys:
            - can_determine: True if the link can be determined and the email is allowed, False otherwise.
            - details: The details from the API response if the link can be determined, None otherwise.
    """

    allowed_emails = [
        # "<EMAIL>",
        # "<EMAIL>",
        # "<EMAIL>",
        # "<EMAIL>",
        # '<EMAIL>',
        "<EMAIL>",
        '<EMAIL>',
        "<EMAIL>",
        '<EMAIL>',
        # '<EMAIL>',
        # '<EMAIL>',
        # '<EMAIL>',
        # "<EMAIL>",
        # "<EMAIL>",
        # "<EMAIL>",
        # "<EMAIL>",
    ]

    if email in allowed_emails:
        return {'can_determine': True, 'details': None}

    # Check if the user has a valid premium status
    premium_status = check_premium_status(email)

    if premium_status['email_exists']:
        return {'can_determine': True, 'details': None}

    # If the email doesn't exist or the premium status is expired, return False
    if not premium_status['email_exists'] or premium_status['is_expired']:
        return {'can_determine': False, 'details': 'Premium required or expired'}

    try:
        response = requests.get(
            f'https://whatslink.info/api/v1/link?url={magnet_link}')
        response.raise_for_status()

        data = response.json()

        print('email:', email, "size:", data['size'])

        # Check if the size is greater than 25 GB
        if data['size'] > 25 * 1024 * 1024 * 1024:  # 25 GB in bytes
            return {'can_determine': False, 'details': 'files greater than 25GB not allowed.'}

        # Check if the link is undetermined
        if data['type'] == 'UNKNOWN' and data['size'] == 0:
            return {'can_determine': True, 'details': None}

        return {'can_determine': True, 'details': data}

    except requests.exceptions.RequestException:
        # Return True on exceptions
        return {'can_determine': True, 'details': None}


@app.route('/addURL', methods=['POST'])
@user_route(enforce_login=False)
def add_url(user):
    # Get the URL from the request data
    data = request.get_json()
    url = data.get('url')
    email = data.get('email')
    user_dir = data.get('user_dir')

    # check_magnet_link
    result = check_magnet_link(url, email)

    if result['can_determine'] is False:
        return jsonify({"error": result['details']}), 400

    # allowed_emails = [
    #     "<EMAIL>",
    #     "<EMAIL>",
    #     "<EMAIL>",
    #     "<EMAIL>",
    #     "<EMAIL>",
    #     '<EMAIL>',
    #     '<EMAIL>'
    # ]
    # if email not in allowed_emails:
    #     return jsonify({"error": "Cloud Download limit 40TB reached. Access restricted till Sep 30th 2024, Downloads Allowed only for specific users" }), 400

    # isRedeemedRowsAvailable, row_count = are_redeemed_rows_available(email)

    # Check if 10 or more redeemed rows are available for today
    # coins_should_be_decreased = not isRedeemedRowsAvailable

    # if coins_should_be_decreased:
    #     # If not enough redeemed rows, check for coin availability
    #     isCoinsAvailable, coins_count = are_coins_available(email)
    #     if not isCoinsAvailable:
    #         return jsonify({"error": "Insufficient coins"}), 400

    # print("crated with parent", user_dir)
    server_number = data.get('server_number')

    if not url:
        return jsonify({"error": "URL parameter is missing"}), 400

    try:
        # Execute the 'fetch' command with the initialized client
        res = cmd.cmds["fetch"](
            initialized_clients.get(server_number), url, user_dir)
        create_supabase_task_action(email, "create_task", res['task'])

        # if coins_should_be_decreased:
        #     # Only decrease coins if fetch command succeeds
        #     decrease_coins(email, coins_count)

        return jsonify({"result": res})

    except Exception as e:
        return jsonify({"error": str(e)}), 500


def get_username_from_email(email):
    # Split the email address at the "@" symbol
    username, _ = email.split('@', 1)

    return username


def create_folder(user_email):

    if not user_email:
        return jsonify({"error": "user parameter is missing"}), 400
    try:
        print("email ", user_email)

        # Define a dictionary to store server information
        server_info = {}

        for server_number in range(0, len(usernamesArray)):
            if usernamesArray[server_number] == "":
                continue
            server_number = server_number+1
            res = check_for_duplicate_file_name(user_email, server_number)
            file_id = res["id"]
            # Create a dictionary to represent the server information
            server_id = f"PP-server#{server_number}"
            server_info[server_id] = {
                "directory_id": file_id,
                "date": datetime.now().isoformat()  # Get the current date and time
            }
        # Insert server_info into the 'server_info' column of the 'pikpak_data' table
        supabase.table("pikpak_data").insert(
            {"email": user_email, "server_info": server_info}).execute()

        return True
    except Exception as e:
        print('error in create folder', e)
        return jsonify({"error": str(e)}), 500


@app.route('/download', methods=['POST'])
@user_route(enforce_login=False)
def download(user):
    # Get the URL from the request data
    data = request.get_json()
    email = data.get('email')
    action = data.get('action')
    id = data.get('id')
    server_number = data.get('server_number')

    # isRedeemedRowsAvailable, row_count = are_redeemed_rows_available(email)

    # # Check if 10 or more redeemed rows are available for today
    # coins_should_be_decreased = not isRedeemedRowsAvailable

    # if coins_should_be_decreased:
    #     # If not enough redeemed rows, check for coin availability
    #     isCoinsAvailable, coins_count = are_coins_available(email)
    #     if not isCoinsAvailable:
    #         return jsonify({"error": "Insufficient coins"}), 400

    if not id:
        return jsonify({"error": "URL parameter is missing"}), 400

    try:
        # Execute the 'fetch' command with the initialized client
        res = cmd.cmds["download"](
            initialized_clients.get(server_number), "param", id)
        supabase.table("user_actions").insert(
            {"email": email, "actions": action, "data": res}).execute()

        # if coins_should_be_decreased:
        #     # Only decrease coins if fetch command succeeds
        #     decrease_coins(email, coins_count)

        return jsonify(res)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/share', methods=['POST'])
def share():
    # Get the URL from the request data
    data = request.get_json()
    email = data.get('email')
    id = data.get('id')
    server_number = data.get('server_number')

    if not id:
        return jsonify({"error": "id parameter is missing"}), 400
    try:
        # Execute the 'fetch' command with the initialized client
        res = cmd.cmds["file_batch_share"](
            initialized_clients.get(server_number), [id])
        supabase.table("user_actions").insert(
            {"email": email, "actions": "share", "data": res}).execute()
        return jsonify(res)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/serverstats', methods=['POST'])
def serverstats():
    try:
        data = request.get_json()
        server_number = data.get('server_number')
        res = cmd.cmds["get_transfer_quota"](
            initialized_clients.get(server_number))
        res['base']['user_id'] = ''
        return jsonify(res['base'])
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/drivestats', methods=['POST'])
def drivestats():
    data = request.get_json()
    server_number = data.get('server_number')
    try:
        res = cmd.cmds["get_quota_info"](
            initialized_clients.get(server_number))
        return jsonify(res)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route('/getRedirectUrl', methods=['POST'])
def getRedirectUrl():
    data = request.get_json()
    url = data.get('url')
    try:
        res = requests.get(url)
        return jsonify(res)
    except Exception as e:
        error_message = str(e)
        start_index = error_message.find("'")
        # Extract the substring after the single quotation marks
        remaining_string = error_message[start_index + 1:-1]

        return jsonify(str(remaining_string)), 200


@app.route('/delete', methods=['POST'])
@user_route(enforce_login=False)
def delete(user):
    data = request.get_json()
    email = data.get('email')
    ids = data.get('id')  # Note the plural 'ids'
    server_number = data.get('server_number')

    if not ids:
        return jsonify({"error": "No IDs provided"}), 400

    DELETE_OR_TRASH_local = DELETE_OR_TRASH.lower()
    try:
        if DELETE_OR_TRASH_local == 'delete':
            res = cmd.cmds["delete"](
                # Pass the array of IDs
                initialized_clients.get(server_number), ids)
            supabase.table("user_actions").insert(
                {"email": email, "actions": "delete", "data": ids}).execute()
        else:
            res = cmd.cmds["trash"](initialized_clients.get(
                server_number), ids)  # Pass the array of IDs
        return jsonify(res)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


def check_error(response):
    # Parse the error response
    error_response = response.json()

    # Check if the error message and error code match the expected conditions
    if ("error" in error_response and error_response["error"] == "file_duplicated_name" and
            "error_code" in error_response and error_response["error_code"] == 3):
        return True
    else:
        return False


def check_for_duplicate_file_name(user_email, server_number):
    user_dir = get_username_from_email(user_email)
    try:
        # Execute the command to create a folder
        res = cmd.cmds["create_folder"](
            initialized_clients.get(server_number), user_dir)

        return res  # No error occurred, so return res
    except Exception as e:
        print("Error while creating folder: ", e)
        return False  # Other 400 errors or exceptions occurred, so return False


def update_server_directory_id(email, server_name, new_file_id):
    try:
        # Get the existing server_info for the given email
        response = supabase.table('pikpak_data').select(
            'server_info').eq('email', email).execute()
        data = response.data

        if data and data[0].get('server_info') is None:
            # If server_info is None, initialize it as an empty dictionary
            data[0]['server_info'] = {}

        if data:
            # Get the current server_info dictionary
            current_server_info = data[0].get('server_info', {})

            # Check if the specified server_name exists in server_info
            if server_name in current_server_info:
                # Update the directory_id for the specified server
                current_server_info[server_name]["directory_id"] = new_file_id
                current_server_info[server_name]["date"] = datetime.now(
                ).isoformat()
            else:
                # Update or create a new entry for the specified server_name
                current_server_info[server_name] = {
                    "date": datetime.now().isoformat(),  # Include the current date
                    "directory_id": new_file_id
                }

            # Construct the update command
            update_command = {
                "server_info": current_server_info
            }

            # Execute the update command and update the Supabase table
            response = supabase.table('pikpak_data').update(
                update_command).eq('email', email).execute()
        else:
            print("User data not found")
    except Exception as e:
        print(f"Error updating server directory ID: {str(e)}")


def switchserver(email):
    try:
        # Get all usernames from the usernamesArray
        usernames = usernamesArray

        # Construct the filter using the 'in' operator to retrieve data for all usernames
        response = supabase.table('premium_accounts').select(
            '*').in_('username', usernames).execute()
        data = response.data

        # Process the data for each username
        for row in data:
            username = row.get('username')
            is_donated = row.get('isDonated')
            print("Processing data for username:", username)

            if is_donated == True:
                print("User has donated.")
                if username in usernamesArray:
                    server_number = usernamesArray.index(username) + 1
                    is_duplicated = check_for_duplicate_file_name(
                        email, server_number)
                    print("is_duplicated", is_duplicated)

                    if is_duplicated == False:
                        continue
                    else:
                        file_id = is_duplicated["id"]
                        server_ID = f'PP-server#{server_number}'
                        print("Updating server directory ID for " + username +
                              " - Server ID: " + server_ID + ", File ID: " + file_id)
                        update_server_directory_id(email, server_ID, file_id)
                else:
                    print(
                        "Username '{}' not found in the usernamesArray.".format(username))
        return jsonify({'result': 'switch success'})
    except Exception as e:
        print("switch Error: ", str(e))
        return jsonify({"error": str(e)}), 500


def generate_server_details(server_id, contact, expiry, limit, drive_used, created_at):
    return {
        "server_id": server_id,
        "created_at": created_at,
        "contact": contact,
        "expiry": expiry,
        "limit": limit,
        "drive_used": drive_used,
    }


# @app.route('/getServers2', methods=['GET'])
# def get_servers2():
#     try:
#         res = cmd.cmds["get_quota_info"](initialized_clients.get(2))
#         return jsonify( res)
#     except Exception as e:
#         error_message = str(e)
#         return jsonify({"error": error_message}), 500

@app.route('/getServers', methods=['GET'])
def get_servers():
    usernames = usernamesArray
    response = supabase.table('premium_accounts').select(
        '*').in_('username', usernames).execute()
    supabase_data = response.data
    servers = {}

    for index, supabase_record in enumerate(supabase_data):
        try:
            username = supabase_record.get('username')
            server_id = usernamesArray.index(username) + 1
            contact = supabase_record.get('contact-email', '')
            created_at = supabase_record.get('created_at', '')
            expiry = supabase_record.get('expiry_in_days', '')

            print("username in get_servers", username)
            print("server_id in get_servers", server_id)

            res = cmd.cmds["get_quota_info"](
                initialized_clients.get(server_id))
            print("res in get_servers", res)

            limit = res.get('quota', {}).get('limit', 0)
            drive_used = res.get('quota', {}).get('usage', 0)

            server_details = generate_server_details(
                server_id, contact, expiry, limit, drive_used, created_at)
            servers[server_id] = server_details

        except Exception as e:
            print("Error processing server {} for username {}: {}".format(
                server_id, username, str(e)))
    return jsonify(servers)


# @app.route('/insert_premium_account', methods=['POST'])
# def insert_premium_account():
#     try:
#         # Extract data from request
#         data = request.json
#         username = data.get('username')
#         password = data.get('password')
#         pikpak_plus_email = data.get('email')
#         contact_email = data.get('contactInfo')
#         expiry_in_days = data.get('expiry')

#         # Insert data into Supabase table
#         response = supabase.table('premium_accounts').insert([
#             {
#                 'username': username,
#                 'password': password,
#                 'pikpak-plus-email': pikpak_plus_email,
#                 'contact-email': contact_email,
#                 'expiry_in_days': expiry_in_days,
#                 'isDonated': True  # Default value
#             }
#         ]).execute()
#         return jsonify({'message': 'Premium account inserted successfully'}), 200
#     except Exception as e:
#         return jsonify({'error': str(e)}), 500

#  shortner
# def shorten_url(long_url):
#     long_url_encoded = urlencode({'url': long_url})
#     # api_url = f"https://vnshortener.com/api?api={VNSHORTENER_KEY}&{long_url_encoded}"
#     # api_url = f"https://shrinkforearn.in/api?api={shrinkforearn_KEY}&{long_url_encoded}"
#     api_url = f"https://clks.pro/api?api={CLKS_PRO_KEY}&{long_url_encoded}"

#     try:
#         response = requests.get(api_url)
#         response.raise_for_status()
#         result = response.json()
#         if result.get("status") == "error":
#             return result["message"], 500
#         else:
#             return result["shortenedUrl"], 200
#     except requests.exceptions.RequestException as e:
#         return f"Error: {e}", 500

# @app.route('/create-reward', methods=['POST'])
# def create_reward():
#     data = request.json
#     email = data.get('email')
#     # return jsonify({'error': 'rewards not available, please clear your browser cache'}), 400
#     if not email:
#         return jsonify({'error': 'Email is required'}), 400

#     try:
#         # Check if there is an existing non-redeemed reward for this email
#         response = supabase.table('rewards').select('unique_id, shortened_url').eq('email', email).eq('is_redeemed', False).execute()

#         rewards = response.data

#         if rewards:
#             # Return the existing non-redeemed reward
#             return jsonify({'shortenedUrl': rewards[0]['shortened_url']})

#         # If no non-redeemed reward exists, create a new one
#         timestamp = int(time.time())  # Use a timestamp as a unique ID
#         long_url = f"http://pikpak-plus.com/rewards?id={timestamp}"

#         short_url, status = shorten_url(long_url)
#         if status != 200:
#             return jsonify({'error': short_url}), status

#         # Store in the rewards table
#         response = supabase.table('rewards').insert({
#             'email': email,
#             'unique_id': str(timestamp),
#             'shortened_url': short_url,
#             'is_redeemed': False
#         }).execute()

#         return jsonify({'shortenedUrl': short_url})

#     except Exception as e:
#         # Handle any potential errors that might occur during the process
#         return jsonify({'error': str(e)}), 500  # Return a generic error message with status code 500


# @app.route('/redeem-reward', methods=['POST'])
# def redeem_reward():
#     data = request.json
#     reward_id = data.get('id')
#     email = data.get('email')
#     # return jsonify({'error': 'rewards not available, please clear your browser cache'}), 400

#     if not reward_id or not email:
#         return jsonify({'error': 'ID and email are required'}), 400

#     try:
#         # Check if the reward exists and is not yet redeemed
#         response = supabase.table('rewards').select('id, is_redeemed').eq('unique_id', reward_id).eq('email', email).single().execute()

#         reward = response.data
#         print(reward)
#         print(reward['is_redeemed'])
#         if reward['is_redeemed']:
#             return jsonify({'error': 'Reward already redeemed'}), 400

#         # Update the reward to mark it as redeemed
#         update_response = supabase.table('rewards').update({'is_redeemed': True}).eq('id', reward['id']).execute()


#         # Update the user's coin count
#         user_response = supabase.table('pikpak_data').select('coins').eq('email', email).single().execute()

#         if user_response.data is None:
#             return jsonify({'error': 'User not found'}), 404

#         user = user_response.data
#         new_coin_count = user['coins'] + 1  # Adjust the coin increment as needed

#         coin_update_response = supabase.table('pikpak_data').update({'coins': new_coin_count}).eq('email', email).execute()

#         return jsonify({'message': 'Reward redeemed successfully', 'new_coin_count': new_coin_count})

#     except Exception as e:
#         # Handle any potential errors that might occur during the process
#         return jsonify({'error': str(e)}), 500  # Return a generic error message with status code 500


# def are_redeemed_rows_available(email):
#     try:
#         # Get today's date
#         today = date.today()

#         # Query the database to count rows for the given email created today and are redeemed
#         response = (
#             supabase
#             .table('rewards')  # Replace 'records' with your actual table name
#             .select('id')  # We only need to count the IDs
#             .eq('email', email)
#             .eq('is_redeemed', True)
#             .gte('created_at', today.strftime('%Y-%m-%d'))
#             .lt('created_at', (today + timedelta(days=1)).strftime('%Y-%m-%d'))
#             .execute()
#         )

#         # Get the data and count the number of rows
#         rows = response.data
#         row_count = len(rows)

#         # Check if there are 10 or more rows
#         if row_count >= 10:
#             return True, row_count
#         else:
#             return False, row_count

#     except Exception as e:
#         # Log the error or handle it as appropriate
#         print(f"Error occurred: {str(e)}")
#         return False, 0

# -------------------------------------------------------------------------------------------------------------

@app.route('/ping', methods=['GET'])
def ping():
    try:
        for server_number in range(1, len(usernamesArray) + 1):
            initialize_client_route(server_number)
        return {"message": "pong"}
    except Exception as e:
        return jsonify({"error": str(e)}), 500


def get_directory_id(email):
    try:
        response = supabase.table('pikpak_data').select(
            'server_info', 'coins').eq('email', email).execute()
        data = response.data

        if data:
            directory_id = data[0]['server_info']
            # coins = data[0]['coins']
            return {'directory_id': directory_id
                    # ,
                    # 'coins': coins
                    }
        else:
            return {'error': 'Email not found'}
    except Exception as e:
        return {'error': str(e)}


@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        data = None
        req = request.get_json()
        email = req.get("email")
        password = req.get("password")
        try:
            if email and password:
                data = supabase.auth.sign_in_with_password(
                    {'email': email, 'password': password})
                response_data = get_directory_id(email)

                # switchserver(email)
                premium_status = check_premium_status(email)

                response = jsonify({'redirect': '/create', "dir": response_data['directory_id'],
                                    "premium_status": premium_status,
                                    # "coins":response_data['coins'],
                                    "auth": data.session.access_token})
                supabase.auth.sign_out()
                return response
            else:
                return jsonify({"error": "provide email and passowrd"}), 401
        except AuthApiError:
            return jsonify({"error": "have u signed up?"}), 401


@app.route("/signup", methods=["GET", "POST"])
def signup():
    if request.method == "POST":
        req = request.get_json()
        email = req.get("email")
        if not re.match(r'[\w\.-]+@(gmail|qq)\.com$', email):
            return jsonify({"error": "email should be either gmail or qq"}), 401
        password = req.get("password")
        try:
            user = supabase.auth.sign_up(
                {"email": email, "password": password})
            res = create_folder(email)

            return jsonify({'result': 'success signUp'}), 200
        except AuthApiError:
            return jsonify({"error": "signUp failed try again"}), 401


@app.route('/logout', methods=["GET", "POST"])
def logout():
    response = jsonify({'redirect': '/login'})
    # response.delete_cookie('auth')
    supabase.auth.sign_out()
    return response

# ---------- torrent api ----------------


@app.route('/searchFields', methods=['GET', 'POST'])
def searchFields():
    return apiscrape.indexerList()


@app.route('/search', methods=['GET', 'POST'])
def searchform():
    if request.method == "POST":
        req = request.get_json()
        query = req.get("query")
        categoryList = req.get("categoryList")
        indexerList = req.get("indexerList")

        df = apiscrape.searchQuery(query, categoryList, indexerList)

        if df is not "Empty":
            json_data = df.to_json(orient='records')
            return json_data
        return "No results found"

# ---------- torret api end -------------


if __name__ == "__main__":
    app.run(debug=True, host="0.0.0.0", port=5000)

{"encodings": ["src\\App.tsx", "App", [44, 22, 124, 1], [45, 19, 45, 66], [54, 20, 54, 34], [66, 2, 66, 24], [69, 4, 83, 6], [73, 10, 75, 21], [73, 30, 73, 48], [77, 10, 77, 35], [79, 10, 79, 34], [87, 4, 90, 19], [88, 6, 88, 43], [89, 6, 89, 34], [95, 6, 97, 16], [96, 8, 96, 63], [98, 6, 121, 16], [99, 8, 112, 26], [100, 10, 100, 50], [102, 10, 102, 51], [103, 10, 103, 59], [114, 8, 120, 20], "renderTabButton", [86, 26, 91, 3], "src\\components\\LoadingSpinner\\LoadingSpinner.tsx", "LoadingSpinner", [4, 33, 6, 1], [5, 9, 5, 48], "src\\components\\payment-card\\PaymentCard.tsx", "PaymentCard", [33, 0, 534, 1], [34, 30, 34, 45], [38, 6, 44, 12], [39, 8, 39, 46], [41, 8, 43, 18], [55, 4, 532, 17], [56, 6, 84, 12], [57, 8, 83, 10], [70, 12, 81, 24], [76, 14, 78, 24], [77, 16, 77, 38], [80, 14, 80, 58], [85, 6, 102, 12], [93, 8, 101, 18], [103, 6, 170, 12], [106, 8, 140, 14], [107, 10, 139, 12], [109, 14, 116, 24], [115, 16, 115, 33], [126, 14, 137, 26], [132, 16, 132, 46], [134, 16, 136, 26], [135, 18, 135, 46], [141, 8, 169, 14], [142, 10, 168, 12], [154, 16, 165, 28], [160, 18, 160, 48], [162, 18, 164, 28], [163, 20, 163, 49], [172, 6, 480, 12], [181, 8, 189, 18], [251, 8, 263, 18], [261, 10, 261, 67], [264, 8, 312, 14], [272, 10, 311, 16], [283, 12, 296, 23], [291, 14, 295, 25], [297, 12, 310, 24], [303, 14, 309, 25], [313, 8, 354, 14], [324, 10, 337, 21], [332, 12, 336, 23], [338, 10, 353, 22], [346, 12, 352, 23], [356, 8, 365, 18], [366, 8, 374, 10], [375, 8, 383, 20], [384, 8, 426, 14], [395, 10, 411, 21], [402, 12, 410, 14], [412, 10, 425, 22], [418, 12, 424, 23], [427, 8, 436, 18], [437, 8, 479, 14], [448, 10, 464, 21], [455, 12, 463, 14], [465, 10, 478, 22], [471, 12, 477, 23], [481, 6, 531, 8], [487, 12, 500, 17], [488, 14, 490, 19], [491, 14, 495, 19], [496, 14, 499, 19], [501, 12, 522, 18], [508, 14, 514, 26], [515, 14, 521, 26], "renderCardList", [36, 25, 46, 3], "src\\components\\FaqPage\\FaqPage.tsx", "FaqPage", [143, 0, 160, 1], [146, 6, 157, 19], [147, 8, 156, 14], [148, 10, 150, 20], [149, 12, 149, 47], [151, 10, 155, 30], [153, 14, 153, 54], "src\\components\\AccordionGroup\\AccordionGroup.tsx", "AccordionGroup", [10, 0, 36, 1], [10, 24, 10, 75], [13, 6, 33, 21], [14, 8, 16, 18], [15, 10, 15, 38], [18, 8, 32, 14], [20, 12, 26, 22], [28, 12, 30, 19], "src\\components\\HelperCard\\HelperCard.tsx", "HelperCard", [21, 0, 53, 1], [21, 20, 29, 18], [31, 4, 51, 10], [32, 6, 50, 16], [33, 8, 48, 24], [34, 10, 44, 25], [43, 12, 43, 35], [45, 10, 47, 28], [49, 24, 49, 70], [45, 19, 45, 65], [41, 22, 121, 1], [42, 19, 42, 65], [51, 20, 51, 34], [63, 2, 63, 24], [66, 4, 80, 6], [70, 10, 72, 21], [70, 30, 70, 48], [74, 10, 74, 35], [76, 10, 76, 34], [84, 4, 87, 19], [85, 6, 85, 43], [86, 6, 86, 34], [92, 6, 94, 16], [93, 8, 93, 63], [95, 6, 118, 16], [96, 8, 96, 48], [98, 8, 98, 49], [99, 8, 99, 57], [100, 8, 109, 26], [111, 8, 117, 20], [83, 26, 88, 3], [43, 22, 123, 1], [44, 19, 44, 65], [68, 4, 82, 6], [72, 10, 74, 21], [72, 30, 72, 48], [76, 10, 76, 35], [78, 10, 78, 34], [86, 4, 89, 19], [87, 6, 87, 43], [88, 6, 88, 34], [94, 6, 96, 16], [95, 8, 95, 63], [97, 6, 120, 16], [98, 8, 98, 48], [100, 8, 100, 49], [101, 8, 101, 57], [102, 8, 111, 26], [113, 8, 119, 20], [85, 26, 90, 3], [46, 22, 126, 1], [47, 19, 47, 65], [71, 4, 85, 6], [75, 10, 77, 21], [75, 30, 75, 48], [79, 10, 79, 35], [81, 10, 81, 34], [89, 4, 92, 19], [90, 6, 90, 43], [91, 6, 91, 34], [97, 6, 99, 16], [98, 8, 98, 63], [100, 6, 123, 16], [101, 8, 114, 26], [102, 10, 102, 50], [104, 10, 104, 51], [105, 10, 105, 59], [116, 8, 122, 20], [88, 26, 93, 3], "src\\components\\authentication\\login\\Login.tsx", "<PERSON><PERSON>", [16, 0, 130, 1], [17, 36, 20, 17], [21, 32, 21, 47], [117, 6, 120, 22], [118, 8, 118, 23], [119, 8, 119, 43], [121, 6, 127, 8], "src\\components\\BlockUiLoader\\BlockUiLoader.tsx", "BlockUiLoader", [24, 51, 39, 1], [24, 52, 28, 1], [29, 2, 38, 12], "src\\components\\authentication\\login\\LoginCard.tsx\\LoginCard.tsx", "LoginCard", [3, 15, 13, 1], [3, 34, 3, 73], [6, 6, 10, 8], "src\\components\\authentication\\AuthButtons\\AuthButtons.tsx", "AuthButtons", [5, 0, 56, 1], [8, 6, 18, 12], [9, 8, 17, 23], [19, 6, 53, 12], [45, 8, 48, 20], [47, 10, 47, 40], [49, 8, 52, 20], [51, 10, 51, 40], "src\\components\\authentication\\AuthCard\\AuthCard.tsx", "AuthCard", [29, 38, 135, 1], [29, 39, 33, 12], [34, 19, 34, 52], [35, 22, 35, 55], [36, 36, 39, 17], [74, 4, 133, 10], [75, 6, 75, 14], [76, 6, 132, 16], [77, 8, 83, 24], [78, 10, 81, 25], [80, 12, 80, 75], [82, 10, 82, 67], [85, 8, 123, 15], [85, 24, 85, 38], [86, 10, 122, 27], [87, 12, 94, 14], [95, 12, 102, 14], [103, 12, 110, 24], [111, 12, 121, 24], [119, 14, 119, 43], [120, 14, 120, 53], [125, 8, 131, 10], [6, 25, 61, 1], [9, 6, 19, 12], [10, 8, 18, 23], [20, 6, 58, 12], [46, 8, 53, 20], [52, 10, 52, 40], [54, 8, 57, 20], [56, 10, 56, 40], [29, 38, 141, 1], [73, 23, 73, 50], [74, 34, 77, 3], [80, 4, 139, 10], [82, 6, 138, 16], [83, 8, 89, 24], [84, 10, 87, 25], [86, 12, 86, 75], [91, 8, 129, 15], [91, 24, 91, 38], [92, 10, 128, 27], [93, 12, 100, 14], [101, 12, 108, 14], [109, 12, 116, 24], [117, 12, 127, 24], [125, 14, 125, 43], [126, 14, 126, 53], [131, 8, 137, 10], [29, 38, 148, 1], [79, 33, 94, 3], [93, 5, 93, 23], [93, 25, 93, 39], [97, 4, 146, 10], [99, 6, 145, 16], [100, 8, 106, 24], [101, 10, 104, 25], [103, 12, 103, 73], [108, 8, 136, 15], [108, 24, 108, 38], [109, 10, 135, 27], [110, 12, 117, 14], [118, 12, 125, 14], [126, 12, 133, 24], [138, 8, 144, 10], "src\\components\\authentication\\signUp\\SignUp.tsx", "SignUp", [8, 0, 71, 1], [9, 36, 12, 17], [13, 32, 13, 47], [56, 6, 60, 22], [57, 6, 57, 21], [59, 8, 59, 44], [62, 6, 68, 8], "src\\components\\authentication\\signUp\\SignUpCard\\SignUpCard.tsx", "SignUpCard", [3, 35, 3, 74], [24, 51, 43, 3], [29, 2, 43, 3], [42, 5, 42, 12], [42, 14, 42, 21], [15, 0, 120, 1], [16, 36, 19, 17], [20, 44, 20, 59], [21, 42, 21, 57], [100, 6, 110, 22], [108, 8, 108, 23], [109, 8, 109, 43], [111, 6, 117, 8], [42, 5, 42, 13], [42, 15, 42, 22], [42, 24, 42, 31], [30, 38, 184, 1], [30, 39, 34, 12], [35, 19, 35, 52], [36, 22, 36, 55], [37, 36, 40, 17], [84, 23, 84, 50], [85, 34, 88, 3], [90, 33, 105, 3], [104, 5, 104, 23], [104, 25, 104, 39], [108, 4, 182, 10], [110, 6, 181, 16], [111, 8, 117, 24], [112, 10, 115, 25], [114, 12, 114, 73], [120, 8, 139, 25], [121, 10, 138, 16], [131, 12, 131, 61], [132, 12, 137, 22], [133, 14, 136, 18], [134, 16, 134, 64], [141, 8, 172, 15], [141, 24, 141, 38], [142, 10, 171, 27], [143, 12, 151, 14], [152, 12, 160, 14], [161, 12, 169, 24], [174, 8, 180, 10], [112, 8, 131, 25], [113, 10, 130, 16], [123, 12, 123, 61], [124, 12, 129, 22], [125, 14, 128, 18], [126, 16, 126, 64], [132, 4, 181, 16], [133, 8, 139, 24], [134, 10, 137, 25], [136, 12, 136, 73], [4, 0, 11, 1], [8, 6, 8, 31], "src\\components\\authentication\\login\\DiscontinuationNotice.tsx", "DiscontinuationNotice", [16, 40, 82, 1], [17, 23, 17, 50], [20, 4, 80, 10], [24, 6, 79, 16], [25, 8, 30, 24], [26, 10, 29, 25], [28, 12, 28, 84], [32, 8, 78, 25], [33, 10, 68, 16], [40, 12, 67, 22], [41, 14, 43, 19], [44, 14, 47, 18], [46, 16, 46, 82], [49, 14, 49, 94], [50, 14, 54, 19], [51, 16, 51, 170], [51, 20, 51, 68], [52, 16, 52, 151], [52, 20, 52, 58], [53, 16, 53, 179], [53, 20, 53, 60], [56, 14, 56, 88], [57, 14, 61, 19], [58, 16, 58, 115], [58, 23, 58, 57], [59, 16, 59, 140], [59, 23, 59, 52], [60, 16, 60, 182], [60, 23, 60, 54], [63, 14, 66, 18], [70, 10, 77, 22], [2, 0, 8, 1], [5, 6, 5, 21], [3, 0, 9, 1], [6, 6, 6, 21], [4, 15, 16, 1], [4, 34, 4, 73], [12, 6, 12, 31], [16, 40, 65, 1], [20, 4, 63, 10], [24, 6, 62, 16], [32, 8, 61, 25], [33, 10, 51, 16], [40, 12, 50, 22], [44, 14, 46, 18], [47, 14, 49, 18], [53, 10, 60, 22], [16, 40, 86, 1], [20, 4, 84, 10], [24, 6, 83, 16], [25, 8, 34, 24], [26, 10, 33, 25], [28, 12, 32, 23], [36, 8, 82, 25], [37, 10, 77, 16], [46, 12, 76, 22], [47, 14, 55, 19], [56, 14, 65, 18], [66, 14, 75, 18], [79, 10, 81, 22], [16, 40, 83, 1], [20, 4, 81, 10], [24, 6, 80, 16], [36, 8, 79, 25], [15, 40, 81, 1], [16, 23, 16, 50], [19, 4, 79, 10], [23, 6, 78, 16], [24, 8, 33, 24], [25, 10, 32, 25], [27, 12, 31, 23], [35, 8, 77, 25], [36, 10, 76, 16], [45, 12, 75, 22], [46, 14, 54, 19], [55, 14, 64, 18], [65, 14, 74, 18], [3, 15, 15, 1], [11, 6, 11, 31], [3, 34, 3, 43], [5, 15, 16, 1], [5, 35, 5, 74], [13, 6, 13, 31], "src\\components\\authentication\\temp\\TempLogin.tsx", "TempLogin", [14, 0, 100, 1], [15, 36, 18, 17], [19, 44, 19, 59], [20, 42, 20, 57], [80, 6, 90, 22], [88, 8, 88, 23], [89, 8, 89, 47], [91, 6, 97, 8]], "reactData": {"src\\App.tsx": {"components": {"App": {"loc": [46, 22, 126, 1], "nameLoc": [46, 6, 46, 19], "captures": [{"loc": [46, 22, 126, 1], "kind": 512}, {"loc": [47, 19, 47, 65], "kind": 128}, {"loc": [47, 19, 47, 65], "kind": 1}, {"loc": [75, 30, 75, 48], "kind": 16}, {"loc": [89, 4, 92, 19], "kind": 16}, {"loc": [90, 6, 90, 43], "kind": 16}, {"loc": [91, 6, 91, 34], "kind": 16}, {"loc": [97, 6, 99, 16], "kind": 16}, {"loc": [100, 6, 123, 16], "kind": 16}, {"loc": [101, 8, 114, 26], "kind": 16}, {"loc": [116, 8, 122, 20], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 6, 14]}, {"kind": "import", "loc": [7, 0, 15, 21]}, {"kind": "import", "loc": [16, 0, 22, 23]}, {"kind": "import", "loc": [23, 0, 23, 46]}, {"kind": "import", "loc": [24, 0, 24, 61]}, {"kind": "import", "loc": [25, 0, 25, 71]}, {"kind": "import", "loc": [27, 0, 27, 18]}, {"kind": "import", "loc": [28, 0, 28, 50]}, {"kind": "import", "loc": [31, 0, 31, 63]}, {"kind": "export", "loc": [128, 0, 128, 18]}]}, "src\\components\\LoadingSpinner\\LoadingSpinner.tsx": {"components": {"LoadingSpinner": {"loc": [4, 33, 6, 1], "nameLoc": [4, 6, 4, 30], "captures": [{"loc": [4, 33, 6, 1], "kind": 512}, {"loc": [5, 9, 5, 48], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 1, 26]}, {"kind": "import", "loc": [2, 0, 2, 46]}, {"kind": "export", "loc": [8, 0, 8, 30]}]}, "src\\components\\payment-card\\PaymentCard.tsx": {"components": {"PaymentCard": {"loc": [33, 0, 534, 1], "nameLoc": [33, 9, 33, 20], "captures": [{"loc": [33, 0, 534, 1], "kind": 512}, {"loc": [34, 30, 34, 45], "kind": 4}, {"loc": [39, 8, 39, 46], "kind": 16}, {"loc": [41, 8, 43, 18], "kind": 16}, {"loc": [55, 4, 532, 17], "kind": 16}, {"loc": [57, 8, 83, 10], "kind": 16}, {"loc": [70, 12, 81, 24], "kind": 16}, {"loc": [76, 14, 78, 24], "kind": 16}, {"loc": [80, 14, 80, 58], "kind": 16}, {"loc": [93, 8, 101, 18], "kind": 16}, {"loc": [107, 10, 139, 12], "kind": 16}, {"loc": [109, 14, 116, 24], "kind": 16}, {"loc": [126, 14, 137, 26], "kind": 16}, {"loc": [132, 16, 132, 46], "kind": 16}, {"loc": [134, 16, 136, 26], "kind": 16}, {"loc": [142, 10, 168, 12], "kind": 16}, {"loc": [154, 16, 165, 28], "kind": 16}, {"loc": [160, 18, 160, 48], "kind": 16}, {"loc": [162, 18, 164, 28], "kind": 16}, {"loc": [181, 8, 189, 18], "kind": 16}, {"loc": [251, 8, 263, 18], "kind": 16}, {"loc": [261, 10, 261, 67], "kind": 16}, {"loc": [283, 12, 296, 23], "kind": 16}, {"loc": [291, 14, 295, 25], "kind": 16}, {"loc": [297, 12, 310, 24], "kind": 16}, {"loc": [303, 14, 309, 25], "kind": 16}, {"loc": [324, 10, 337, 21], "kind": 16}, {"loc": [332, 12, 336, 23], "kind": 16}, {"loc": [338, 10, 353, 22], "kind": 16}, {"loc": [346, 12, 352, 23], "kind": 16}, {"loc": [356, 8, 365, 18], "kind": 16}, {"loc": [366, 8, 374, 10], "kind": 16}, {"loc": [375, 8, 383, 20], "kind": 16}, {"loc": [395, 10, 411, 21], "kind": 16}, {"loc": [402, 12, 410, 14], "kind": 16}, {"loc": [412, 10, 425, 22], "kind": 16}, {"loc": [418, 12, 424, 23], "kind": 16}, {"loc": [427, 8, 436, 18], "kind": 16}, {"loc": [448, 10, 464, 21], "kind": 16}, {"loc": [455, 12, 463, 14], "kind": 16}, {"loc": [465, 10, 478, 22], "kind": 16}, {"loc": [471, 12, 477, 23], "kind": 16}, {"loc": [481, 6, 531, 8], "kind": 16}, {"loc": [508, 14, 514, 26], "kind": 16}, {"loc": [515, 14, 521, 26], "kind": 16}]}, "renderCardList": {"loc": [36, 25, 46, 3], "nameLoc": [36, 8, 36, 22], "captures": [{"loc": [39, 8, 39, 46], "kind": 16}, {"loc": [41, 8, 43, 18], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 8, 21]}, {"kind": "import", "loc": [9, 0, 21, 23]}, {"kind": "import", "loc": [22, 0, 22, 32]}, {"kind": "import", "loc": [23, 0, 23, 56]}, {"kind": "import", "loc": [24, 0, 24, 49]}, {"kind": "import", "loc": [25, 0, 25, 67]}, {"kind": "import", "loc": [26, 0, 26, 53]}, {"kind": "import", "loc": [27, 0, 31, 34]}, {"kind": "export", "loc": [536, 0, 536, 26]}]}, "src\\components\\FaqPage\\FaqPage.tsx": {"components": {"FaqPage": {"loc": [143, 0, 160, 1], "nameLoc": [143, 9, 143, 16], "captures": [{"loc": [143, 0, 160, 1], "kind": 512}, {"loc": [146, 6, 157, 19], "kind": 16}, {"loc": [148, 10, 150, 20], "kind": 16}, {"loc": [151, 10, 155, 30], "kind": 16}, {"loc": [153, 14, 153, 54], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 1, 69]}, {"kind": "import", "loc": [2, 0, 2, 22]}, {"kind": "import", "loc": [3, 0, 3, 61]}, {"kind": "export", "loc": [162, 0, 162, 22]}]}, "src\\components\\AccordionGroup\\AccordionGroup.tsx": {"components": {"AccordionGroup": {"loc": [10, 0, 36, 1], "nameLoc": [10, 9, 10, 23], "captures": [{"loc": [10, 0, 36, 1], "kind": 512}, {"loc": [10, 24, 10, 75], "kind": 2}, {"loc": [13, 6, 33, 21], "kind": 16}, {"loc": [14, 8, 16, 18], "kind": 16}, {"loc": [15, 10, 15, 38], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 1, 62]}, {"kind": "import", "loc": [2, 0, 2, 29]}, {"kind": "export", "loc": [37, 0, 37, 29]}]}, "src\\components\\HelperCard\\HelperCard.tsx": {"components": {"HelperCard": {"loc": [21, 0, 53, 1], "nameLoc": [21, 9, 21, 19], "captures": [{"loc": [21, 0, 53, 1], "kind": 512}, {"loc": [21, 20, 29, 18], "kind": 2}, {"loc": [32, 6, 50, 16], "kind": 16}, {"loc": [33, 8, 48, 24], "kind": 16}, {"loc": [34, 10, 44, 25], "kind": 16}, {"loc": [43, 12, 43, 35], "kind": 16}, {"loc": [45, 10, 47, 28], "kind": 16}, {"loc": [49, 24, 49, 70], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 8, 21]}, {"kind": "import", "loc": [9, 0, 9, 25]}, {"kind": "export", "loc": [55, 0, 55, 25]}]}, "src\\components\\authentication\\login\\Login.tsx": {"components": {"Login": {"loc": [15, 0, 120, 1], "nameLoc": [15, 9, 15, 14], "captures": [{"loc": [15, 0, 120, 1], "kind": 512}, {"loc": [16, 36, 19, 17], "kind": 4}, {"loc": [20, 44, 20, 59], "kind": 4}, {"loc": [21, 42, 21, 57], "kind": 4}, {"loc": [100, 6, 110, 22], "kind": 16}, {"loc": [108, 8, 108, 23], "kind": 16}, {"loc": [109, 8, 109, 43], "kind": 16}, {"loc": [111, 6, 117, 8], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 1, 32]}, {"kind": "import", "loc": [2, 0, 8, 33]}, {"kind": "import", "loc": [9, 0, 9, 49]}, {"kind": "import", "loc": [10, 0, 10, 39]}, {"kind": "import", "loc": [11, 0, 11, 61]}, {"kind": "import", "loc": [12, 0, 12, 52]}, {"kind": "import", "loc": [13, 0, 13, 59]}, {"kind": "export", "loc": [122, 0, 122, 20]}]}, "src\\components\\BlockUiLoader\\BlockUiLoader.tsx": {"components": {"BlockUiLoader": {"loc": [24, 51, 43, 3], "nameLoc": [24, 6, 24, 48], "captures": [{"loc": [24, 51, 43, 3], "kind": 512}, {"loc": [24, 52, 28, 1], "kind": 2}, {"loc": [29, 2, 43, 3], "kind": 128}, {"loc": [29, 2, 43, 3], "kind": 1}]}}, "externals": [{"kind": "import", "loc": [2, 0, 2, 38]}, {"kind": "import", "loc": [3, 0, 3, 40]}, {"kind": "import", "loc": [5, 0, 5, 42]}, {"kind": "export", "loc": [45, 0, 45, 28]}]}, "src\\components\\authentication\\login\\LoginCard.tsx\\LoginCard.tsx": {"components": {"LoginCard": {"loc": [4, 15, 16, 1], "nameLoc": [4, 24, 4, 33], "captures": [{"loc": [4, 15, 16, 1], "kind": 512}, {"loc": [4, 34, 4, 73], "kind": 2}, {"loc": [12, 6, 12, 31], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 1, 60]}, {"kind": "export", "loc": [4, 0, 16, 1]}]}, "src\\components\\authentication\\AuthButtons\\AuthButtons.tsx": {"components": {"AuthButtons": {"loc": [6, 25, 61, 1], "nameLoc": [6, 34, 6, 45], "captures": [{"loc": [6, 25, 61, 1], "kind": 512}, {"loc": [10, 8, 18, 23], "kind": 16}, {"loc": [46, 8, 53, 20], "kind": 16}, {"loc": [52, 10, 52, 40], "kind": 16}, {"loc": [54, 8, 57, 20], "kind": 16}, {"loc": [56, 10, 56, 40], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 1, 49]}, {"kind": "import", "loc": [2, 0, 2, 28]}, {"kind": "import", "loc": [3, 0, 3, 43]}, {"kind": "import", "loc": [4, 0, 4, 26]}, {"kind": "import", "loc": [5, 0, 5, 44]}, {"kind": "export", "loc": [63, 0, 63, 26]}]}, "src\\components\\authentication\\AuthCard\\AuthCard.tsx": {"components": {"AuthCard": {"loc": [30, 38, 184, 1], "nameLoc": [30, 6, 30, 35], "captures": [{"loc": [30, 38, 184, 1], "kind": 512}, {"loc": [30, 39, 34, 12], "kind": 2}, {"loc": [35, 19, 35, 52], "kind": 4}, {"loc": [36, 22, 36, 55], "kind": 4}, {"loc": [37, 36, 40, 17], "kind": 4}, {"loc": [84, 23, 84, 50], "kind": 128}, {"loc": [84, 23, 84, 50], "kind": 1}, {"loc": [85, 34, 88, 3], "kind": 128}, {"loc": [85, 34, 88, 3], "kind": 1}, {"loc": [90, 33, 105, 3], "kind": 128}, {"loc": [90, 33, 105, 3], "kind": 1}, {"loc": [112, 8, 131, 25], "kind": 16}, {"loc": [123, 12, 123, 61], "kind": 16}, {"loc": [124, 12, 129, 22], "kind": 16}, {"loc": [132, 4, 181, 16], "kind": 16}, {"loc": [133, 8, 139, 24], "kind": 16}, {"loc": [134, 10, 137, 25], "kind": 16}, {"loc": [136, 12, 136, 73], "kind": 16}, {"loc": [141, 24, 141, 38], "kind": 128}, {"loc": [142, 10, 171, 27], "kind": 16}, {"loc": [143, 12, 151, 14], "kind": 16}, {"loc": [152, 12, 160, 14], "kind": 16}, {"loc": [161, 12, 169, 24], "kind": 16}, {"loc": [174, 8, 180, 10], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 1, 56]}, {"kind": "import", "loc": [2, 0, 13, 21]}, {"kind": "import", "loc": [14, 0, 14, 23]}, {"kind": "import", "loc": [15, 0, 15, 82]}, {"kind": "import", "loc": [17, 0, 17, 73]}, {"kind": "export", "loc": [186, 0, 186, 23]}]}, "src\\components\\authentication\\signUp\\SignUp.tsx": {"components": {"SignUp": {"loc": [8, 0, 71, 1], "nameLoc": [8, 9, 8, 15], "captures": [{"loc": [8, 0, 71, 1], "kind": 512}, {"loc": [9, 36, 12, 17], "kind": 4}, {"loc": [13, 32, 13, 47], "kind": 4}, {"loc": [56, 6, 60, 22], "kind": 16}, {"loc": [57, 6, 57, 21], "kind": 16}, {"loc": [59, 8, 59, 44], "kind": 16}, {"loc": [62, 6, 68, 8], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 1, 32]}, {"kind": "import", "loc": [2, 0, 2, 48]}, {"kind": "import", "loc": [3, 0, 3, 39]}, {"kind": "import", "loc": [4, 0, 4, 61]}, {"kind": "import", "loc": [5, 0, 5, 54]}, {"kind": "import", "loc": [6, 0, 6, 52]}, {"kind": "export", "loc": [73, 0, 73, 21]}]}, "src\\components\\authentication\\signUp\\SignUpCard\\SignUpCard.tsx": {"components": {"SignUpCard": {"loc": [5, 15, 16, 1], "nameLoc": [5, 24, 5, 34], "captures": [{"loc": [5, 15, 16, 1], "kind": 512}, {"loc": [5, 35, 5, 74], "kind": 2}, {"loc": [13, 6, 13, 31], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [2, 0, 2, 69]}, {"kind": "export", "loc": [5, 0, 16, 1]}]}, "src\\components\\authentication\\login\\DiscontinuationNotice.tsx": {"components": {"DiscontinuationNotice": {"loc": [15, 40, 81, 1], "nameLoc": [15, 6, 15, 37], "captures": [{"loc": [15, 40, 81, 1], "kind": 512}, {"loc": [16, 23, 16, 50], "kind": 128}, {"loc": [16, 23, 16, 50], "kind": 1}, {"loc": [23, 6, 78, 16], "kind": 16}, {"loc": [24, 8, 33, 24], "kind": 16}, {"loc": [25, 10, 32, 25], "kind": 16}, {"loc": [27, 12, 31, 23], "kind": 16}, {"loc": [35, 8, 77, 25], "kind": 16}, {"loc": [45, 12, 75, 22], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 1, 38]}, {"kind": "import", "loc": [2, 0, 9, 21]}, {"kind": "import", "loc": [10, 0, 10, 33]}, {"kind": "import", "loc": [11, 0, 11, 47]}, {"kind": "import", "loc": [13, 0, 13, 73]}, {"kind": "export", "loc": [83, 0, 83, 36]}]}, "src\\components\\authentication\\temp\\TempLogin.tsx": {"components": {"TempLogin": {"loc": [14, 0, 100, 1], "nameLoc": [14, 9, 14, 18], "captures": [{"loc": [14, 0, 100, 1], "kind": 512}, {"loc": [15, 36, 18, 17], "kind": 4}, {"loc": [19, 44, 19, 59], "kind": 4}, {"loc": [20, 42, 20, 57], "kind": 4}, {"loc": [80, 6, 90, 22], "kind": 16}, {"loc": [88, 8, 88, 23], "kind": 16}, {"loc": [89, 8, 89, 47], "kind": 16}, {"loc": [91, 6, 97, 8], "kind": 16}]}}, "externals": [{"kind": "import", "loc": [1, 0, 1, 32]}, {"kind": "import", "loc": [2, 0, 7, 33]}, {"kind": "import", "loc": [8, 0, 8, 43]}, {"kind": "import", "loc": [9, 0, 9, 39]}, {"kind": "import", "loc": [10, 0, 10, 61]}, {"kind": "import", "loc": [11, 0, 11, 52]}, {"kind": "import", "loc": [12, 0, 12, 59]}, {"kind": "export", "loc": [102, 0, 102, 24]}]}}, "unusedFiles": ["D:/PROJECTS/private repos/pikpak-plus-private/pikpak-plus-client/src/helpers/helpers.ts", "D:/PROJECTS/private repos/pikpak-plus-private/pikpak-plus-client/src/constants/constants.ts"], "mtime": null}
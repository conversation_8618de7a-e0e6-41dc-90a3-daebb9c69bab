const __vite__fileDeps=["assets/web-Cw_ky3YK.js","assets/index-YQZYyPkh.js","assets/index-Dd_C_NnE.css","assets/prod-Dyxyokbu.js","assets/vidstack-Cs9WY57L-BXc9oK4Q.js","assets/CustomIonHeader-RqF7V-cv.js","assets/HelperCard-t_yvTUPv.js","assets/HelperCard-xinFkzDJ.css","assets/vidstack-Bswg46LY-DkFdumZQ.js","assets/vidstack-B9KOumdA-hXZRHia0.js","assets/vidstack-B4MOJc-J-B29ZAfIi.js","assets/vidstack-BTBUzdbF-Cao5mZMB.js","assets/vidstack-1WuajY3Z-CbJs2KuC.js","assets/vidstack-DscYSLiW-CA6XwpqT.js","assets/vidstack-Ci28C5f8-CTyhVKy_.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{t as a,a8 as nt,a9 as Un,aa as vd,ab as ie,j as M,a as Zr,b as Kr,K as Ql,I as Yl,ac as Cd,E as Xl,ad as wd,ae as Bo,r as qo,af as Ed,ag as $d,ah as Td,w as mn,l as Qr,L as $n,h as Jl,B as Sd,D as xd,ai as kd,k as Ho,q as Nr,aj as Pd,ak as Ad,c as Md,U as Ld,al as Nd,am as jo,an as Dd}from"./index-YQZYyPkh.js";import{C as Id}from"./CustomIonHeader-RqF7V-cv.js";import{H as Od}from"./HelperCard-t_yvTUPv.js";function Lt(){return Lt=Object.assign||function(s){for(var t=1;t<arguments.length;t++){var e=arguments[t];for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(s[i]=e[i])}return s},Lt.apply(this,arguments)}function zn(s,t){if(s==null)return{};var e,i,n={},r=Object.keys(s);for(i=0;i<r.length;i++)t.indexOf(e=r[i])>=0||(n[e]=s[e]);return n}function Ls(s){var t=a.useRef({fn:s,curr:void 0}).current;if(t.fn=s,!t.curr){var e=Object.create(null);Object.keys(s).forEach(function(i){e[i]=function(){var n;return(n=t.fn[i]).call.apply(n,[t.fn].concat([].slice.call(arguments)))}}),t.curr=e}return t.curr}function Nn(s){return a.useReducer(function(t,e){return Lt({},t,typeof e=="function"?e(t):e)},s)}var tc=a.createContext(void 0),Je=typeof window<"u"&&"ontouchstart"in window,Yr=function(s,t,e){return Math.max(Math.min(s,e),t)},gn=function(s,t,e){return t===void 0&&(t=0),e===void 0&&(e=0),Yr(s,1*(1-e),Math.max(6,t)*(1+e))},Xr=typeof window>"u"||/ServerSideRendering/.test(navigator&&navigator.userAgent)?a.useEffect:a.useLayoutEffect;function Ys(s,t,e){var i=a.useRef(t);i.current=t,a.useEffect(function(){function n(r){i.current(r)}return s&&window.addEventListener(s,n,e),function(){s&&window.removeEventListener(s,n)}},[s])}var Rd=["container"];function Vd(s){var t=s.container,e=t===void 0?document.body:t,i=zn(s,Rd);return Un.createPortal(nt.createElement("div",Lt({},i)),e)}function _d(s){return nt.createElement("svg",Lt({width:"44",height:"44",viewBox:"0 0 768 768"},s),nt.createElement("path",{d:"M607.5 205.5l-178.5 178.5 178.5 178.5-45 45-178.5-178.5-178.5 178.5-45-45 178.5-178.5-178.5-178.5 45-45 178.5 178.5 178.5-178.5z"}))}function Fd(s){return nt.createElement("svg",Lt({width:"44",height:"44",viewBox:"0 0 768 768"},s),nt.createElement("path",{d:"M640.5 352.5v63h-390l178.5 180-45 45-256.5-256.5 256.5-256.5 45 45-178.5 180h390z"}))}function Bd(s){return nt.createElement("svg",Lt({width:"44",height:"44",viewBox:"0 0 768 768"},s),nt.createElement("path",{d:"M384 127.5l256.5 256.5-256.5 256.5-45-45 178.5-180h-390v-63h390l-178.5-180z"}))}function qd(){return a.useEffect(function(){var s=document.body.style,t=s.overflow;return s.overflow="hidden",function(){s.overflow=t}},[]),null}function Go(s){var t=s.touches[0],e=t.clientX,i=t.clientY;if(s.touches.length>=2){var n=s.touches[1],r=n.clientX,o=n.clientY;return[(e+r)/2,(i+o)/2,Math.sqrt(Math.pow(r-e,2)+Math.pow(o-i,2))]}return[e,i,0]}var hs=function(s,t,e,i){var n=e*t,r=(n-i)/2,o=void 0,l=s;return n<=i?(o=1,l=0):s>0&&r-s<=0?(o=2,l=r):s<0&&r+s<=0&&(o=3,l=-r),[o,l]};function Dr(s,t,e,i,n,r,o,l,c,u){o===void 0&&(o=innerWidth/2),l===void 0&&(l=innerHeight/2),c===void 0&&(c=0),u===void 0&&(u=0);var h=hs(s,r,e,innerWidth)[0],d=hs(t,r,i,innerHeight),f=innerWidth/2,p=innerHeight/2;return{x:o-r/n*(o-(f+s))-f+(i/e>=3&&e*r===innerWidth?0:h?c/2:c),y:l-r/n*(l-(p+t))-p+(d[0]?u/2:u),lastCX:o,lastCY:l}}function Jr(s,t,e){var i=s%180!=0;return i?[e,t,i]:[t,e,i]}function Ir(s,t,e){var i=Jr(e,innerWidth,innerHeight),n=i[0],r=i[1],o=0,l=n,c=r,u=s/t*r,h=t/s*n;return s<n&&t<r?(l=s,c=t):s<n&&t>=r?l=u:s>=n&&t<r||s/t>n/r?c=h:t/s>=3&&!i[2]?o=((c=h)-r)/2:l=u,{width:l,height:c,x:0,y:o,pause:!0}}function bn(s,t){var e=t.leading,i=e!==void 0&&e,n=t.maxWait,r=t.wait,o=r===void 0?n||0:r,l=a.useRef(s);l.current=s;var c=a.useRef(0),u=a.useRef(),h=function(){return u.current&&clearTimeout(u.current)},d=a.useCallback(function(){var f=[].slice.call(arguments),p=Date.now();function g(){c.current=p,h(),l.current.apply(null,f)}var b=c.current,y=p-b;if(b===0&&(i&&g(),c.current=p),n!==void 0){if(y>n)return void g()}else y<o&&(c.current=p);h(),u.current=setTimeout(function(){g(),c.current=0},o)},[o,n,i]);return d.cancel=h,d}var Wo=function(s,t,e){return Ms(s,t,e,100,function(i){return i},function(){return Ms(t,s,e)})},Hd=function(s){return 1-Math.pow(1-s,4)};function Ms(s,t,e,i,n,r){i===void 0&&(i=400),n===void 0&&(n=Hd);var o=t-s;if(o!==0){var l=Date.now(),c=0,u=function(){var d=Math.min(1,(Date.now()-l)/i);e(s+n(d)*o)&&d<1?h():(cancelAnimationFrame(c),d>=1&&r&&r())};h()}function h(){c=requestAnimationFrame(u)}}var jd={T:0,L:0,W:0,H:0,FIT:void 0},ec=function(){var s=a.useRef(!1);return a.useEffect(function(){return s.current=!0,function(){s.current=!1}},[]),s},Gd=["className"];function Wd(s){var t=s.className,e=zn(s,Gd);return nt.createElement("div",Lt({className:"PhotoView__Spinner "+t},e),nt.createElement("svg",{viewBox:"0 0 32 32",width:"36",height:"36",fill:"white"},nt.createElement("path",{opacity:".25",d:"M16 0 A16 16 0 0 0 16 32 A16 16 0 0 0 16 0 M16 4 A12 12 0 0 1 16 28 A12 12 0 0 1 16 4"}),nt.createElement("path",{d:"M16 0 A16 16 0 0 1 32 16 L28 16 A12 12 0 0 0 16 4z"})))}var Ud=["src","loaded","broken","className","onPhotoLoad","loadingElement","brokenElement"];function zd(s){var t=s.src,e=s.loaded,i=s.broken,n=s.className,r=s.onPhotoLoad,o=s.loadingElement,l=s.brokenElement,c=zn(s,Ud),u=ec();return t&&!i?nt.createElement(nt.Fragment,null,nt.createElement("img",Lt({className:"PhotoView__Photo"+(n?" "+n:""),src:t,onLoad:function(h){var d=h.target;u.current&&r({loaded:!0,naturalWidth:d.naturalWidth,naturalHeight:d.naturalHeight})},onError:function(){u.current&&r({broken:!0})},alt:""},c)),!e&&(nt.createElement("span",{className:"PhotoView__icon"},o)||nt.createElement(Wd,{className:"PhotoView__icon"}))):l?nt.createElement("span",{className:"PhotoView__icon"},typeof l=="function"?l({src:t}):l):null}var Zd={naturalWidth:void 0,naturalHeight:void 0,width:void 0,height:void 0,loaded:void 0,broken:!1,x:0,y:0,touched:!1,maskTouched:!1,rotate:0,scale:1,CX:0,CY:0,lastX:0,lastY:0,lastCX:0,lastCY:0,lastScale:1,touchTime:0,touchLength:0,pause:!0,stopRaf:!0,reach:void 0};function Kd(s){var t=s.item,e=t.src,i=t.render,n=t.width,r=n===void 0?0:n,o=t.height,l=o===void 0?0:o,c=t.originRef,u=s.visible,h=s.speed,d=s.easing,f=s.wrapClassName,p=s.className,g=s.style,b=s.loadingElement,y=s.brokenElement,w=s.onPhotoTap,S=s.onMaskTap,N=s.onReachMove,j=s.onReachUp,V=s.onPhotoResize,K=s.isActive,ot=s.expose,C=Nn(Zd),k=C[0],_=C[1],St=a.useRef(0),gt=ec(),ct=k.naturalWidth,et=ct===void 0?r:ct,T=k.naturalHeight,x=T===void 0?l:T,D=k.width,st=D===void 0?r:D,At=k.height,Et=At===void 0?l:At,Rt=k.loaded,Vt=Rt===void 0?!e:Rt,_t=k.broken,$t=k.x,L=k.y,I=k.touched,U=k.stopRaf,vt=k.maskTouched,bt=k.rotate,ut=k.scale,Qt=k.CX,be=k.CY,ye=k.lastX,qe=k.lastY,yt=k.lastCX,ws=k.lastCY,ke=k.lastScale,Es=k.touchTime,on=k.touchLength,ve=k.pause,$s=k.reach,Ei=Ls({onScale:function(v){return He(gn(v))},onRotate:function(v){bt!==v&&(ot({rotate:v}),_(Lt({rotate:v},Ir(et,x,v))))}});function He(v,F,ft){ut!==v&&(ot({scale:v}),_(Lt({scale:v},Dr($t,L,st,Et,ut,v,F,ft),v<=1&&{x:0,y:0})))}var ln=bn(function(v,F,ft){if(ft===void 0&&(ft=0),(I||vt)&&K){var Ts=Jr(bt,st,Et),je=Ts[0],Ce=Ts[1];if(ft===0&&St.current===0){var Ft=Math.abs(v-Qt)<=20,Xt=Math.abs(F-be)<=20;if(Ft&&Xt)return void _({lastCX:v,lastCY:F});St.current=Ft?F>be?3:2:1}var Ss=v-yt,xs=F-ws,we=void 0;if(ft===0){var ks=hs(Ss+ye,ut,je,innerWidth)[0],Us=hs(xs+qe,ut,Ce,innerHeight);we=function(os,We,Ue,Me){return We&&os===1||Me==="x"?"x":Ue&&os>1||Me==="y"?"y":void 0}(St.current,ks,Us[0],$s),we!==void 0&&N(we,v,F,ut)}if(we==="x"||vt)return void _({reach:"x"});var Ge=gn(ut+(ft-on)/100/2*ut,et/st,.2);ot({scale:Ge}),_(Lt({touchLength:ft,reach:we,scale:Ge},Dr($t,L,st,Et,ut,Ge,v,F,Ss,xs)))}},{maxWait:8});function $i(v){return!U&&!I&&(gt.current&&_(Lt({},v,{pause:u})),gt.current)}var rs,Pe,as,Ti,cn,un,hn,Yt,dn=(cn=function(v){return $i({x:v})},un=function(v){return $i({y:v})},hn=function(v){return gt.current&&(ot({scale:v}),_({scale:v})),!I&&gt.current},Yt=Ls({X:function(v){return cn(v)},Y:function(v){return un(v)},S:function(v){return hn(v)}}),function(v,F,ft,Ts,je,Ce,Ft,Xt,Ss,xs,we){var ks=Jr(xs,je,Ce),Us=ks[0],Ge=ks[1],os=hs(v,Xt,Us,innerWidth),We=os[0],Ue=os[1],Me=hs(F,Xt,Ge,innerHeight),zs=Me[0],pn=Me[1],Zs=Date.now()-we;if(Zs>=200||Xt!=Ft||Math.abs(Ss-Ft)>1){var ls=Dr(v,F,je,Ce,Ft,Xt),ze=ls.x,Ps=ls.y,ae=We?Ue:ze!==v?ze:null,Ks=zs?pn:Ps!==F?Ps:null;return ae!==null&&Ms(v,ae,Yt.X),Ks!==null&&Ms(F,Ks,Yt.Y),void(Xt!=Ft&&Ms(Ft,Xt,Yt.S))}var As=(v-ft)/Zs,cs=(F-Ts)/Zs,us=Math.sqrt(Math.pow(As,2)+Math.pow(cs,2)),Ze=!1,Ke=!1;(function(Ee,Ht){var jt=Ee,Qe=0,$e=void 0,oe=0,Qs=function(xi){$e||($e=xi);var ki=xi-$e,Vo=Math.sign(Ee),_o=-.001*Vo,Fo=Math.sign(-jt)*Math.pow(jt,2)*2e-4,yd=jt*ki+(_o+Fo)*Math.pow(ki,2)/2;Qe+=yd,$e=xi,Vo*(jt+=(_o+Fo)*ki)<=0?le():Ht(Qe)?Le():le()};function Le(){oe=requestAnimationFrame(Qs)}function le(){cancelAnimationFrame(oe)}Le()})(us,function(Ee){var Ht=v+Ee*(As/us),jt=F+Ee*(cs/us),Qe=hs(Ht,Ft,Us,innerWidth),$e=Qe[0],oe=Qe[1],Qs=hs(jt,Ft,Ge,innerHeight),Le=Qs[0],le=Qs[1];if($e&&!Ze&&(Ze=!0,We?Ms(Ht,oe,Yt.X):Wo(oe,Ht+(Ht-oe),Yt.X)),Le&&!Ke&&(Ke=!0,zs?Ms(jt,le,Yt.Y):Wo(le,jt+(jt-le),Yt.Y)),Ze&&Ke)return!1;var xi=Ze||Yt.X(oe),ki=Ke||Yt.Y(le);return xi&&ki})}),Gs=(rs=w,Pe=function(v,F){$s||He(ut!==1?1:Math.max(2,et/st),v,F)},as=a.useRef(0),Ti=bn(function(){as.current=0,rs.apply(void 0,[].slice.call(arguments))},{wait:300}),function(){var v=[].slice.call(arguments);as.current+=1,Ti.apply(void 0,v),as.current>=2&&(Ti.cancel(),as.current=0,Pe.apply(void 0,v))});function Ws(v,F){if(St.current=0,(I||vt)&&K){_({touched:!1,maskTouched:!1,pause:!1,stopRaf:!1,reach:void 0});var ft=gn(ut,et/st);if(dn($t,L,ye,qe,st,Et,ut,ft,ke,bt,Es),j(v,F),Qt===v&&be===F){if(I)return void Gs(v,F);vt&&S(v,F)}}}function Si(v,F,ft){ft===void 0&&(ft=0),_({touched:!0,CX:v,CY:F,lastCX:v,lastCY:F,lastX:$t,lastY:L,lastScale:ut,touchLength:ft,touchTime:Date.now()})}function fn(v){_({maskTouched:!0,CX:v.clientX,CY:v.clientY,lastX:$t,lastY:L})}Ys(Je?void 0:"mousemove",function(v){v.preventDefault(),ln(v.clientX,v.clientY)}),Ys(Je?void 0:"mouseup",function(v){Ws(v.clientX,v.clientY)}),Ys(Je?"touchmove":void 0,function(v){v.preventDefault();var F=Go(v);ln.apply(void 0,F)},{passive:!1}),Ys(Je?"touchend":void 0,function(v){var F=v.changedTouches[0];Ws(F.clientX,F.clientY)},{passive:!1}),Ys("resize",bn(function(){Vt&&!I&&(_(Ir(et,x,bt)),V())},{maxWait:8})),Xr(function(){K&&ot(Lt({scale:ut,rotate:bt},Ei))},[K]);var Ae=function(v,F,ft,Ts,je,Ce,Ft,Xt,Ss,xs){var we=function(ze,Ps,ae,Ks,As){var cs=a.useRef(!1),us=Nn({lead:!0,scale:ae}),Ze=us[0],Ke=Ze.lead,Ee=Ze.scale,Ht=us[1],jt=bn(function(Qe){try{return As(!0),Ht({lead:!1,scale:Qe}),Promise.resolve()}catch($e){return Promise.reject($e)}},{wait:Ks});return Xr(function(){cs.current?(As(!1),Ht({lead:!0}),jt(ae)):cs.current=!0},[ae]),Ke?[ze*Ee,Ps*Ee,ae/Ee]:[ze*ae,Ps*ae,1]}(Ce,Ft,Xt,Ss,xs),ks=we[0],Us=we[1],Ge=we[2],os=function(ze,Ps,ae,Ks,As){var cs=a.useState(jd),us=cs[0],Ze=cs[1],Ke=a.useState(0),Ee=Ke[0],Ht=Ke[1],jt=a.useRef(),Qe=Ls({OK:function(){return ze&&Ht(4)}});function $e(oe){As(!1),Ht(oe)}return a.useEffect(function(){if(jt.current||(jt.current=Date.now()),ae){if(function(oe,Qs){var Le=oe&&oe.current;if(Le&&Le.nodeType===1){var le=Le.getBoundingClientRect();Qs({T:le.top,L:le.left,W:le.width,H:le.height,FIT:Le.tagName==="IMG"?getComputedStyle(Le).objectFit:void 0})}}(Ps,Ze),ze)return Date.now()-jt.current<250?(Ht(1),requestAnimationFrame(function(){Ht(2),requestAnimationFrame(function(){return $e(3)})}),void setTimeout(Qe.OK,Ks)):void Ht(4);$e(5)}},[ze,ae]),[Ee,us]}(v,F,ft,Ss,xs),We=os[0],Ue=os[1],Me=Ue.W,zs=Ue.FIT,pn=innerWidth/2,Zs=innerHeight/2,ls=We<3||We>4;return[ls?Me?Ue.L:pn:Ts+(pn-Ce*Xt/2),ls?Me?Ue.T:Zs:je+(Zs-Ft*Xt/2),ks,ls&&zs?ks*(Ue.H/Me):Us,We===0?Ge:ls?Me/(Ce*Xt)||.01:Ge,ls?zs?1:0:1,We,zs]}(u,c,Vt,$t,L,st,Et,ut,h,function(v){return _({pause:v})}),it=Ae[4],wt=Ae[6],Mt="transform "+h+"ms "+d,Pt={className:p,onMouseDown:Je?void 0:function(v){v.stopPropagation(),v.button===0&&Si(v.clientX,v.clientY,0)},onTouchStart:Je?function(v){v.stopPropagation(),Si.apply(void 0,Go(v))}:void 0,onWheel:function(v){if(!$s){var F=gn(ut-v.deltaY/100/2,et/st);_({stopRaf:!0}),He(F,v.clientX,v.clientY)}},style:{width:Ae[2],height:Ae[3],opacity:Ae[5],objectFit:wt===4?void 0:Ae[7],transform:bt?"rotate("+bt+"deg)":void 0,transition:wt>2?Mt+", opacity "+h+"ms ease, height "+(wt<4?h/2:wt>4?h:0)+"ms "+d:void 0}};return nt.createElement("div",{className:"PhotoView__PhotoWrap"+(f?" "+f:""),style:g,onMouseDown:!Je&&K?fn:void 0,onTouchStart:Je&&K?function(v){return fn(v.touches[0])}:void 0},nt.createElement("div",{className:"PhotoView__PhotoBox",style:{transform:"matrix("+it+", 0, 0, "+it+", "+Ae[0]+", "+Ae[1]+")",transition:I||ve?void 0:Mt,willChange:K?"transform":void 0}},e?nt.createElement(zd,Lt({src:e,loaded:Vt,broken:_t},Pt,{onPhotoLoad:function(v){_(Lt({},v,v.loaded&&Ir(v.naturalWidth||0,v.naturalHeight||0,bt)))},loadingElement:b,brokenElement:y})):i&&i({attrs:Pt,scale:it,rotate:bt})))}var Uo={x:0,touched:!1,pause:!1,lastCX:void 0,lastCY:void 0,bg:void 0,lastBg:void 0,overlay:!0,minimal:!0,scale:1,rotate:0};function Qd(s){var t=s.loop,e=t===void 0?3:t,i=s.speed,n=s.easing,r=s.photoClosable,o=s.maskClosable,l=o===void 0||o,c=s.maskOpacity,u=c===void 0?1:c,h=s.pullClosable,d=h===void 0||h,f=s.bannerVisible,p=f===void 0||f,g=s.overlayRender,b=s.toolbarRender,y=s.className,w=s.maskClassName,S=s.photoClassName,N=s.photoWrapClassName,j=s.loadingElement,V=s.brokenElement,K=s.images,ot=s.index,C=ot===void 0?0:ot,k=s.onIndexChange,_=s.visible,St=s.onClose,gt=s.afterClose,ct=s.portalContainer,et=Nn(Uo),T=et[0],x=et[1],D=a.useState(0),st=D[0],At=D[1],Et=T.x,Rt=T.touched,Vt=T.pause,_t=T.lastCX,$t=T.lastCY,L=T.bg,I=L===void 0?u:L,U=T.lastBg,vt=T.overlay,bt=T.minimal,ut=T.scale,Qt=T.rotate,be=T.onScale,ye=T.onRotate,qe=s.hasOwnProperty("index"),yt=qe?C:st,ws=qe?k:At,ke=a.useRef(yt),Es=K.length,on=K[yt],ve=typeof e=="boolean"?e:Es>e,$s=function(it,wt){var Mt=a.useReducer(function(ft){return!ft},!1)[1],Pt=a.useRef(0),v=function(ft,Ts){var je=a.useRef(ft);function Ce(Ft){je.current=Ft}return a.useMemo(function(){(function(Ft){it?(Ft(it),Pt.current=1):Pt.current=2})(Ce)},[ft]),[je.current,Ce]}(it),F=v[1];return[v[0],Pt.current,function(){Mt(),Pt.current===2&&(F(!1),wt&&wt()),Pt.current=0}]}(_,gt),Ei=$s[0],He=$s[1],ln=$s[2];Xr(function(){if(Ei)return x({pause:!0,x:yt*-(innerWidth+20)}),void(ke.current=yt);x(Uo)},[Ei]);var $i=Ls({close:function(it){ye&&ye(0),x({overlay:!0,lastBg:I}),St(it)},changeIndex:function(it,wt){wt===void 0&&(wt=!1);var Mt=ve?ke.current+(it-yt):it,Pt=Es-1,v=Yr(Mt,0,Pt),F=ve?Mt:v,ft=innerWidth+20;x({touched:!1,lastCX:void 0,lastCY:void 0,x:-ft*F,pause:wt}),ke.current=F,ws&&ws(ve?it<0?Pt:it>Pt?0:it:v)}}),rs=$i.close,Pe=$i.changeIndex;function as(it){return it?rs():x({overlay:!vt})}function Ti(){x({x:-(innerWidth+20)*yt,lastCX:void 0,lastCY:void 0,pause:!0}),ke.current=yt}function cn(it,wt,Mt,Pt){it==="x"?function(v){if(_t!==void 0){var F=v-_t,ft=F;!ve&&(yt===0&&F>0||yt===Es-1&&F<0)&&(ft=F/2),x({touched:!0,lastCX:_t,x:-(innerWidth+20)*ke.current+ft,pause:!1})}else x({touched:!0,lastCX:v,x:Et,pause:!1})}(wt):it==="y"&&function(v,F){if($t!==void 0){var ft=u===null?null:Yr(u,.01,u-Math.abs(v-$t)/100/4);x({touched:!0,lastCY:$t,bg:F===1?ft:u,minimal:F===1})}else x({touched:!0,lastCY:v,bg:I,minimal:!0})}(Mt,Pt)}function un(it,wt){var Mt=it-(_t??it),Pt=wt-($t??wt),v=!1;if(Mt<-40)Pe(yt+1);else if(Mt>40)Pe(yt-1);else{var F=-(innerWidth+20)*ke.current;Math.abs(Pt)>100&&bt&&d&&(v=!0,rs()),x({touched:!1,x:F,lastCX:void 0,lastCY:void 0,bg:u,overlay:!!v||vt})}}Ys("keydown",function(it){if(_)switch(it.key){case"ArrowLeft":Pe(yt-1,!0);break;case"ArrowRight":Pe(yt+1,!0);break;case"Escape":rs()}});var hn=function(it,wt,Mt){return a.useMemo(function(){var Pt=it.length;return Mt?it.concat(it).concat(it).slice(Pt+wt-1,Pt+wt+2):it.slice(Math.max(wt-1,0),Math.min(wt+2,Pt+1))},[it,wt,Mt])}(K,yt,ve);if(!Ei)return null;var Yt=vt&&!He,dn=_?I:U,Gs=be&&ye&&{images:K,index:yt,visible:_,onClose:rs,onIndexChange:Pe,overlayVisible:Yt,overlay:on&&on.overlay,scale:ut,rotate:Qt,onScale:be,onRotate:ye},Ws=i?i(He):400,Si=n?n(He):"cubic-bezier(0.25, 0.8, 0.25, 1)",fn=i?i(3):600,Ae=n?n(3):"cubic-bezier(0.25, 0.8, 0.25, 1)";return nt.createElement(Vd,{className:"PhotoView-Portal"+(Yt?"":" PhotoView-Slider__clean")+(_?"":" PhotoView-Slider__willClose")+(y?" "+y:""),role:"dialog",onClick:function(it){return it.stopPropagation()},container:ct},_&&nt.createElement(qd,null),nt.createElement("div",{className:"PhotoView-Slider__Backdrop"+(w?" "+w:"")+(He===1?" PhotoView-Slider__fadeIn":He===2?" PhotoView-Slider__fadeOut":""),style:{background:dn?"rgba(0, 0, 0, "+dn+")":void 0,transitionTimingFunction:Si,transitionDuration:(Rt?0:Ws)+"ms",animationDuration:Ws+"ms"},onAnimationEnd:ln}),p&&nt.createElement("div",{className:"PhotoView-Slider__BannerWrap"},nt.createElement("div",{className:"PhotoView-Slider__Counter"},yt+1," / ",Es),nt.createElement("div",{className:"PhotoView-Slider__BannerRight"},b&&Gs&&b(Gs),nt.createElement(_d,{className:"PhotoView-Slider__toolbarIcon",onClick:rs}))),hn.map(function(it,wt){var Mt=ve||yt!==0?ke.current-1+wt:yt+wt;return nt.createElement(Kd,{key:ve?it.key+"/"+it.src+"/"+Mt:it.key,item:it,speed:Ws,easing:Si,visible:_,onReachMove:cn,onReachUp:un,onPhotoTap:function(){return as(r)},onMaskTap:function(){return as(l)},wrapClassName:N,className:S,style:{left:(innerWidth+20)*Mt+"px",transform:"translate3d("+Et+"px, 0px, 0)",transition:Rt||Vt?void 0:"transform "+fn+"ms "+Ae},loadingElement:j,brokenElement:V,onPhotoResize:Ti,isActive:ke.current===Mt,expose:x})}),!Je&&p&&nt.createElement(nt.Fragment,null,(ve||yt!==0)&&nt.createElement("div",{className:"PhotoView-Slider__ArrowLeft",onClick:function(){return Pe(yt-1,!0)}},nt.createElement(Fd,null)),(ve||yt+1<Es)&&nt.createElement("div",{className:"PhotoView-Slider__ArrowRight",onClick:function(){return Pe(yt+1,!0)}},nt.createElement(Bd,null))),g&&Gs&&nt.createElement("div",{className:"PhotoView-Slider__Overlay"},g(Gs)))}var Yd=["children","onIndexChange","onVisibleChange"],Xd={images:[],visible:!1,index:0};function Jd(s){var t=s.children,e=s.onIndexChange,i=s.onVisibleChange,n=zn(s,Yd),r=Nn(Xd),o=r[0],l=r[1],c=a.useRef(0),u=o.images,h=o.visible,d=o.index,f=Ls({nextId:function(){return c.current+=1},update:function(b){var y=u.findIndex(function(S){return S.key===b.key});if(y>-1){var w=u.slice();return w.splice(y,1,b),void l({images:w})}l(function(S){return{images:S.images.concat(b)}})},remove:function(b){l(function(y){var w=y.images.filter(function(S){return S.key!==b});return{images:w,index:Math.min(w.length-1,d)}})},show:function(b){var y=u.findIndex(function(w){return w.key===b});l({visible:!0,index:y}),i&&i(!0,y,o)}}),p=Ls({close:function(){l({visible:!1}),i&&i(!1,d,o)},changeIndex:function(b){l({index:b}),e&&e(b,o)}}),g=a.useMemo(function(){return Lt({},o,f)},[o,f]);return nt.createElement(tc.Provider,{value:g},t,nt.createElement(Qd,Lt({images:u,visible:h,index:d,onIndexChange:p.changeIndex,onClose:p.close},n)))}var tf=function(s){var t,e,i=s.src,n=s.render,r=s.overlay,o=s.width,l=s.height,c=s.triggers,u=c===void 0?["onClick"]:c,h=s.children,d=a.useContext(tc),f=(t=function(){return d.nextId()},(e=a.useRef({sign:!1,fn:void 0}).current).sign||(e.sign=!0,e.fn=t()),e.fn),p=a.useRef(null);a.useImperativeHandle(h==null?void 0:h.ref,function(){return p.current}),a.useEffect(function(){return function(){d.remove(f)}},[]);var g=Ls({render:function(y){return n&&n(y)},show:function(y,w){d.show(f),function(S,N){if(h){var j=h.props[S];j&&j(N)}}(y,w)}}),b=a.useMemo(function(){var y={};return u.forEach(function(w){y[w]=g.show.bind(null,w)}),y},[]);return a.useEffect(function(){d.update({key:f,src:i,originRef:p,render:g.render,overlay:r,width:o,height:l})},[i]),h?a.Children.only(a.cloneElement(h,Lt({},b,{ref:p}))):null};const zo=vd("Share",{web:()=>ie(()=>import("./web-Cw_ky3YK.js"),__vite__mapDeps([0,1,2])).then(s=>new s.ShareWeb)}),ef=nt.memo(({color:s,icon:t,onClick:e,text:i})=>M.jsxs(Zr,{button:!0,detail:!1,onClick:e,children:[M.jsx(Kr,{slot:"start",color:s,icon:t}),i]})),sf=({item:s,setShowModal:t,setIsLoading:e,setShowVideoPlayer:i,setVideoDetails:n,scrollToTop:r,handleDeleteItem:o})=>{var _t,$t;const l=s==null?void 0:s.name,c=s==null?void 0:s.size,[u,h]=a.useState(!1),[d,f]=a.useState(""),[p,g]=a.useState(null),[b,y]=a.useState(null),w={share:{color:"success",header:"Share",message:"Do you want to proceed with sharing?",icon:wd},play:{color:"secondary",header:"Play",message:"Do you want to proceed with playing?",icon:Bo},playInAnotherApp:{color:"secondary",header:"Play in Another App",message:"Do you want to proceed with playing in another app?",icon:Bo},copyDownloadLink:{color:"tertiary",header:"Copy Download/Stream Link",message:`Are you sure you want to copy the download link for: ${l}?`,icon:qo},copyFileName:{color:"tertiary",header:"Copy File Name",message:`Are you sure you want to copy the file name: ${l}?`,icon:qo},download:{color:"warning",header:"Download to Device",message:"This will initiate the download process. Do you want to proceed?",icon:Ed},delete:{color:"danger",header:"Delete",message:"This will delete the item. Are you sure you want to proceed?",icon:$d}},{email:S}=Ql(),N=async L=>{mn(L),y({message:"File name - copied to clipboard",color:"success"})},j=async L=>{for(const I of L)await mn(I),await new Promise(U=>setTimeout(U,500))},V=async(L,I)=>{if((p==null?void 0:p.id)===L)return p;{const vt=(await $n("download","POST",{email:S,id:L,action:I})).data;return g(vt),vt}},K=async L=>{const I=parseInt("4294967296");if(I<parseInt(c)){y({message:"File too large to play, limit "+Qr(I),color:"danger"});return}try{e(!0);const U=await V(L,"play"),vt=U==null?void 0:U.web_content_link,bt=U==null?void 0:U.thumbnail_link,ut=U==null?void 0:U.name,Qt=U==null?void 0:U.mime_type;n&&n({videoUrl:vt,thumbnailImg:bt,videoTitle:ut,videoType:Qt}),i&&i(!0),r&&r()}catch{y({message:"Play failed, try again later",color:"danger"})}finally{e(!1)}},ot=async L=>{try{e(!0);const I=await V(L,"play"),U=I==null?void 0:I.web_content_link,vt=I==null?void 0:I.name;mn(U),await zo.share({title:vt,url:U,dialogTitle:"Select Video Player"})}catch(I){y({message:I+", Use Android App",color:"danger"})}finally{e(!1)}},C=async(L,I)=>{try{e(!0);const U=await V(L,"download"),vt=U==null?void 0:U.web_content_link,bt=U==null?void 0:U.name;vt?(await I(vt,bt),y({message:"completed",color:"success"})):console.error("Download link is not available")}catch(U){console.error("Error handling action:",U)}finally{e(!1)}},ct={copyDownloadLink:async L=>{await C(L,async(U,vt)=>{const ut=[vt,U].filter(Qt=>Qt!==void 0);await j(ut)})},copyFileName:()=>N(l||""),share:async L=>{try{const U=(await $n("share","POST",{email:S,id:L})).data;mn(U.share_url),await zo.share({url:U.share_url})}catch{y({message:"Failed to share",color:"danger"})}},download:async L=>{await C(L,async(I,U)=>{const bt=[U,I].filter(ut=>ut!==void 0);await j(bt).then(()=>{window.open(I,"_blank")})})},delete:async L=>{try{e(!0),(await $n("delete","POST",{email:S,id:L})).data.task_id&&(o&&o(L),y({message:"Deleted",color:"success"}))}catch(I){y({message:"Failed to delete "+I,color:"danger"})}finally{e(!1)}},play:K,playInAnotherApp:ot},et=["copyFileName"],T=async L=>{if(f(L),et.includes(L)){const I=ct[L];if(I){await I((s==null?void 0:s.id)||"")&&y({message:`${L} successful`,color:"success"}),t(!1),f("");return}}h(!0)},x=()=>{console.log(`${d} confirmed`);const L=ct[d];L&&L((s==null?void 0:s.id)||""),t(!1),f(""),h(!1)},D=()=>{h(!1)},st=()=>[{text:"Cancel",role:"cancel",handler:D,cssClass:"hover-effect alert-button-cancel hover-effect"},{text:"OK",handler:x,cssClass:"hover-effect alert-button-confirm hover-effect"}],At=["delete","download","share","copyDownloadLink","play","playInAnotherApp"],Et=()=>[{text:"OK",handler:x}],Rt=["download","copyDownloadLink","play","playInAnotherApp"],Vt=L=>L.map(I=>Td(I)).some(Boolean);return M.jsxs(M.Fragment,{children:[M.jsx(Yl,{children:Object.keys(w).map((L,I)=>L==="play"&&s&&s.file_category!=="VIDEO"||L==="playInAnotherApp"&&s&&(s.file_category!=="VIDEO"||!Vt(["capacitor","desktop"]))?"":s&&(s.kind!=="drive#folder"||s.kind==="drive#folder"&&!Rt.includes(L))?M.jsx(ef,{color:w[L].color,icon:w[L].icon,text:w[L].header,onClick:()=>T(L)},I):"")}),M.jsx(Cd,{isOpen:u,className:"custom-alert",onDidDismiss:()=>h(!1),header:((_t=w[d])==null?void 0:_t.header)||"Are you sure?",message:(($t=w[d])==null?void 0:$t.message)||"Do you want to proceed with this action?",buttons:At.includes(d)?st():Et()}),M.jsx(Xl,{isOpen:!!b,onDidDismiss:()=>y(null),message:b==null?void 0:b.message,duration:3e3,position:"top",color:b==null?void 0:b.color})]})},Se=typeof document>"u",Oe=Symbol(0);let Dn=!1,ta=!1,Te=null,ei=null,ue=null,te=0,Xs=[],ba={};const nf=()=>{},fi=0,sc=1,Ii=2,Zn=3;function rf(){Dn=!0,queueMicrotask(ic)}function ic(){if(!Xs.length){Dn=!1;return}ta=!0;for(let s=0;s<Xs.length;s++)Xs[s].$st!==fi&&af(Xs[s]);Xs=[],Dn=!1,ta=!1}function af(s){let t=[s];for(;s=s[Oe];)s.$e&&s.$st!==fi&&t.push(s);for(let e=t.length-1;e>=0;e--)Sa(t[e])}function of(s){const t=ri();return ji(t,s.length?s.bind(null,ni.bind(t)):s,null)}function $(s){return ji(Te,s,null)}function ya(s){return ji(null,s,null)}function Ie(){ta||ic()}function Kn(){return Te}function Dt(s,t){try{return ji(t,s,null)}catch(e){Ca(t,e);return}}function va(s,t=Te){return t==null?void 0:t.$cx[s]}function lf(s,t,e=Te){e&&(e.$cx={...e.$cx,[s]:t})}function Y(s){if(!s||!Te)return s||nf;const t=Te;return t.$d?Array.isArray(t.$d)?t.$d.push(s):t.$d=[t.$d,s]:t.$d=s,function(){t.$st!==Zn&&(s.call(null),Qn(t.$d)?t.$d=null:Array.isArray(t.$d)&&t.$d.splice(t.$d.indexOf(s),1))}}function ni(s=!0){if(this.$st!==Zn){if(this.$h)if(Array.isArray(this.$h))for(let t=this.$h.length-1;t>=0;t--)ni.call(this.$h[t]);else ni.call(this.$h);if(s){const t=this[Oe];t&&(Array.isArray(t.$h)?t.$h.splice(t.$h.indexOf(this),1):t.$h=null),cf(this)}}}function cf(s){s.$st=Zn,s.$d&&nc(s),s.$s&&Tn(s,0),s[Oe]=null,s.$s=null,s.$o=null,s.$h=null,s.$cx=ba,s.$eh=null}function nc(s){try{if(Array.isArray(s.$d))for(let t=s.$d.length-1;t>=0;t--){const e=s.$d[t];e.call(e)}else s.$d.call(s.$d);s.$d=null}catch(t){Ca(s,t)}}function ji(s,t,e){const i=Te,n=ei;Te=s,ei=e;try{return t.call(s)}finally{Te=i,ei=n}}function Ca(s,t){if(!s||!s.$eh)throw t;let e=0,i=s.$eh.length,n=Zo(t);for(e=0;e<i;e++)try{s.$eh[e](n);break}catch(r){n=Zo(r)}if(e===i)throw n}function Zo(s){return s instanceof Error?s:Error(JSON.stringify(s))}function wa(){return this.$st===Zn?this.$v:(ei&&!this.$e&&(!ue&&ei.$s&&ei.$s[te]==this?te++:ue?ue.push(this):ue=[this]),this.$c&&Sa(this),this.$v)}function rc(s){const t=Qn(s)?s(this.$v):s;if(this.$ch(this.$v,t)&&(this.$v=t,this.$o))for(let e=0;e<this.$o.length;e++)lc(this.$o[e],Ii);return this.$v}const Ea=function(){this[Oe]=null,this.$h=null,Te&&Te.append(this)},Os=Ea.prototype;Os.$cx=ba;Os.$eh=null;Os.$c=null;Os.$d=null;Os.append=function(s){s[Oe]=this,this.$h?Array.isArray(this.$h)?this.$h.push(s):this.$h=[this.$h,s]:this.$h=s,s.$cx=s.$cx===ba?this.$cx:{...this.$cx,...s.$cx},this.$eh&&(s.$eh=s.$eh?[...s.$eh,...this.$eh]:this.$eh)};Os.dispose=function(){ni.call(this)};function ri(){return new Ea}const ac=function(t,e,i){Ea.call(this),this.$st=e?Ii:fi,this.$i=!1,this.$e=!1,this.$s=null,this.$o=null,this.$v=t,e&&(this.$c=e),i&&i.dirty&&(this.$ch=i.dirty)},$a=ac.prototype;Object.setPrototypeOf($a,Os);$a.$ch=uf;$a.call=wa;function Ta(s,t,e){return new ac(s,t,e)}function uf(s,t){return s!==t}function Qn(s){return typeof s=="function"}function Sa(s){if(s.$st===sc)for(let t=0;t<s.$s.length&&(Sa(s.$s[t]),s.$st!==Ii);t++);s.$st===Ii?oc(s):s.$st=fi}function Ko(s){s.$h&&ni.call(s,!1),s.$d&&nc(s),s.$eh=s[Oe]?s[Oe].$eh:null}function oc(s){let t=ue,e=te;ue=null,te=0;try{Ko(s);const i=ji(s,s.$c,s);if(ue){if(s.$s&&Tn(s,te),s.$s&&te>0){s.$s.length=te+ue.length;for(let r=0;r<ue.length;r++)s.$s[te+r]=ue[r]}else s.$s=ue;let n;for(let r=te;r<s.$s.length;r++)n=s.$s[r],n.$o?n.$o.push(s):n.$o=[s]}else s.$s&&te<s.$s.length&&(Tn(s,te),s.$s.length=te);!s.$e&&s.$i?rc.call(s,i):(s.$v=i,s.$i=!0)}catch(i){Ca(s,i),s.$st===Ii&&(Ko(s),s.$s&&Tn(s,0));return}ue=t,te=e,s.$st=fi}function lc(s,t){if(!(s.$st>=t)&&(s.$e&&s.$st===fi&&(Xs.push(s),Dn||rf()),s.$st=t,s.$o))for(let e=0;e<s.$o.length;e++)lc(s.$o[e],sc)}function Tn(s,t){let e,i;for(let n=t;n<s.$s.length;n++)e=s.$s[n],e.$o&&(i=e.$o.indexOf(s),e.$o[i]=e.$o[e.$o.length-1],e.$o.pop())}function H(s,t){const e=Ta(s,null,t),i=wa.bind(e);return i[Oe]=!0,i.set=rc.bind(e),i}function hf(s){return Qn(s)&&Oe in s}function es(s,t){const e=Ta(t==null?void 0:t.initial,s,t),i=wa.bind(e);return i[Oe]=!0,i}function cc(s,t){const e=Ta(null,function(){let n=s();return Qn(n)&&Y(n),null},void 0);return e.$e=!0,oc(e),ni.bind(e,!0)}function df(s){return hf(s)&&"set"in s}function fe(...s){}function bs(s){return s===null}function kt(s){return typeof s>"u"}function ff(s){return bs(s)||kt(s)}function pf(s){return(s==null?void 0:s.constructor)===Object}function Zt(s){return typeof s=="number"&&!Number.isNaN(s)}function q(s){return typeof s=="string"}function Oi(s){return typeof s=="boolean"}function ee(s){return typeof s=="function"}function xt(s){return Array.isArray(s)}const m=Se?mf:cc;function mf(s,t){return typeof process<"u",fe}var uc;const gf=Se?class{}:Event,hc=Symbol("DOM_EVENT");class lt extends gf{constructor(t,...e){var n,r;super(t,e[0]),this[uc]=!0,this.triggers=new bf,this.detail=(n=e[0])==null?void 0:n.detail;const i=(r=e[0])==null?void 0:r.trigger;i&&this.triggers.add(i)}get trigger(){return this.triggers.source}get originEvent(){return this.triggers.origin}get isOriginTrusted(){var t;return((t=this.triggers.origin)==null?void 0:t.isTrusted)??!1}}uc=hc;class bf{constructor(){this.chain=[]}get source(){return this.chain[0]}get origin(){return this.chain[this.chain.length-1]}add(t){this.chain.push(t),yf(t)&&this.chain.push(...t.triggers)}remove(t){return this.chain.splice(this.chain.indexOf(t),1)[0]}has(t){return this.chain.some(e=>e===t)}hasType(t){return!!this.findType(t)}findType(t){return this.chain.find(e=>e.type===t)}walk(t){for(const e of this.chain){const i=t(e);if(i)return[e,i]}}[Symbol.iterator](){return this.chain.values()}}function yf(s){return!!(s!=null&&s[hc])}class dc extends EventTarget{addEventListener(t,e,i){return super.addEventListener(t,e,i)}removeEventListener(t,e,i){return super.removeEventListener(t,e,i)}}function E(s,t,e,i){return Se?fe:(s.addEventListener(t,e,i),Y(()=>s.removeEventListener(t,e,i)))}function Qo(s){return!!(s!=null&&s.type.startsWith("pointer"))}function Sn(s){return!!(s!=null&&s.type.startsWith("touch"))}function vf(s){return/^(click|mouse)/.test((s==null?void 0:s.type)??"")}function Yn(s){return!!(s!=null&&s.type.startsWith("key"))}function Cf(s){return Yn(s)&&s.key==="Enter"}function xa(s){return Yn(s)&&(s.key==="Enter"||s.key===" ")}function wf(s){return s instanceof Node}function G(s,t,e){if(s)if(!e&&e!==""&&e!==0)s.removeAttribute(t);else{const i=e===!0?"":e+"";s.getAttribute(t)!==i&&s.setAttribute(t,i)}else return}function Gt(s,t,e){if(s)!e&&e!==0?s.style.removeProperty(t):s.style.setProperty(t,e+"");else return}function Ef(s,t,e){s.classList[e?"add":"remove"](t)}function Yo(s){let t=s;for(;typeof t=="function";)t=t.call(this);return t}function is(s){return{id:Symbol(),provide:s}}function Wt(s,t,e=Kn()){var n;const i=!kt(t);lf(s.id,i?t:(n=s.provide)==null?void 0:n.call(s),e)}function Ct(s){return va(s.id)}function ne(s){return!kt(va(s.id))}const Or=Symbol(0),Rr=Symbol(0),Xn=Symbol(0);var fc;const Vr={};class $f{constructor(t,e,i){var o;this[fc]=null,this.$el=H(null),this.a=null,this.d=null,this.f=null,this.g=null,this.e=null,this.o=!1,this.i=Vr,this.b=null,this.c=null,this.l=[],this.m=[],this.j=[],this.n=[],this.d=e,i!=null&&i.scope&&i.scope.append(e);let n=t.state,r=t.props;if(n&&(this.h=n.create(),this.k=new Proxy(this.h,{get:(l,c)=>this.h[c]()}),Wt(n,this.h)),r&&(this.i=Tf(r),i!=null&&i.props))for(const l of Object.keys(i.props))(o=this.i[l])==null||o.set(i.props[l]);Y(this.p.bind(this))}w(){Dt(()=>{for(const t of this.l)t()},this.d)}x(t){this.a||(this.a=t,this.$el.set(t),Dt(()=>{this.f=ri(),Dt(()=>{for(const e of this.m)e(this.a);this.q(),this.r()},this.f)},this.d),t.dispatchEvent(new Event("attached")))}s(){var t;(t=this.f)==null||t.dispose(),this.f=null,this.g=null,this.a=null,this.$el.set(null)}y(){!this.a||!this.f||!this.j.length||Dt(()=>{this.g=ri(),Dt(()=>{for(const t of this.j)t(this.a)},this.g)},this.f)}z(){var t;(t=this.g)==null||t.dispose(),this.g=null}p(){if(this.o)return;this.o=!0,Dt(()=>{for(const e of this.n)e(this.a)},this.d);const t=this.a;this.s(),this.d.dispose(),this.l.length=0,this.m.length=0,this.j.length=0,this.n.length=0,this.e=null,this.b=null,this.c=null,this.i=Vr,this.d=null,this.k=Vr,this.h=null,t&&delete t.$}t(t){t.onSetup&&this.l.push(t.onSetup.bind(t)),t.onAttach&&this.m.push(t.onAttach.bind(t)),t.onConnect&&this.j.push(t.onConnect.bind(t)),t.onDestroy&&this.n.push(t.onDestroy.bind(t))}q(){if(this.b)for(const t of Object.keys(this.b))Se?G(this.a,t,Yo.call(this.e,this.b[t])):ee(this.b[t])?m(this.u.bind(this,t)):G(this.a,t,this.b[t])}r(){if(this.c)for(const t of Object.keys(this.c))Se?Gt(this.a,t,Yo.call(this.e,this.c[t])):ee(this.c[t])?m(this.v.bind(this,t)):Gt(this.a,t,this.c[t])}u(t){G(this.a,t,this.b[t].call(this.e))}v(t){Gt(this.a,t,this.c[t].call(this.e))}}fc=Xn;function Tf(s){const t={};for(const e of Object.keys(s)){const i=s[e];t[e]=H(i,i)}return t}let Mi={$$:null};function pc(s,t){return of(()=>{Mi.$$=new $f(s,Kn(),t);const e=new s;return Mi.$$.e=e,Mi.$$=null,e})}class me extends EventTarget{constructor(){super(),Mi.$$&&this.attach(Mi)}get el(){return this.$$.a}get $el(){return this.$$.$el()}get scope(){return this.$$.d}get attachScope(){return this.$$.f}get connectScope(){return this.$$.g}get $props(){return this.$$.i}get $state(){return this.$$.h}get state(){return this.$$.k}attach({$$:t}){return this.$$=t,t.t(this),this}addEventListener(t,e,i){this.listen(t,e,i)}removeEventListener(t,e,i){var n;(n=this.el)==null||n.removeEventListener(t,e,i)}setAttributes(t){this.$$.b||(this.$$.b={}),Object.assign(this.$$.b,t)}setStyles(t){this.$$.c||(this.$$.c={}),Object.assign(this.$$.c,t)}setCSSVars(t){this.setStyles(t)}createEvent(t,...e){return new lt(t,e[0])}dispatch(t,...e){if(Se||!this.el)return!1;const i=t instanceof Event?t:new lt(t,e[0]);return Object.defineProperty(i,"target",{get:()=>this.$$.e}),ya(()=>{var n,r;return(r=(n=this.$$)[Xn])==null||r.call(n,i),this.el.dispatchEvent(i)})}dispatchEvent(t){return this.dispatch(t)}listen(t,e,i){return Se||!this.el?fe:E(this.el,t,e,i)}}class W extends me{subscribe(t){return Dt(()=>m(()=>t(this.state)),this.$$.d)}destroy(){this.$$.p()}}function J(s,t,e){s[Or]||(s[Or]=new Set),s[Or].add(t)}function Tt(s,t,e){s[Rr]||(s[Rr]=new Set),s[Rr].add(t)}class ys{constructor(t){this.id=Symbol(0),this.record=t,this.A=Object.getOwnPropertyDescriptors(t)}create(){const t={},e=new Proxy(t,{get:(i,n)=>t[n]()});for(const i of Object.keys(this.record)){const n=this.A[i].get;t[i]=n?es(n.bind(e)):H(this.record[i])}return t}reset(t,e){for(const i of Object.keys(t))!this.A[i].get&&(!e||e(i))&&t[i].set(this.record[i])}}function Gi(s){return Ct(s)}function Jn(s){return s.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function mc(s){return s.replace(/-./g,t=>t[1].toUpperCase())}function Sf(s){return xf(s).replace(/\s/g,"")}function xf(s){return Wi(s.replace(/-./g,t=>" "+t[1].toUpperCase()))}function Wi(s){return s.charAt(0).toUpperCase()+s.slice(1)}const pi=a.createContext({current:null});pi.displayName="Scope";function ka(s,...t){return a.createElement(pi.Provider,{value:s},...t)}function tr(){return a.useContext(pi).current}function gc(s){const t=tr();return a.useMemo(()=>va(s.id,t),[t])}const kf=class extends a.Component{constructor(t,e){var n;super(t),this.d={current:ri()},e&&e.append(this.d.current);const i=this.constructor;i.F&&Wt(i.F,(n=i.T)==null?void 0:n.call(i),this.d.current)}render(){var t;return ka(this.d,(t=this.props)==null?void 0:t.children)}};kf.contextType=pi;function bc(s,t){typeof s=="function"?s(t):s&&(s.current=t)}function pt(...s){return t=>s.forEach(e=>bc(e,t))}function Pf(s,t){const e=a.forwardRef((i,n)=>{var y,w,S,N,j,V,K,ot;let r=a.useContext(pi),o=a.useRef(null),l=a.useRef();if(!l.current){const C=Mf(),k=tl(s,C,i,r.current);C.e=k,l.current=C,o.current=k.scope}function c(){let C=l.current,k=r.current;if(window.cancelAnimationFrame(C.ea),C.ea=-1,C.e.$$.o){const _=tl(s,C,i,k);C.e=_,C.Q=!1,C.da=!1,o.current=_.scope}return C.a&&Jo(C,C.a),C.da||(bc(n,C.e),C.da=!0),()=>xn(C)}function u(C){const k=l.current;if(!k.da){k.a=C;return}window.cancelAnimationFrame(k.K),k.K=window.requestAnimationFrame(()=>{const _=l.current;_.K=-1,_.a!==C&&(xn(_),C&&Jo(_,C),_.a=C)})}a.useEffect(()=>{const C=l.current;return window.cancelAnimationFrame(C.ea),C.ea=-1,function(){ee(i.children)&&(window.cancelAnimationFrame(C.K),C.K=-1,window.cancelAnimationFrame(C.E),C.E=-1,window.cancelAnimationFrame(C.ea),C.ea=window.requestAnimationFrame(()=>{C.ea=-1,xn(C),C.e.$$.p(),C.e.$$[Xn]=null,C.J={},C._={},o.current=null}))}},[]),a.useEffect(Ie);let h=l.current,{children:d,...f}=i,p={},g=h.aa,b=Object.keys(f);h.J={};for(const C of[...g,...b])if(t.props.has(C))h.e.$props[C].set(kt(f[C])?(y=s.props)==null?void 0:y[C]:f[C]);else if((w=t.events)!=null&&w.has(C)||(S=t.eventsRE)!=null&&S.test(C))h.J[C]=f[C];else if((N=t.domEvents)!=null&&N.has(C)||(j=t.domEventsRE)!=null&&j.test(C)){let k=Jn(C.slice(2));h._[k]=f[C],b.includes(C)?h.a&&!((ot=h.ca)!=null&&ot.has(k))&&(h.ca||(h.ca=new Set),h.ca.add(k),h.a.addEventListener(k,h.ba)):((V=h.a)==null||V.removeEventListener(k,h.ba),(K=h.ca)==null||K.delete(k))}else p[C]=f[C];return h.aa=b,ka(o,a.createElement(Af,{effect:c}),ee(d)?d==null?void 0:d({...p,suppressHydrationWarning:!0,ref:u},h.e):d)});return e.displayName=s.name+"Bridge",e}function Af({effect:s}){return a.useEffect(s,[]),null}const Xo=new Map;function Mf(){const s={a:null,aa:[],J:{},_:{},K:-1,E:-1,ea:-1,Q:!1,da:!1,ca:null,ba(t){var i,n;const e=kt(t.detail)?[t]:[t.detail,t];(n=(i=s._)[t.type])==null||n.call(i,...e)}};return s}function Jo(s,t){if(!(s.a===t&&s.Q)){if(s.Q&&xn(s),s._){s.ca||(s.ca=new Set);for(const e of Object.keys(s._))s.ca.has(e)||(t.addEventListener(e,s.ba),s.ca.add(e))}s.e.$$.x(t),s.E=window.requestAnimationFrame(()=>{s.e.$$.y(),s.E=-1}),s.Q=!0}}function xn(s){if(s.Q&&(window.cancelAnimationFrame(s.E),s.E=-1,s.e.$$.s(),s.Q=!1,s.a&&s.ca)){for(const t of s.ca)s.a.removeEventListener(t,s.ba);s.ca.clear()}}function Lf(s){var i,n;let t=Xo.get(s.type),e=kt(s.detail)?[s]:[s.detail,s];t||Xo.set(s.type,t=`on${Sf(s.type)}`),(n=(i=this.J)[t])==null||n.call(i,...e)}function tl(s,t,e,i){const n=pc(s,{props:e,scope:i});return n.$$[Xn]=Lf.bind(t),n.$$.w(),n}function yc(s,t=!1){const e=typeof s;if(e!=="string")return!t&&e==="function"?yc(s()):t&&e==="boolean"?s+"":s;const i=t?'"':"<",n=t?"&quot;":"&lt;";let r=s.indexOf(i),o=s.indexOf("&");if(r<0&&o<0)return s;let l=0,c="";for(;r>=0&&o>=0;)r<o?(l<r&&(c+=s.substring(l,r)),c+=n,l=r+1,r=s.indexOf(i,l)):(l<o&&(c+=s.substring(l,o)),c+="&amp;",l=o+1,o=s.indexOf("&",l));if(r>=0)do l<r&&(c+=s.substring(l,r)),c+=n,l=r+1,r=s.indexOf(i,l);while(r>=0);else for(;o>=0;)l<o&&(c+=s.substring(l,o)),c+="&amp;",l=o+1,o=s.indexOf("&",l);return l<s.length?c+s.substring(l):c}const Nf=Symbol(0),Df=/\s+/;function If(s,t){const e=t.trim().split(Df);for(const i of e)s.add(i)}const Of=/\s*:\s*/,Rf=/\s*;\s*/;function Vf(s,t){const e=t.trim().split(Rf);for(let i=0;i<e.length;i++){if(e[i]==="")continue;const[n,r]=e[i].split(Of);s.set(n,r)}}class _f{constructor(t){this.keepAlive=!1,this.forwardKeepAlive=!0,this.attributes=new Ff,this.style=new Bf,this.classList=new qf,this.$=t}get $props(){return this.$.$$.i}get $state(){return this.$.$$.h}get state(){return this.$.state}setup(){const t=this.$.$$;Dt(()=>{this.hasAttribute("class")&&If(this.classList.tokens,this.getAttribute("class")),this.hasAttribute("style")&&Vf(this.style.tokens,this.getAttribute("style")),t.w(),t.x(this),this.classList.length>0&&this.setAttribute("class",this.classList.toString()),this.style.length>0&&this.setAttribute("style",this.style.toString()),this.keepAlive&&this.setAttribute("keep-alive","")},t.d)}getAttribute(t){return this.attributes.getAttribute(t)}setAttribute(t,e){this.attributes.setAttribute(t,e)}hasAttribute(t){return this.attributes.hasAttribute(t)}removeAttribute(t){return this.attributes.removeAttribute(t)}[Nf](){}addEventListener(){}removeEventListener(){}dispatchEvent(){return!1}subscribe(){return fe}destroy(){this.$.destroy()}}class Ff{constructor(){this.Z=new Map}get length(){return this.Z.size}get tokens(){return this.Z}getAttribute(t){return this.Z.get(t)??null}hasAttribute(t){return this.Z.has(t)}setAttribute(t,e){this.Z.set(t,e+"")}removeAttribute(t){this.Z.delete(t)}toString(){if(this.Z.size===0)return"";let t="";for(const[e,i]of this.Z)t+=` ${e}="${yc(i,!0)}"`;return t}}class Bf{constructor(){this.Z=new Map}get length(){return this.Z.size}get tokens(){return this.Z}getPropertyValue(t){return this.Z.get(t)??""}setProperty(t,e){this.Z.set(t,e??"")}removeProperty(t){const e=this.Z.get(t);return this.Z.delete(t),e??""}toString(){if(this.Z.size===0)return"";let t="";for(const[e,i]of this.Z)t+=`${e}: ${i};`;return t}}class qf{constructor(){this.Z=new Set}get length(){return this.Z.size}get tokens(){return this.Z}add(...t){for(const e of t)this.Z.add(e)}contains(t){return this.Z.has(t)}remove(t){this.Z.delete(t)}replace(t,e){return this.Z.has(t)?(this.Z.delete(t),this.Z.add(e),!0):!1}toggle(t,e){return e!==!0&&(this.Z.has(t)||e===!1)?(this.Z.delete(t),!1):(this.Z.add(t),!0)}toString(){return Array.from(this.Z).join(" ")}}const Hf={acceptcharset:"acceptCharset","accept-charset":"acceptCharset",accesskey:"accessKey",allowfullscreen:"allowFullScreen",autocapitalize:"autoCapitalize",autocomplete:"autoComplete",autocorrect:"autoCorrect",autofocus:"autoFocus",autoplay:"autoPlay",autosave:"autoSave",cellpadding:"cellPadding",cellspacing:"cellSpacing",charset:"charSet",class:"className",classid:"classID",classname:"className",colspan:"colSpan",contenteditable:"contentEditable",contextmenu:"contextMenu",controlslist:"controlsList",crossorigin:"crossOrigin",dangerouslysetinnerhtml:"dangerouslySetInnerHTML",datetime:"dateTime",defaultchecked:"defaultChecked",defaultvalue:"defaultValue",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback",enctype:"encType",enterkeyhint:"enterKeyHint",fetchpriority:"fetchPriority",for:"htmlFor",formmethod:"formMethod",formaction:"formAction",formenctype:"formEncType",formnovalidate:"formNoValidate",formtarget:"formTarget",frameborder:"frameBorder",hreflang:"hrefLang",htmlfor:"htmlFor",httpequiv:"httpEquiv","http-equiv":"httpEquiv",imagesizes:"imageSizes",imagesrcset:"imageSrcSet",innerhtml:"innerHTML",inputmode:"inputMode",itemid:"itemID",itemprop:"itemProp",itemref:"itemRef",itemscope:"itemScope",itemtype:"itemType",keyparams:"keyParams",keytype:"keyType",marginwidth:"marginWidth",marginheight:"marginHeight",maxlength:"maxLength",mediagroup:"mediaGroup",minlength:"minLength",nomodule:"noModule",novalidate:"noValidate",playsinline:"playsInline",radiogroup:"radioGroup",readonly:"readOnly",referrerpolicy:"referrerPolicy",rowspan:"rowSpan",spellcheck:"spellCheck",srcdoc:"srcDoc",srclang:"srcLang",srcset:"srcSet",tabindex:"tabIndex",usemap:"useMap"};function jf(s,t){function e(i){let n=a.useContext(pi),r=pc(s,{props:i,scope:n.current}),o=new _f(r),l={},{style:c={},children:u,forwardRef:h,...d}=i;if(t.props.size)for(const f of Object.keys(d))t.props.has(f)||(l[f]=d[f]);else l=d;if(o.setup(),o.hasAttribute("style")){for(const[f,p]of o.style.tokens)c[f.startsWith("--")?f:mc(f)]=p;o.removeAttribute("style")}for(const[f,p]of o.attributes.tokens){const g=Hf[f];g&&(g in l||(l[g]=p),o.removeAttribute(f))}return ka({current:r.$$.d},ee(u)?u==null?void 0:u({...Object.fromEntries(o.attributes.tokens),...l,style:c},r):u,a.createElement(()=>(o.destroy(),null)))}return e.displayName=s.name+"Bridge",e}function Ui(s){return gc(s)}function rt(s,t){const[,e]=a.useState();return a.useEffect(()=>cc(()=>{s(),e({})}),[t??s]),s()}function In(s){return s?"true":"false"}function mi(){const s=new Set;return{add(...t){for(const e of t)s.add(e)},empty(){for(const t of s)t();s.clear()}}}function Gf(){const s=mi();return Y(s.empty),s}function O0(s){return Object.keys(s)}function ea(){let s,t;return{promise:new Promise((i,n)=>{s=i,t=n}),resolve:s,reject:t}}function Wf(s){return new Promise(t=>setTimeout(t,s))}function Rs(s){if(Se)return fe;let t=-1,e;function i(...n){e=n,!(t>=0)&&(t=window.requestAnimationFrame(()=>{s.apply(this,e),t=-1,e=void 0}))}return i}const Uf=Se?fe:typeof window<"u"?"requestIdleCallback"in window?window.requestIdleCallback:s=>window.setTimeout(s,1):fe;function zf(s,t){return Se?Promise.resolve():new Promise(e=>{Uf(i=>{s==null||s(i),e()},t)})}function tt(s,t){return Se?jf(s,{props:new Set(Object.keys(s.props||{}))}):Pf(s,{props:new Set(Object.keys(s.props||{})),events:new Set(t==null?void 0:t.events),eventsRE:t==null?void 0:t.eventsRegex,domEvents:t==null?void 0:t.domEvents,domEventsRE:t==null?void 0:t.domEventsRegex})}const Zf=Symbol(0),Kf=Symbol(0),Qf=Symbol(0),Yf=Symbol(0),Xf=Symbol(0),Jf=Symbol(0),tp=Symbol(0),ep=Symbol(0),sp=Symbol(0),X={ea:Zf,dc:Kf,A:Qf,fa:Yf,Zc:Xf,Pd:Jf,Hf:tp,If:ep,Jf:sp};var el;class vc extends dc{constructor(){super(...arguments),this.B=[],this[el]=!1}get length(){return this.B.length}get readonly(){return this[X.Zc]}indexOf(t){return this.B.indexOf(t)}getById(t){return t===""?null:this.B.find(e=>e.id===t)??null}toArray(){return[...this.B]}[(el=X.Zc,Symbol.iterator)](){return this.B.values()}[X.ea](t,e){const i=this.B.length;""+i in this||Object.defineProperty(this,i,{get(){return this.B[i]}}),!this.B.includes(t)&&(this.B.push(t),this.dispatchEvent(new lt("add",{detail:t,trigger:e})))}[X.dc](t,e){var n;const i=this.B.indexOf(t);i>=0&&((n=this[X.If])==null||n.call(this,t,e),this.B.splice(i,1),this.dispatchEvent(new lt("remove",{detail:t,trigger:e})))}[X.A](t){var e;for(const i of[...this.B])this[X.dc](i,t);this.B=[],this[X.Pd](!1,t),(e=this[X.Hf])==null||e.call(this)}[X.Pd](t,e){this[X.Zc]!==t&&(this[X.Zc]=t,this.dispatchEvent(new lt("readonly-change",{detail:t,trigger:e})))}}var Jt={fullscreenEnabled:0,fullscreenElement:1,requestFullscreen:2,exitFullscreen:3,fullscreenchange:4,fullscreenerror:5,fullscreen:6},sl=["webkitFullscreenEnabled","webkitFullscreenElement","webkitRequestFullscreen","webkitExitFullscreen","webkitfullscreenchange","webkitfullscreenerror","-webkit-full-screen"],il=["mozFullScreenEnabled","mozFullScreenElement","mozRequestFullScreen","mozCancelFullScreen","mozfullscreenchange","mozfullscreenerror","-moz-full-screen"],nl=["msFullscreenEnabled","msFullscreenElement","msRequestFullscreen","msExitFullscreen","MSFullscreenChange","MSFullscreenError","-ms-fullscreen"],Ut=typeof window<"u"&&typeof window.document<"u"?window.document:{},ce="fullscreenEnabled"in Ut&&Object.keys(Jt)||sl[0]in Ut&&sl||il[0]in Ut&&il||nl[0]in Ut&&nl||[],ds={requestFullscreen:function(s){return s[ce[Jt.requestFullscreen]]()},requestFullscreenFunction:function(s){return s[ce[Jt.requestFullscreen]]},get exitFullscreen(){return Ut[ce[Jt.exitFullscreen]].bind(Ut)},get fullscreenPseudoClass(){return":"+ce[Jt.fullscreen]},addEventListener:function(s,t,e){return Ut.addEventListener(ce[Jt[s]],t,e)},removeEventListener:function(s,t,e){return Ut.removeEventListener(ce[Jt[s]],t,e)},get fullscreenEnabled(){return!!Ut[ce[Jt.fullscreenEnabled]]},set fullscreenEnabled(s){},get fullscreenElement(){return Ut[ce[Jt.fullscreenElement]]},set fullscreenElement(s){},get onfullscreenchange(){return Ut[("on"+ce[Jt.fullscreenchange]).toLowerCase()]},set onfullscreenchange(s){return Ut[("on"+ce[Jt.fullscreenchange]).toLowerCase()]=s},get onfullscreenerror(){return Ut[("on"+ce[Jt.fullscreenerror]).toLowerCase()]},set onfullscreenerror(s){return Ut[("on"+ce[Jt.fullscreenerror]).toLowerCase()]=s}};const sa=ds.fullscreenEnabled;class ip extends me{constructor(){super(...arguments),this.ec=!1,this.Qd=!1}get active(){return this.Qd}get supported(){return sa}onConnect(){E(ds,"fullscreenchange",this.Rd.bind(this)),E(ds,"fullscreenerror",this._c.bind(this)),Y(this.Ga.bind(this))}async Ga(){sa&&await this.exit()}Rd(t){const e=_r(this.el);e!==this.Qd&&(e||(this.ec=!1),this.Qd=e,this.dispatch("fullscreen-change",{detail:e,trigger:t}))}_c(t){this.ec&&(this.dispatch("fullscreen-error",{detail:null,trigger:t}),this.ec=!1)}async enter(){try{return this.ec=!0,!this.el||_r(this.el)?void 0:(rl(),ds.requestFullscreen(this.el))}catch(t){throw this.ec=!1,t}}async exit(){if(!(!this.el||!_r(this.el)))return rl(),ds.exitFullscreen()}}function _r(s){if(ds.fullscreenElement===s)return!0;try{return s.matches(ds.fullscreenPseudoClass)}catch{return!1}}function rl(){if(!sa)throw Error("[vidstack] no fullscreen API")}const P=typeof document>"u",np=P?"":(navigator==null?void 0:navigator.userAgent.toLowerCase())||"",Pa=!P&&/iphone|ipad|ipod|ios|crios|fxios/i.test(np),Cc=!P&&/(iphone|ipod)/gi.test((navigator==null?void 0:navigator.platform)||""),wc=!P&&!!window.chrome,rp=!P&&(!!window.safari||Pa);function Ec(){return ap()&&ee(screen.orientation.unlock)}function ap(){return!P&&!kt(window.screen.orientation)&&!kt(window.screen.orientation.lock)}function $c(s,t){return P?!1:(s||(s=document.createElement("audio")),s.canPlayType(t).length>0)}function Tc(s,t){return P?!1:(s||(s=document.createElement("video")),s.canPlayType(t).length>0)}function Sc(s){return P?!1:(s||(s=document.createElement("video")),s.canPlayType("application/vnd.apple.mpegurl").length>0)}function xc(s){return P?!1:!!document.pictureInPictureEnabled&&!(s!=null&&s.disablePictureInPicture)}function kc(s){return P?!1:ee(s==null?void 0:s.webkitSupportsPresentationMode)&&ee(s==null?void 0:s.webkitSetPresentationMode)}async function op(){const s=document.createElement("video");return s.volume=.5,await Wf(0),s.volume===.5}function lp(){return P?void 0:(window==null?void 0:window.ManagedMediaSource)??(window==null?void 0:window.MediaSource)??(window==null?void 0:window.WebKitMediaSource)}function cp(){return P?void 0:(window==null?void 0:window.SourceBuffer)??(window==null?void 0:window.WebKitSourceBuffer)}function Aa(){if(P)return!1;const s=lp();if(kt(s))return!1;const t=s&&ee(s.isTypeSupported)&&s.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"'),e=cp(),i=kt(e)||!kt(e.prototype)&&ee(e.prototype.appendBuffer)&&ee(e.prototype.remove);return!!t&&!!i}function Pc(){return Aa()}const Ac=class Mc extends me{constructor(){super(...arguments),this.ma=H(this.Kf()),this.Db=H(!1)}get type(){return this.ma()}get locked(){return this.Db()}get portrait(){return this.ma().startsWith("portrait")}get landscape(){return this.ma().startsWith("landscape")}get supported(){return Mc.supported}onConnect(){if(this.supported)E(screen.orientation,"change",this.Lf.bind(this));else{const t=window.matchMedia("(orientation: landscape)");t.onchange=this.Lf.bind(this),Y(()=>t.onchange=null)}Y(this.Ga.bind(this))}async Ga(){this.supported&&this.Db()&&await this.unlock()}Lf(t){this.ma.set(this.Kf()),this.dispatch("orientation-change",{detail:{orientation:$(this.ma),lock:this.$c},trigger:t})}async lock(t){$(this.Db)||this.$c===t||(this.Mf(),await screen.orientation.lock(t),this.Db.set(!0),this.$c=t)}async unlock(){$(this.Db)&&(this.Mf(),this.$c=void 0,await screen.orientation.unlock(),this.Db.set(!1))}Mf(){if(!this.supported)throw Error("[vidstack] no orientation API")}Kf(){return P?"portrait-primary":this.supported?window.screen.orientation.type:window.innerWidth>=window.innerHeight?"landscape-primary":"portrait-primary"}};Ac.supported=Ec();let Lc=Ac;const up=/\.(m4a|m4b|mp4a|mpga|mp2|mp2a|mp3|m2a|m3a|wav|weba|aac|oga|spx)($|\?)/i,hp=new Set(["audio/mpeg","audio/ogg","audio/3gp","audio/mp4","audio/webm","audio/flac"]),dp=/\.(mp4|og[gv]|webm|mov|m4v)(#t=[,\d+]+)?($|\?)/i,fp=new Set(["video/mp4","video/webm","video/3gp","video/ogg","video/avi","video/mpeg"]),pp=/\.(m3u8)($|\?)/i,mp=/\.(mpd)($|\?)/i,gp=new Set(["application/vnd.apple.mpegurl","audio/mpegurl","audio/x-mpegurl","application/x-mpegurl","video/x-mpegurl","video/mpegurl","application/mpegurl"]),bp=new Set(["application/dash+xml"]);function Ma({src:s,type:t}){return q(s)?up.test(s)||hp.has(t)||s.startsWith("blob:")&&t==="audio/object":t==="audio/object"}function La(s){return q(s.src)?dp.test(s.src)||fp.has(s.type)||s.src.startsWith("blob:")&&s.type==="video/object"||gi(s)&&(P||Sc()):s.type==="video/object"}function gi({src:s,type:t}){return q(s)&&pp.test(s)||gp.has(t)}function Na({src:s,type:t}){return q(s)&&mp.test(s)||bp.has(t)}function yp(s){return q(s.src)&&(Ma(s)||La(s)||gi(s))}function vp(s){return!P&&typeof window.MediaStream<"u"&&s instanceof window.MediaStream}function R0(s,t){const e=new URLSearchParams;for(const i of Object.keys(t))e.set(i,t[i]+"");return s+"?"+e.toString()}function bi(s,t="preconnect"){if(P)return!1;const e=document.querySelector(`link[href="${s}"]`);if(!bs(e))return!0;const i=document.createElement("link");return i.rel=t,i.href=s,i.crossOrigin="true",document.head.append(i),!0}const yn={};function Cp(s){if(yn[s])return yn[s].promise;const t=ea(),e=document.querySelector(`script[src="${s}"]`);if(!bs(e))return t.resolve(),t.promise;const i=document.createElement("script");return i.src=s,i.onload=()=>{t.resolve(),delete yn[s]},i.onerror=()=>{t.reject(),delete yn[s]},setTimeout(()=>document.head.append(i),0),t.promise}function Li(s){return s==="use-credentials"?"include":q(s)?"same-origin":void 0}function wp({title:s,src:t,download:e}){const i=Oi(e)||e===""?t.src:q(e)?e:e==null?void 0:e.url;return Ep({url:i,src:t,download:e})?{url:i,name:!Oi(e)&&!q(e)&&(e==null?void 0:e.filename)||s.toLowerCase()||"media"}:null}function Ep({url:s,src:t,download:e}){return q(s)&&(e&&e!==!0||Ma(t)||La(t))}function ia(s){return!q(s)&&"width"in s&&"height"in s&&Zt(s.width)&&Zt(s.height)}class kn{get length(){return this.ua.length}constructor(t,e){xt(t)?this.ua=t:!kt(t)&&!kt(e)?this.ua=[[t,e]]:this.ua=[]}start(t){return this.ua[t][0]??1/0}end(t){return this.ua[t][1]??1/0}}function na(s){if(!s.length)return null;let t=s.start(0);for(let e=1;e<s.length;e++){const i=s.start(e);i<t&&(t=i)}return t}function Ni(s){if(!s.length)return null;let t=s.end(0);for(let e=1;e<s.length;e++){const i=s.end(e);i>t&&(t=i)}return t}const $p=Symbol(0),Tp=Symbol(0),Sp=Symbol(0),xp=Symbol(0),kp=Symbol(0),Pp=Symbol(0),Ap=Symbol(0),Q={Eb:$p,na:Tp,Fb:Sp,_:xp,ib:kp,$:Pp,Nf:Ap};function ra(s,t){return t>=s.startTime&&t<s.endTime}function er(s,t,e){let i=null,n=Kn();function r(){const o=q(t)?[t]:t,l=s.toArray().find(c=>o.includes(c.kind)&&c.mode==="showing");if(l!==i){if(!l){e(null),i=null;return}l.readyState==2?e(l):(e(null),Dt(()=>{const c=E(l,"load",()=>{e(l),c()},{once:!0})},n)),i=l}}return r(),E(s,"mode-change",r)}var al,ol,ll;class ai extends dc{constructor(t){super(),this.id="",this.label="",this.language="",this.default=!1,this._=!1,this.va=0,this.V="disabled",this.Of={},this.ad=[],this.C=[],this.Gb=[],this[al]=0,this[ol]=null,this[ll]=null;for(const e of Object.keys(t))this[e]=t[e];this.type||(this.type="vtt"),!P&&t.content?this.$h(t):t.src||(this[Q.na]=2)}static createId(t){return`vds-${t.type}-${t.kind}-${t.src??t.label??"?"}`}get metadata(){return this.Of}get regions(){return this.ad}get cues(){return this.C}get activeCues(){return this.Gb}get readyState(){return this[Q.na]}get mode(){return this.V}set mode(t){this.setMode(t)}addCue(t,e){var r;let i=0,n=this.C.length;for(i=0;i<n&&!(t.endTime<=this.C[i].startTime);i++);i===n?this.C.push(t):this.C.splice(i,0,t),t instanceof TextTrackCue||(r=this[Q.$])==null||r.track.addCue(t),this.dispatchEvent(new lt("add-cue",{detail:t,trigger:e})),ra(t,this.va)&&this[Q.Fb](this.va,e)}removeCue(t,e){var n;const i=this.C.indexOf(t);if(i>=0){const r=this.Gb.includes(t);this.C.splice(i,1),(n=this[Q.$])==null||n.track.removeCue(t),this.dispatchEvent(new lt("remove-cue",{detail:t,trigger:e})),r&&this[Q.Fb](this.va,e)}}setMode(t,e){var i;this.V!==t&&(this.V=t,t==="disabled"?(this.Gb=[],this.Pf()):this.readyState===2?this[Q.Fb](this.va,e):this.Qf(),this.dispatchEvent(new lt("mode-change",{detail:this,trigger:e})),(i=this[Q.ib])==null||i.call(this))}[(al=Q.na,ol=Q.ib,ll=Q.$,Q.Fb)](t,e){if(this.va=t,this.mode==="disabled"||!this.C.length)return;const i=[];for(let r=0,o=this.C.length;r<o;r++){const l=this.C[r];ra(l,t)&&i.push(l)}let n=i.length!==this.Gb.length;if(!n){for(let r=0;r<i.length;r++)if(!this.Gb.includes(i[r])){n=!0;break}}this.Gb=i,n&&this.Pf(e)}[Q._](){this._=!0,this.V!=="disabled"&&this.Qf()}$h(t){ie(()=>import("./prod-Dyxyokbu.js").then(e=>e.d),__vite__mapDeps([3,1,2])).then(({parseText:e,VTTCue:i,VTTRegion:n})=>{!q(t.content)||t.type==="json"?(this.Rf(t.content,i,n),this.readyState!==3&&this.Ha()):e(t.content,{type:t.type}).then(({cues:r,regions:o})=>{this.C=r,this.ad=o,this.Ha()})})}async Qf(){var t,e;if(!(!this._||this[Q.na]>0)){if(this[Q.na]=1,this.dispatchEvent(new lt("load-start")),!this.src){this.Ha();return}try{const{parseResponse:i,VTTCue:n,VTTRegion:r}=await ie(()=>import("./prod-Dyxyokbu.js").then(c=>c.d),__vite__mapDeps([3,1,2])),o=(t=this[Q.Eb])==null?void 0:t.call(this),l=fetch(this.src,{headers:this.type==="json"?{"Content-Type":"application/json"}:void 0,credentials:Li(o)});if(this.type==="json")this.Rf(await(await l).text(),n,r);else{const{errors:c,metadata:u,regions:h,cues:d}=await i(l,{type:this.type,encoding:this.encoding});if(((e=c[0])==null?void 0:e.code)===0)throw c[0];this.Of=u,this.ad=h,this.C=d}this.Ha()}catch(i){this.Sf(i)}}}Ha(){if(this[Q.na]=2,!this.src||this.type!=="vtt"){const e=this[Q.$];if(e&&!e.managed)for(const i of this.C)e.track.addCue(i)}const t=new lt("load");this[Q.Fb](this.va,t),this.dispatchEvent(t)}Sf(t){this[Q.na]=3,this.dispatchEvent(new lt("error",{detail:t}))}Rf(t,e,i){try{const{regions:n,cues:r}=Lp(t,e,i);this.ad=n,this.C=r}catch(n){this.Sf(n)}}Pf(t){this.dispatchEvent(new lt("cue-change",{trigger:t}))}}const Mp=/captions|subtitles/;function It(s){return Mp.test(s.kind)}function Lp(s,t,e){const i=q(s)?JSON.parse(s):s;let n=[],r=[];return i.regions&&e&&(n=i.regions.map(o=>Object.assign(new e,o))),(i.cues||xt(i))&&(r=(xt(i)?i:i.cues).filter(o=>Zt(o.startTime)&&Zt(o.endTime)).map(o=>Object.assign(new t(0,0,""),o))),{regions:n,cues:r}}const zi=new ys({artist:"",artwork:null,audioTrack:null,audioTracks:[],autoPlay:!1,autoPlayError:null,audioGain:null,buffered:new kn,canLoad:!1,canLoadPoster:!1,canFullscreen:!1,canOrientScreen:Ec(),canPictureInPicture:!1,canPlay:!1,clipStartTime:0,clipEndTime:0,controls:!1,get iOSControls(){return Cc&&this.mediaType==="video"&&(!this.playsInline||!ds.fullscreenEnabled&&this.fullscreen)},get nativeControls(){return this.controls||this.iOSControls},controlsVisible:!1,get controlsHidden(){return!this.controlsVisible},crossOrigin:null,ended:!1,error:null,fullscreen:!1,get loop(){return this.providedLoop||this.userPrefersLoop},logLevel:"silent",mediaType:"unknown",muted:!1,paused:!0,played:new kn,playing:!1,playsInline:!1,pictureInPicture:!1,preload:"metadata",playbackRate:1,qualities:[],quality:null,autoQuality:!1,canSetQuality:!0,canSetPlaybackRate:!0,canSetVolume:!1,canSetAudioGain:!1,seekable:new kn,seeking:!1,source:{src:"",type:""},sources:[],started:!1,textTracks:[],textTrack:null,get hasCaptions(){return this.textTracks.filter(It).length>0},volume:1,waiting:!1,realCurrentTime:0,get currentTime(){return this.clipStartTime>0?Math.max(0,Math.min(this.realCurrentTime-this.clipStartTime,this.duration)):this.realCurrentTime},providedDuration:-1,intrinsicDuration:0,get realDuration(){return this.providedDuration>0?this.providedDuration:this.intrinsicDuration},get duration(){return this.clipEndTime>0?this.clipEndTime-this.clipStartTime:Math.max(0,this.realDuration-this.clipStartTime)},get title(){return this.providedTitle||this.inferredTitle},get poster(){return this.providedPoster||this.inferredPoster},get viewType(){return this.providedViewType!=="unknown"?this.providedViewType:this.inferredViewType},get streamType(){return this.providedStreamType!=="unknown"?this.providedStreamType:this.inferredStreamType},get currentSrc(){return this.source},get bufferedStart(){const s=na(this.buffered)??0;return Math.max(0,s-this.clipStartTime)},get bufferedEnd(){const s=Ni(this.buffered)??0;return Math.min(this.duration,Math.max(0,s-this.clipStartTime))},get seekableStart(){const s=na(this.seekable)??0;return Math.max(0,s-this.clipStartTime)},get seekableEnd(){const s=this.canPlay?Ni(this.seekable)??1/0:0;return this.clipEndTime>0?Math.max(this.clipEndTime,Math.max(0,s-this.clipStartTime)):s},get seekableWindow(){return Math.max(0,this.seekableEnd-this.seekableStart)},canAirPlay:!1,canGoogleCast:!1,remotePlaybackState:"disconnected",remotePlaybackType:"none",remotePlaybackLoader:null,remotePlaybackInfo:null,get isAirPlayConnected(){return this.remotePlaybackType==="airplay"&&this.remotePlaybackState==="connected"},get isGoogleCastConnected(){return this.remotePlaybackType==="google-cast"&&this.remotePlaybackState==="connected"},pointer:"fine",orientation:"landscape",width:0,height:0,mediaWidth:0,mediaHeight:0,lastKeyboardAction:null,userBehindLiveEdge:!1,liveEdgeTolerance:10,minLiveDVRWindow:60,get canSeek(){return/unknown|on-demand|:dvr/.test(this.streamType)&&Number.isFinite(this.seekableWindow)&&(!this.live||/:dvr/.test(this.streamType)&&this.seekableWindow>=this.minLiveDVRWindow)},get live(){return this.streamType.includes("live")||!Number.isFinite(this.realDuration)},get liveEdgeStart(){return this.live&&Number.isFinite(this.seekableEnd)?Math.max(0,(this.liveSyncPosition??this.seekableEnd)-this.liveEdgeTolerance):0},get liveEdge(){return this.live&&(!this.canSeek||!this.userBehindLiveEdge&&this.currentTime>=this.liveEdgeStart)},get liveEdgeWindow(){return this.live&&Number.isFinite(this.seekableEnd)?this.seekableEnd-this.liveEdgeStart:0},autoPlaying:!1,providedTitle:"",inferredTitle:"",providedLoop:!1,userPrefersLoop:!1,providedPoster:"",inferredPoster:"",inferredViewType:"unknown",providedViewType:"unknown",providedStreamType:"unknown",inferredStreamType:"unknown",liveSyncPosition:null,savedState:null}),Nc=new Set(["autoPlayError","autoPlaying","buffered","canPlay","error","paused","played","playing","seekable","seeking","waiting"]),Np=new Set([...Nc,"ended","inferredPoster","inferredStreamType","inferredTitle","intrinsicDuration","liveSyncPosition","realCurrentTime","savedState","started","userBehindLiveEdge"]);function cl(s,t=!1){const e=t?Nc:Np;zi.reset(s,i=>e.has(i)),Ie()}const Zi=is();function at(){return Ct(Zi)}class Dp{constructor(t=void 0){this.cc=t,this.H=null,this.f=null,this.Sd=-1}setTarget(t){this.H=t}getPlayer(t){var e;return this.f?this.f:((e=t??this.H)==null||e.dispatchEvent(new lt("find-media-player",{detail:i=>void(this.f=i),bubbles:!0,composed:!0})),this.f)}setPlayer(t){this.f=t}startLoading(t){this.t("media-start-loading",t)}startLoadingPoster(t){this.t("media-poster-start-loading",t)}requestAirPlay(t){this.t("media-airplay-request",t)}requestGoogleCast(t){this.t("media-google-cast-request",t)}play(t){this.t("media-play-request",t)}pause(t){this.t("media-pause-request",t)}mute(t){this.t("media-mute-request",t)}unmute(t){this.t("media-unmute-request",t)}enterFullscreen(t,e){this.t("media-enter-fullscreen-request",e,t)}exitFullscreen(t,e){this.t("media-exit-fullscreen-request",e,t)}lockScreenOrientation(t,e){this.t("media-orientation-lock-request",e,t)}unlockScreenOrientation(t){this.t("media-orientation-unlock-request",t)}enterPictureInPicture(t){this.t("media-enter-pip-request",t)}exitPictureInPicture(t){this.t("media-exit-pip-request",t)}seeking(t,e){this.t("media-seeking-request",e,t)}seek(t,e){this.t("media-seek-request",e,t)}seekToLiveEdge(t){this.t("media-live-edge-request",t)}changeVolume(t,e){this.t("media-volume-change-request",e,Math.max(0,Math.min(1,t)))}changeAudioTrack(t,e){this.t("media-audio-track-change-request",e,t)}changeQuality(t,e){this.t("media-quality-change-request",e,t)}requestAutoQuality(t){this.changeQuality(-1,t)}changeTextTrackMode(t,e,i){this.t("media-text-track-change-request",i,{index:t,mode:e})}changePlaybackRate(t,e){this.t("media-rate-change-request",e,t)}changeAudioGain(t,e){this.t("media-audio-gain-change-request",e,t)}resumeControls(t){this.t("media-resume-controls-request",t)}pauseControls(t){this.t("media-pause-controls-request",t)}togglePaused(t){const e=this.getPlayer(t==null?void 0:t.target);e&&(e.state.paused?this.play(t):this.pause(t))}toggleControls(t){const e=this.getPlayer(t==null?void 0:t.target);e&&(e.controls.showing?e.controls.hide(0,t):e.controls.show(0,t))}toggleMuted(t){const e=this.getPlayer(t==null?void 0:t.target);e&&(e.state.muted?this.unmute(t):this.mute(t))}toggleFullscreen(t,e){const i=this.getPlayer(e==null?void 0:e.target);i&&(i.state.fullscreen?this.exitFullscreen(t,e):this.enterFullscreen(t,e))}togglePictureInPicture(t){const e=this.getPlayer(t==null?void 0:t.target);e&&(e.state.pictureInPicture?this.exitPictureInPicture(t):this.enterPictureInPicture(t))}showCaptions(t){const e=this.getPlayer(t==null?void 0:t.target);if(!e)return;let i=e.state.textTracks,n=this.Sd;(!i[n]||!It(i[n]))&&(n=-1),n===-1&&(n=i.findIndex(r=>It(r)&&r.default)),n===-1&&(n=i.findIndex(r=>It(r))),n>=0&&this.changeTextTrackMode(n,"showing",t),this.Sd=-1}disableCaptions(t){const e=this.getPlayer(t==null?void 0:t.target);if(!e)return;const i=e.state.textTracks,n=e.state.textTrack;if(n){const r=i.indexOf(n);this.changeTextTrackMode(r,"disabled",t),this.Sd=r}}toggleCaptions(t){const e=this.getPlayer(t==null?void 0:t.target);e&&(e.state.textTrack?this.disableCaptions():this.showCaptions())}userPrefersLoopChange(t,e){this.t("media-user-loop-change-request",e,t)}t(t,e,i){var l,c;const n=new lt(t,{bubbles:!0,composed:!0,cancelable:!0,detail:i,trigger:e});let r=(e==null?void 0:e.target)||null;r&&r instanceof W&&(r=r.el),r=!r||r===document||r===window||r===document.body||((l=this.f)==null?void 0:l.el)&&r instanceof Node&&!this.f.el.contains(r)?this.H??((c=this.getPlayer())==null?void 0:c.el):r??this.H,this.f?t==="media-play-request"&&!this.f.state.canLoad?r==null||r.dispatchEvent(n):this.f.canPlayQueue.k(t,()=>r==null?void 0:r.dispatchEvent(n)):r==null||r.dispatchEvent(n)}Wa(t){}}const On=Math.min,Ns=Math.max,Rn=Math.round,vn=Math.floor,fs=s=>({x:s,y:s}),Ip={left:"right",right:"left",bottom:"top",top:"bottom"},Op={start:"end",end:"start"};function ul(s,t,e){return Ns(s,On(t,e))}function Da(s,t){return typeof s=="function"?s(t):s}function oi(s){return s.split("-")[0]}function Ia(s){return s.split("-")[1]}function Dc(s){return s==="x"?"y":"x"}function Ic(s){return s==="y"?"height":"width"}function Oa(s){return["top","bottom"].includes(oi(s))?"y":"x"}function Oc(s){return Dc(Oa(s))}function Rp(s,t,e){e===void 0&&(e=!1);const i=Ia(s),n=Oc(s),r=Ic(n);let o=n==="x"?i===(e?"end":"start")?"right":"left":i==="start"?"bottom":"top";return t.reference[r]>t.floating[r]&&(o=Vn(o)),[o,Vn(o)]}function Vp(s){const t=Vn(s);return[aa(s),t,aa(t)]}function aa(s){return s.replace(/start|end/g,t=>Op[t])}function _p(s,t,e){const i=["left","right"],n=["right","left"],r=["top","bottom"],o=["bottom","top"];switch(s){case"top":case"bottom":return e?t?n:i:t?i:n;case"left":case"right":return t?r:o;default:return[]}}function Fp(s,t,e,i){const n=Ia(s);let r=_p(oi(s),e==="start",i);return n&&(r=r.map(o=>o+"-"+n),t&&(r=r.concat(r.map(aa)))),r}function Vn(s){return s.replace(/left|right|bottom|top/g,t=>Ip[t])}function Bp(s){return{top:0,right:0,bottom:0,left:0,...s}}function qp(s){return typeof s!="number"?Bp(s):{top:s,right:s,bottom:s,left:s}}function _n(s){return{...s,top:s.y,left:s.x,right:s.x+s.width,bottom:s.y+s.height}}function hl(s,t,e){let{reference:i,floating:n}=s;const r=Oa(t),o=Oc(t),l=Ic(o),c=oi(t),u=r==="y",h=i.x+i.width/2-n.width/2,d=i.y+i.height/2-n.height/2,f=i[l]/2-n[l]/2;let p;switch(c){case"top":p={x:h,y:i.y-n.height};break;case"bottom":p={x:h,y:i.y+i.height};break;case"right":p={x:i.x+i.width,y:d};break;case"left":p={x:i.x-n.width,y:d};break;default:p={x:i.x,y:i.y}}switch(Ia(t)){case"start":p[o]-=f*(e&&u?-1:1);break;case"end":p[o]+=f*(e&&u?-1:1);break}return p}const Hp=async(s,t,e)=>{const{placement:i="bottom",strategy:n="absolute",middleware:r=[],platform:o}=e,l=r.filter(Boolean),c=await(o.isRTL==null?void 0:o.isRTL(t));let u=await o.getElementRects({reference:s,floating:t,strategy:n}),{x:h,y:d}=hl(u,i,c),f=i,p={},g=0;for(let b=0;b<l.length;b++){const{name:y,fn:w}=l[b],{x:S,y:N,data:j,reset:V}=await w({x:h,y:d,initialPlacement:i,placement:f,strategy:n,middlewareData:p,rects:u,platform:o,elements:{reference:s,floating:t}});h=S??h,d=N??d,p={...p,[y]:{...p[y],...j}},V&&g<=50&&(g++,typeof V=="object"&&(V.placement&&(f=V.placement),V.rects&&(u=V.rects===!0?await o.getElementRects({reference:s,floating:t,strategy:n}):V.rects),{x:h,y:d}=hl(u,f,c)),b=-1)}return{x:h,y:d,placement:f,strategy:n,middlewareData:p}};async function Rc(s,t){var e;t===void 0&&(t={});const{x:i,y:n,platform:r,rects:o,elements:l,strategy:c}=s,{boundary:u="clippingAncestors",rootBoundary:h="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=Da(t,s),g=qp(p),y=l[f?d==="floating"?"reference":"floating":d],w=_n(await r.getClippingRect({element:(e=await(r.isElement==null?void 0:r.isElement(y)))==null||e?y:y.contextElement||await(r.getDocumentElement==null?void 0:r.getDocumentElement(l.floating)),boundary:u,rootBoundary:h,strategy:c})),S=d==="floating"?{...o.floating,x:i,y:n}:o.reference,N=await(r.getOffsetParent==null?void 0:r.getOffsetParent(l.floating)),j=await(r.isElement==null?void 0:r.isElement(N))?await(r.getScale==null?void 0:r.getScale(N))||{x:1,y:1}:{x:1,y:1},V=_n(r.convertOffsetParentRelativeRectToViewportRelativeRect?await r.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:S,offsetParent:N,strategy:c}):S);return{top:(w.top-V.top+g.top)/j.y,bottom:(V.bottom-w.bottom+g.bottom)/j.y,left:(w.left-V.left+g.left)/j.x,right:(V.right-w.right+g.right)/j.x}}const jp=function(s){return s===void 0&&(s={}),{name:"flip",options:s,async fn(t){var e,i;const{placement:n,middlewareData:r,rects:o,initialPlacement:l,platform:c,elements:u}=t,{mainAxis:h=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:b=!0,...y}=Da(s,t);if((e=r.arrow)!=null&&e.alignmentOffset)return{};const w=oi(n),S=oi(l)===l,N=await(c.isRTL==null?void 0:c.isRTL(u.floating)),j=f||(S||!b?[Vn(l)]:Vp(l));!f&&g!=="none"&&j.push(...Fp(l,b,g,N));const V=[l,...j],K=await Rc(t,y),ot=[];let C=((i=r.flip)==null?void 0:i.overflows)||[];if(h&&ot.push(K[w]),d){const gt=Rp(n,o,N);ot.push(K[gt[0]],K[gt[1]])}if(C=[...C,{placement:n,overflows:ot}],!ot.every(gt=>gt<=0)){var k,_;const gt=(((k=r.flip)==null?void 0:k.index)||0)+1,ct=V[gt];if(ct)return{data:{index:gt,overflows:C},reset:{placement:ct}};let et=(_=C.filter(T=>T.overflows[0]<=0).sort((T,x)=>T.overflows[1]-x.overflows[1])[0])==null?void 0:_.placement;if(!et)switch(p){case"bestFit":{var St;const T=(St=C.map(x=>[x.placement,x.overflows.filter(D=>D>0).reduce((D,st)=>D+st,0)]).sort((x,D)=>x[1]-D[1])[0])==null?void 0:St[0];T&&(et=T);break}case"initialPlacement":et=l;break}if(n!==et)return{reset:{placement:et}}}return{}}}},Gp=function(s){return s===void 0&&(s={}),{name:"shift",options:s,async fn(t){const{x:e,y:i,placement:n}=t,{mainAxis:r=!0,crossAxis:o=!1,limiter:l={fn:y=>{let{x:w,y:S}=y;return{x:w,y:S}}},...c}=Da(s,t),u={x:e,y:i},h=await Rc(t,c),d=Oa(oi(n)),f=Dc(d);let p=u[f],g=u[d];if(r){const y=f==="y"?"top":"left",w=f==="y"?"bottom":"right",S=p+h[y],N=p-h[w];p=ul(S,p,N)}if(o){const y=d==="y"?"top":"left",w=d==="y"?"bottom":"right",S=g+h[y],N=g-h[w];g=ul(S,g,N)}const b=l.fn({...t,[f]:p,[d]:g});return{...b,data:{x:b.x-e,y:b.y-i}}}}};function ps(s){return Vc(s)?(s.nodeName||"").toLowerCase():"#document"}function se(s){var t;return(s==null||(t=s.ownerDocument)==null?void 0:t.defaultView)||window}function ns(s){var t;return(t=(Vc(s)?s.ownerDocument:s.document)||window.document)==null?void 0:t.documentElement}function Vc(s){return s instanceof Node||s instanceof se(s).Node}function ss(s){return s instanceof Element||s instanceof se(s).Element}function Re(s){return s instanceof HTMLElement||s instanceof se(s).HTMLElement}function dl(s){return typeof ShadowRoot>"u"?!1:s instanceof ShadowRoot||s instanceof se(s).ShadowRoot}function Ki(s){const{overflow:t,overflowX:e,overflowY:i,display:n}=pe(s);return/auto|scroll|overlay|hidden|clip/.test(t+i+e)&&!["inline","contents"].includes(n)}function Wp(s){return["table","td","th"].includes(ps(s))}function Ra(s){const t=Va(),e=pe(s);return e.transform!=="none"||e.perspective!=="none"||(e.containerType?e.containerType!=="normal":!1)||!t&&(e.backdropFilter?e.backdropFilter!=="none":!1)||!t&&(e.filter?e.filter!=="none":!1)||["transform","perspective","filter"].some(i=>(e.willChange||"").includes(i))||["paint","layout","strict","content"].some(i=>(e.contain||"").includes(i))}function Up(s){let t=li(s);for(;Re(t)&&!sr(t);){if(Ra(t))return t;t=li(t)}return null}function Va(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function sr(s){return["html","body","#document"].includes(ps(s))}function pe(s){return se(s).getComputedStyle(s)}function ir(s){return ss(s)?{scrollLeft:s.scrollLeft,scrollTop:s.scrollTop}:{scrollLeft:s.pageXOffset,scrollTop:s.pageYOffset}}function li(s){if(ps(s)==="html")return s;const t=s.assignedSlot||s.parentNode||dl(s)&&s.host||ns(s);return dl(t)?t.host:t}function _c(s){const t=li(s);return sr(t)?s.ownerDocument?s.ownerDocument.body:s.body:Re(t)&&Ki(t)?t:_c(t)}function Ri(s,t,e){var i;t===void 0&&(t=[]),e===void 0&&(e=!0);const n=_c(s),r=n===((i=s.ownerDocument)==null?void 0:i.body),o=se(n);return r?t.concat(o,o.visualViewport||[],Ki(n)?n:[],o.frameElement&&e?Ri(o.frameElement):[]):t.concat(n,Ri(n,[],e))}function Fc(s){const t=pe(s);let e=parseFloat(t.width)||0,i=parseFloat(t.height)||0;const n=Re(s),r=n?s.offsetWidth:e,o=n?s.offsetHeight:i,l=Rn(e)!==r||Rn(i)!==o;return l&&(e=r,i=o),{width:e,height:i,$:l}}function _a(s){return ss(s)?s:s.contextElement}function si(s){const t=_a(s);if(!Re(t))return fs(1);const e=t.getBoundingClientRect(),{width:i,height:n,$:r}=Fc(t);let o=(r?Rn(e.width):e.width)/i,l=(r?Rn(e.height):e.height)/n;return(!o||!Number.isFinite(o))&&(o=1),(!l||!Number.isFinite(l))&&(l=1),{x:o,y:l}}const zp=fs(0);function Bc(s){const t=se(s);return!Va()||!t.visualViewport?zp:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Zp(s,t,e){return t===void 0&&(t=!1),!e||t&&e!==se(s)?!1:t}function Ds(s,t,e,i){t===void 0&&(t=!1),e===void 0&&(e=!1);const n=s.getBoundingClientRect(),r=_a(s);let o=fs(1);t&&(i?ss(i)&&(o=si(i)):o=si(s));const l=Zp(r,e,i)?Bc(r):fs(0);let c=(n.left+l.x)/o.x,u=(n.top+l.y)/o.y,h=n.width/o.x,d=n.height/o.y;if(r){const f=se(r),p=i&&ss(i)?se(i):i;let g=f,b=g.frameElement;for(;b&&i&&p!==g;){const y=si(b),w=b.getBoundingClientRect(),S=pe(b),N=w.left+(b.clientLeft+parseFloat(S.paddingLeft))*y.x,j=w.top+(b.clientTop+parseFloat(S.paddingTop))*y.y;c*=y.x,u*=y.y,h*=y.x,d*=y.y,c+=N,u+=j,g=se(b),b=g.frameElement}}return _n({width:h,height:d,x:c,y:u})}const Kp=[":popover-open",":modal"];function qc(s){return Kp.some(t=>{try{return s.matches(t)}catch{return!1}})}function Qp(s){let{elements:t,rect:e,offsetParent:i,strategy:n}=s;const r=n==="fixed",o=ns(i),l=t?qc(t.floating):!1;if(i===o||l&&r)return e;let c={scrollLeft:0,scrollTop:0},u=fs(1);const h=fs(0),d=Re(i);if((d||!d&&!r)&&((ps(i)!=="body"||Ki(o))&&(c=ir(i)),Re(i))){const f=Ds(i);u=si(i),h.x=f.x+i.clientLeft,h.y=f.y+i.clientTop}return{width:e.width*u.x,height:e.height*u.y,x:e.x*u.x-c.scrollLeft*u.x+h.x,y:e.y*u.y-c.scrollTop*u.y+h.y}}function Yp(s){return Array.from(s.getClientRects())}function Hc(s){return Ds(ns(s)).left+ir(s).scrollLeft}function Xp(s){const t=ns(s),e=ir(s),i=s.ownerDocument.body,n=Ns(t.scrollWidth,t.clientWidth,i.scrollWidth,i.clientWidth),r=Ns(t.scrollHeight,t.clientHeight,i.scrollHeight,i.clientHeight);let o=-e.scrollLeft+Hc(s);const l=-e.scrollTop;return pe(i).direction==="rtl"&&(o+=Ns(t.clientWidth,i.clientWidth)-n),{width:n,height:r,x:o,y:l}}function Jp(s,t){const e=se(s),i=ns(s),n=e.visualViewport;let r=i.clientWidth,o=i.clientHeight,l=0,c=0;if(n){r=n.width,o=n.height;const u=Va();(!u||u&&t==="fixed")&&(l=n.offsetLeft,c=n.offsetTop)}return{width:r,height:o,x:l,y:c}}function t1(s,t){const e=Ds(s,!0,t==="fixed"),i=e.top+s.clientTop,n=e.left+s.clientLeft,r=Re(s)?si(s):fs(1),o=s.clientWidth*r.x,l=s.clientHeight*r.y,c=n*r.x,u=i*r.y;return{width:o,height:l,x:c,y:u}}function fl(s,t,e){let i;if(t==="viewport")i=Jp(s,e);else if(t==="document")i=Xp(ns(s));else if(ss(t))i=t1(t,e);else{const n=Bc(s);i={...t,x:t.x-n.x,y:t.y-n.y}}return _n(i)}function jc(s,t){const e=li(s);return e===t||!ss(e)||sr(e)?!1:pe(e).position==="fixed"||jc(e,t)}function e1(s,t){const e=t.get(s);if(e)return e;let i=Ri(s,[],!1).filter(l=>ss(l)&&ps(l)!=="body"),n=null;const r=pe(s).position==="fixed";let o=r?li(s):s;for(;ss(o)&&!sr(o);){const l=pe(o),c=Ra(o);!c&&l.position==="fixed"&&(n=null),(r?!c&&!n:!c&&l.position==="static"&&!!n&&["absolute","fixed"].includes(n.position)||Ki(o)&&!c&&jc(s,o))?i=i.filter(h=>h!==o):n=l,o=li(o)}return t.set(s,i),i}function s1(s){let{element:t,boundary:e,rootBoundary:i,strategy:n}=s;const o=[...e==="clippingAncestors"?e1(t,this._c):[].concat(e),i],l=o[0],c=o.reduce((u,h)=>{const d=fl(t,h,n);return u.top=Ns(d.top,u.top),u.right=On(d.right,u.right),u.bottom=On(d.bottom,u.bottom),u.left=Ns(d.left,u.left),u},fl(t,l,n));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function i1(s){const{width:t,height:e}=Fc(s);return{width:t,height:e}}function n1(s,t,e){const i=Re(t),n=ns(t),r=e==="fixed",o=Ds(s,!0,r,t);let l={scrollLeft:0,scrollTop:0};const c=fs(0);if(i||!i&&!r)if((ps(t)!=="body"||Ki(n))&&(l=ir(t)),i){const d=Ds(t,!0,r,t);c.x=d.x+t.clientLeft,c.y=d.y+t.clientTop}else n&&(c.x=Hc(n));const u=o.left+l.scrollLeft-c.x,h=o.top+l.scrollTop-c.y;return{x:u,y:h,width:o.width,height:o.height}}function pl(s,t){return!Re(s)||pe(s).position==="fixed"?null:t?t(s):s.offsetParent}function Gc(s,t){const e=se(s);if(!Re(s)||qc(s))return e;let i=pl(s,t);for(;i&&Wp(i)&&pe(i).position==="static";)i=pl(i,t);return i&&(ps(i)==="html"||ps(i)==="body"&&pe(i).position==="static"&&!Ra(i))?e:i||Up(s)||e}const r1=async function(s){const t=this.getOffsetParent||Gc,e=this.getDimensions;return{reference:n1(s.reference,await t(s.floating),s.strategy),floating:{x:0,y:0,...await e(s.floating)}}};function a1(s){return pe(s).direction==="rtl"}const o1={convertOffsetParentRelativeRectToViewportRelativeRect:Qp,getDocumentElement:ns,getClippingRect:s1,getOffsetParent:Gc,getElementRects:r1,getClientRects:Yp,getDimensions:i1,getScale:si,isElement:ss,isRTL:a1};function l1(s,t){let e=null,i;const n=ns(s);function r(){var l;clearTimeout(i),(l=e)==null||l.disconnect(),e=null}function o(l,c){l===void 0&&(l=!1),c===void 0&&(c=1),r();const{left:u,top:h,width:d,height:f}=s.getBoundingClientRect();if(l||t(),!d||!f)return;const p=vn(h),g=vn(n.clientWidth-(u+d)),b=vn(n.clientHeight-(h+f)),y=vn(u),S={rootMargin:-p+"px "+-g+"px "+-b+"px "+-y+"px",threshold:Ns(0,On(1,c))||1};let N=!0;function j(V){const K=V[0].intersectionRatio;if(K!==c){if(!N)return o();K?o(!1,K):i=setTimeout(()=>{o(!1,1e-7)},100)}N=!1}try{e=new IntersectionObserver(j,{...S,root:n.ownerDocument})}catch{e=new IntersectionObserver(j,S)}e.observe(s)}return o(!0),r}function c1(s,t,e,i){i===void 0&&(i={});const{ancestorScroll:n=!0,ancestorResize:r=!0,elementResize:o=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:c=!1}=i,u=_a(s),h=n||r?[...u?Ri(u):[],...Ri(t)]:[];h.forEach(w=>{n&&w.addEventListener("scroll",e,{passive:!0}),r&&w.addEventListener("resize",e)});const d=u&&l?l1(u,e):null;let f=-1,p=null;o&&(p=new ResizeObserver(w=>{let[S]=w;S&&S.target===u&&p&&(p.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame(()=>{var N;(N=p)==null||N.observe(t)})),e()}),u&&!c&&p.observe(u),p.observe(t));let g,b=c?Ds(s):null;c&&y();function y(){const w=Ds(s);b&&(w.x!==b.x||w.y!==b.y||w.width!==b.width||w.height!==b.height)&&e(),b=w,g=requestAnimationFrame(y)}return e(),()=>{var w;h.forEach(S=>{n&&S.removeEventListener("scroll",e),r&&S.removeEventListener("resize",e)}),d==null||d(),(w=p)==null||w.disconnect(),p=null,c&&cancelAnimationFrame(g)}}const u1=Gp,h1=jp,d1=(s,t,e)=>{const i=new Map,n={platform:o1,...e},r={...n.platform,_c:i};return Hp(s,t,{...n,platform:r})};function Ot(s,t=2){return Number(s.toFixed(t))}function oa(s){var t;return((t=String(s).split(".")[1])==null?void 0:t.length)??0}function Fa(s,t,e){return Math.max(s,Math.min(e,t))}function V0(s,t,e){if(s)return E(s,t,e)}function ml(s,t){return wf(t.target)&&s.contains(t.target)}const la=new Set;P||window.setInterval(()=>{for(const s of la)s()},1e3);function f1(s){return la.add(s),()=>la.delete(s)}function ht(s,t,e){s.hasAttribute(t)||s.setAttribute(t,e)}function vs(s,t){if(s.hasAttribute("aria-label")||s.hasAttribute("data-no-label"))return;if(!ee(t)){G(s,"aria-label",t);return}function e(){G(s,"aria-label",t())}P?e():m(e)}function Wc(s){const t=getComputedStyle(s);return t.display!=="none"&&parseInt(t.opacity)>0}function p1(s){return!!s&&("checkVisibility"in s?s.checkVisibility({checkOpacity:!0,checkVisibilityCSS:!0}):Wc(s))}function Uc(s,t){return f1(()=>t(p1(s)))}function zc(s,t,e){for(;t;){if(t===s)return!0;if(e!=null&&e(t))break;t=t.parentElement}return!1}function ms(s,t){E(s,"pointerup",e=>{e.button===0&&t(e)}),E(s,"keydown",e=>{xa(e)&&t(e)})}function Ba(s){return Sn(s)&&(s.touches.length>1||s.changedTouches.length>1)}function Vi(s){if(P)return s();let t=Kn(),e=window.requestAnimationFrame(()=>{Dt(s,t),e=-1});return()=>void window.cancelAnimationFrame(e)}function Zc(s,t,e,{offsetVarName:i,xOffset:n,yOffset:r,...o}){if(!s)return;const l=e.replace(" ","-").replace("-center","");if(Gt(s,"visibility",t?null:"hidden"),!t)return;let c=e.includes("top");const u=d=>e.includes("left")?`calc(-1 * ${d})`:d,h=d=>c?`calc(-1 * ${d})`:d;return c1(t,s,()=>{d1(t,s,{placement:l,middleware:[...o.middleware??[],h1({fallbackAxisSideDirection:"start",crossAxis:!1}),u1()],...o}).then(({x:d,y:f,middlewareData:p})=>{var b;const g=!!((b=p.flip)!=null&&b.index);c=e.includes(g?"bottom":"top"),s.setAttribute("data-placement",g?e.startsWith("top")?e.replace("top","bottom"):e.replace("bottom","top"):e),Object.assign(s.style,{top:`calc(${f+"px"} + ${h(r?r+"px":`var(--${i}-y-offset, 0px)`)})`,left:`calc(${d+"px"} + ${u(n?n+"px":`var(--${i}-x-offset, 0px)`)})`})})})}function m1(s){return getComputedStyle(s).animationName!=="none"}function _i(s){return s instanceof HTMLElement}class Vs extends me{}class g1 extends Vs{constructor(){super(...arguments),this.Td=-2,this.Hb=!1,this.Tf=H(!1),this.Ud=H(!1),this.fc=null,this.Vd=H(!0),this.defaultDelay=2e3}get canIdle(){return this.Vd()}set canIdle(t){this.Vd.set(t)}get hideOnMouseLeave(){const{hideControlsOnMouseLeave:t}=this.$props;return this.Tf()||t()}set hideOnMouseLeave(t){this.Tf.set(t)}get showing(){return this.$state.controlsVisible()}show(t=0,e){this.Wd(),this.Hb||this.bd(!0,t,e)}hide(t=this.defaultDelay,e){this.Wd(),this.Hb||this.bd(!1,t,e)}pause(t){this.Hb=!0,this.Wd(),this.bd(!0,0,t)}resume(t){this.Hb=!1,!this.$state.paused()&&this.bd(!1,this.defaultDelay,t)}onConnect(){m(this.Ib.bind(this))}Ib(){const{viewType:t}=this.$state;if(!this.Vd())return;if(t()==="audio"){this.show();return}m(this.ai.bind(this)),m(this.gc.bind(this));const e=this.hc.bind(this),i=this.jb.bind(this);this.listen("can-play",n=>this.show(0,n)),this.listen("play",e),this.listen("pause",i),this.listen("auto-play-fail",i)}ai(){const{started:t,pointer:e,paused:i}=this.$state;if(!t()||e()!=="fine")return;const n=this.hideOnMouseLeave;(!n||!this.Ud())&&m(()=>{i()||this.listen("pointermove",this.Uf.bind(this))}),n&&(this.listen("mouseenter",this.bi.bind(this)),this.listen("mouseleave",this.ci.bind(this)))}gc(){const{paused:t,started:e,autoPlayError:i}=this.$state;if(t()||i()&&!e())return;const n=this.Uf.bind(this);m(()=>{const r=this.$state.pointer(),o=r==="coarse",l=[o?"touchend":"pointerup","keydown"];for(const c of l)this.listen(c,n,{passive:!1})})}hc(t){this.show(0,t),this.hide(void 0,t)}jb(t){this.show(0,t)}bi(t){this.Ud.set(!1),this.show(0,t),this.hide(void 0,t)}ci(t){this.Ud.set(!0),this.hide(0,t)}Wd(){window.clearTimeout(this.Td),this.Td=-1}Uf(t){var e;t.MEDIA_GESTURE||this.Hb||Ba(t)||(Yn(t)&&(t.key==="Escape"?((e=this.el)==null||e.focus(),this.fc=null):this.fc&&(t.preventDefault(),requestAnimationFrame(()=>{var i;(i=this.fc)==null||i.focus(),this.fc=null}))),this.show(0,t),this.hide(this.defaultDelay,t))}bd(t,e,i){if(e===0){this.F(t,i);return}this.Td=window.setTimeout(()=>{this.scope&&this.F(t&&!this.Hb,i)},e)}F(t,e){var i;this.$state.controlsVisible()!==t&&(this.$state.controlsVisible.set(t),!t&&document.activeElement&&((i=this.el)!=null&&i.contains(document.activeElement))&&(this.fc=document.activeElement,requestAnimationFrame(()=>{var n;(n=this.el)==null||n.focus({preventScroll:!0})})),this.dispatch("controls-change",{detail:t,trigger:e}))}}var _s=b1;function b1(s,t,e){var i=null,n=null,r=e&&e.leading,o=e&&e.trailing;r==null&&(r=!0),o==null&&(o=!r),r==!0&&(o=!1);var l=function(){i&&(clearTimeout(i),i=null)},c=function(){var h=n;l(),h&&h()},u=function(){var h=r&&!i,d=this,f=arguments;if(n=function(){return s.apply(d,f)},i||(i=setTimeout(function(){if(i=null,o)return n()},t)),h)return h=!1,n()};return u.cancel=l,u.flush=c,u}class y1{constructor(){this.playerId="vds-player",this.mediaId=null,this.I={volume:null,muted:null,audioGain:null,time:null,lang:null,captions:null,rate:null,quality:null},this.saveTimeThrottled=_s(this.saveTime.bind(this),1e3)}async getVolume(){return this.I.volume}async setVolume(t){this.I.volume=t,this.save()}async getMuted(){return this.I.muted}async setMuted(t){this.I.muted=t,this.save()}async getTime(){return this.I.time}async setTime(t,e){const i=t<0;this.I.time=i?null:t,i||e?this.saveTime():this.saveTimeThrottled()}async getLang(){return this.I.lang}async setLang(t){this.I.lang=t,this.save()}async getCaptions(){return this.I.captions}async setCaptions(t){this.I.captions=t,this.save()}async getPlaybackRate(){return this.I.rate}async setPlaybackRate(t){this.I.rate=t,this.save()}async getAudioGain(){return this.I.audioGain}async setAudioGain(t){this.I.audioGain=t,this.save()}async getVideoQuality(){return this.I.quality}async setVideoQuality(t){this.I.quality=t,this.save()}onChange(t,e,i="vds-player"){const n=i?localStorage.getItem(i):null,r=e?localStorage.getItem(e):null;this.playerId=i,this.mediaId=e,this.I={volume:null,muted:null,audioGain:null,lang:null,captions:null,rate:null,quality:null,...n?JSON.parse(n):{},time:r?+r:null}}save(){if(P||!this.playerId)return;const t=JSON.stringify({...this.I,time:void 0});localStorage.setItem(this.playerId,t)}saveTime(){if(P||!this.mediaId)return;const t=(this.I.time??0).toString();localStorage.setItem(this.mediaId,t)}}class v1{constructor(){this.priority=0,this.Vf=!0,this.m=null,this.K=null,this.wa=new Set}canRender(t,e){return!!e}attach(t){this.m=t,t&&(t.textTracks.onchange=this.F.bind(this))}addTrack(t){this.wa.add(t),this.di(t)}removeTrack(t){var e,i;(i=(e=t[Q.$])==null?void 0:e.remove)==null||i.call(e),t[Q.$]=null,this.wa.delete(t)}changeTrack(t){const e=t==null?void 0:t[Q.$];e&&e.track.mode!=="showing"&&(e.track.mode="showing"),this.K=t}setDisplay(t){this.Vf=t,this.F()}detach(){this.m&&(this.m.textTracks.onchange=null);for(const t of this.wa)this.removeTrack(t);this.wa.clear(),this.m=null,this.K=null}di(t){var i;if(!this.m)return;const e=t[i=Q.$]??(t[i]=this.ei(t));_i(e)&&(this.m.append(e),e.track.mode=e.default?"showing":"disabled")}ei(t){const e=document.createElement("track"),i=t.default||t.mode==="showing",n=t.src&&t.type==="vtt";return e.id=t.id,e.src=n?t.src:"",e.label=t.label,e.kind=t.kind,e.default=i,t.language&&(e.srclang=t.language),i&&!n&&this.Wf(t,e.track),e}Wf(t,e){var i;if(!(t.src&&t.type==="vtt"||(i=e.cues)!=null&&i.length))for(const n of t.cues)e.addCue(n)}F(t){for(const e of this.wa){const i=e[Q.$];if(!i)continue;if(!this.Vf){i.track.mode=i.managed?"hidden":"disabled";continue}const n=i.track.mode==="showing";n&&this.Wf(e,i.track),e.setMode(n?"showing":"disabled",t)}}}class C1{constructor(t){this.a=t,this.m=null,this.cd=[],this.Xf=!1,this.xa=null,this.kb=null;const e=t.textTracks;this.Xd=e,m(this.Yd.bind(this)),Y(this.fi.bind(this)),E(e,"add",this.Zd.bind(this)),E(e,"remove",this.gi.bind(this)),E(e,"mode-change",this.Ia.bind(this))}Yd(){const{nativeControls:t}=this.a.$state;this.Xf=t(),this.Ia()}add(t){this.cd.push(t),this.Ia()}remove(t){t.detach(),this.cd.splice(this.cd.indexOf(t),1),this.Ia()}Yf(t){requestAnimationFrame(()=>{if(this.m=t,t){this.xa=new v1,this.xa.attach(t);for(const e of this.Xd)this.Zf(e)}this.Ia()})}Zf(t){var e;It(t)&&((e=this.xa)==null||e.addTrack(t))}hi(t){var e;It(t)&&((e=this.xa)==null||e.removeTrack(t))}Zd(t){this.Zf(t.detail)}gi(t){this.hi(t.detail)}Ia(){var i,n,r,o,l,c,u;const t=this.Xd.selected;if(this.m&&(this.Xf||t!=null&&t[Q.Nf])){(i=this.kb)==null||i.changeTrack(null),(n=this.xa)==null||n.setDisplay(!0),(r=this.xa)==null||r.changeTrack(t);return}if((o=this.xa)==null||o.setDisplay(!1),(l=this.xa)==null||l.changeTrack(null),!t){(c=this.kb)==null||c.changeTrack(null);return}const e=this.cd.sort((h,d)=>h.priority-d.priority).find(h=>h.canRender(t,this.m));this.kb!==e&&((u=this.kb)==null||u.detach(),e==null||e.attach(this.m),this.kb=e??null),e==null||e.changeTrack(t)}fi(){var t,e;(t=this.xa)==null||t.detach(),this.xa=null,(e=this.kb)==null||e.detach(),this.kb=null}}var qa=w1;function w1(s,t,e){var i=null,n=null,r=function(){i&&(clearTimeout(i),n=null,i=null)},o=function(){var c=n;r(),c&&c()},l=function(){if(!t)return s.apply(this,arguments);var c=this,u=arguments,h=e&&!i;if(r(),n=function(){s.apply(c,u)},i=setTimeout(function(){if(i=null,!h){var d=n;return n=null,d()}},t),h)return n()};return l.cancel=r,l.flush=o,l}class E1 extends vc{constructor(){super(),this._=!1,this.lb={},this.mb=null,this.nb=null,this.cg=qa(async()=>{var i;if(!this._)return;!this.nb&&this.mb&&(this.nb=await this.mb.getLang());const t=await((i=this.mb)==null?void 0:i.getCaptions()),e=[["captions","subtitles"],"chapters","descriptions","metadata"];for(const n of e){const r=this.getByKind(n);if(r.find(h=>h.mode==="showing"))continue;const o=this.nb?r.find(h=>h.language===this.nb):null,l=xt(n)?this.lb[n.find(h=>this.lb[h])||""]:this.lb[n],c=o??l,u=c&&It(c);c&&(!u||t!==!1)&&(c.mode="showing",u&&this.dg(c))}},300),this._d=null,this.bg=this.ii.bind(this)}get selected(){return this.B.find(e=>e.mode==="showing"&&It(e))??null}get selectedIndex(){const t=this.selected;return t?this.indexOf(t):-1}get preferredLang(){return this.nb}set preferredLang(t){this.nb=t,this.ag(t)}add(t,e){const i=t instanceof ai,n=i?t:new ai(t),r=t.kind==="captions"||t.kind==="subtitles"?"captions":t.kind;return this.lb[r]&&t.default&&delete t.default,n.addEventListener("mode-change",this.bg),this[X.ea](n,e),n[Q.Eb]=this[Q.Eb],this._&&n[Q._](),t.default&&(this.lb[r]=n),this.cg(),this}remove(t,e){if(this._d=t,!!this.B.includes(t))return t===this.lb[t.kind]&&delete this.lb[t.kind],t.mode="disabled",t[Q.ib]=null,t.removeEventListener("mode-change",this.bg),this[X.dc](t,e),this._d=null,this}clear(t){for(const e of[...this.B])this.remove(e,t);return this}getByKind(t){const e=Array.isArray(t)?t:[t];return this.B.filter(i=>e.includes(i.kind))}[Q._](){if(!this._){for(const t of this.B)t[Q._]();this._=!0,this.cg()}}ii(t){const e=t.detail;if(this.mb&&It(e)&&e!==this._d&&this.dg(e),e.mode==="showing"){const i=It(e)?["captions","subtitles"]:[e.kind];for(const n of this.B)n.mode==="showing"&&n!=e&&i.includes(n.kind)&&(n.mode="disabled")}this.dispatchEvent(new lt("mode-change",{detail:t.detail,trigger:t}))}dg(t){var e,i;t.mode!=="disabled"&&this.ag(t.language),(i=(e=this.mb)==null?void 0:e.setCaptions)==null||i.call(e,t.mode==="showing")}ag(t){var e,i;(i=(e=this.mb)==null?void 0:e.setLang)==null||i.call(e,this.nb=t)}setStorage(t){this.mb=t}}const Pi=Symbol(0);class Kc extends vc{get selected(){return this.B.find(t=>t.selected)??null}get selectedIndex(){return this.B.findIndex(t=>t.selected)}[X.If](t,e){this[X.fa](t,!1,e)}[X.ea](t,e){t[Pi]=!1,Object.defineProperty(t,"selected",{get(){return this[Pi]},set:i=>{var n;this.readonly||((n=this[X.Jf])==null||n.call(this),this[X.fa](t,i))}}),super[X.ea](t,e)}[X.fa](t,e,i){if(e===(t==null?void 0:t[Pi]))return;const n=this.selected;t&&(t[Pi]=e),(e?n!==t:n===t)&&(n&&(n[Pi]=!1),this.dispatchEvent(new lt("change",{detail:{prev:n,current:this.selected},trigger:i})))}}class $1 extends Kc{}const T1=Symbol(0),S1=Symbol(0),he={Xa:T1,Ja:S1};class x1 extends Kc{constructor(){super(...arguments),this.dd=!1,this.switch="current"}get auto(){return this.dd||this.readonly}[X.Jf](){this[he.Xa](!1)}[X.Hf](t){this[he.Ja]=void 0,this[he.Xa](!1,t)}autoSelect(t){var e;this.readonly||this.dd||!this[he.Ja]||((e=this[he.Ja])==null||e.call(this,t),this[he.Xa](!0,t))}getBySrc(t){return this.B.find(e=>e.src===t)}[he.Xa](t,e){this.dd!==t&&(this.dd=t,this.dispatchEvent(new lt("auto-change",{detail:t,trigger:e})))}}function Ha(s,t){return[...s].sort(t?P1:k1)}function k1(s,t){return s.height===t.height?(s.bitrate??0)-(t.bitrate??0):s.height-t.height}function P1(s,t){return t.height===s.height?(t.bitrate??0)-(s.bitrate??0):t.height-s.height}function A1(s){return!P&&s instanceof HTMLAudioElement}function M1(s){return!P&&s instanceof HTMLVideoElement}function L1(s){return A1(s)||M1(s)}const N1={togglePaused:"k Space",toggleMuted:"m",toggleFullscreen:"f",togglePictureInPicture:"i",toggleCaptions:"c",seekBackward:"j J ArrowLeft",seekForward:"l L ArrowRight",volumeUp:"ArrowUp",volumeDown:"ArrowDown",speedUp:">",slowDown:"<"},gl=new Set(["Shift","Alt","Meta","Control"]),D1='button, [role="button"]',bl='input, textarea, select, [contenteditable], [role^="menuitem"], [role="timer"]';class I1 extends Vs{constructor(t){super(),this.a=t,this.Jb=null}onConnect(){m(this.ji.bind(this))}ji(){const{keyDisabled:t,keyTarget:e}=this.$props;if(t())return;const i=e()==="player"?this.el:document,n=H(!1);i===this.el?(this.listen("focusin",()=>n.set(!0)),this.listen("focusout",r=>{this.el.contains(r.target)||n.set(!1)})):($(n)||n.set(document.querySelector("[data-media-player]")===this.el),E(document,"focusin",r=>{const o=r.composedPath().find(l=>l instanceof Element&&l.localName==="media-player");o!==void 0&&n.set(this.el===o)})),m(()=>{n()&&(E(i,"keyup",this.ic.bind(this)),E(i,"keydown",this.jc.bind(this)),E(i,"keydown",this.ki.bind(this),{capture:!0}))})}ic(t){var r,o;const e=document.activeElement;if(!t.key||!this.$state.canSeek()||e!=null&&e.matches(bl))return;let{method:i,value:n}=this.$d(t);if(!q(n)&&!xt(n)){(r=n==null?void 0:n.onKeyUp)==null||r.call(n,{event:t,player:this.a.player,remote:this.a.remote}),(o=n==null?void 0:n.callback)==null||o.call(n,t,this.a.remote);return}if(i!=null&&i.startsWith("seek")&&(t.preventDefault(),t.stopPropagation(),this.Jb?(this.eg(t,i==="seekForward"),this.Jb=null):(this.a.remote.seek(this.ed,t),this.ed=void 0)),i!=null&&i.startsWith("volume")){const l=this.el.querySelector("[data-media-volume-slider]");l==null||l.dispatchEvent(new KeyboardEvent("keyup",{key:i==="volumeUp"?"Up":"Down",shiftKey:t.shiftKey,trigger:t}))}}jc(t){var o,l,c,u;if(!t.key||gl.has(t.key))return;const e=document.activeElement;if(e!=null&&e.matches(bl)||xa(t)&&(e!=null&&e.matches(D1)))return;let{method:i,value:n}=this.$d(t),r=!t.metaKey&&/^[0-9]$/.test(t.key);if(!q(n)&&!xt(n)&&!r){(o=n==null?void 0:n.onKeyDown)==null||o.call(n,{event:t,player:this.a.player,remote:this.a.remote}),(l=n==null?void 0:n.callback)==null||l.call(n,t,this.a.remote);return}if(!i&&r){t.preventDefault(),t.stopPropagation(),this.a.remote.seek(this.$state.duration()/10*Number(t.key),t);return}if(i){switch(t.preventDefault(),t.stopPropagation(),i){case"seekForward":case"seekBackward":this.Ka(t,i,i==="seekForward");break;case"volumeUp":case"volumeDown":const h=this.el.querySelector("[data-media-volume-slider]");if(h)h.dispatchEvent(new KeyboardEvent("keydown",{key:i==="volumeUp"?"Up":"Down",shiftKey:t.shiftKey,trigger:t}));else{const f=t.shiftKey?.1:.05;this.a.remote.changeVolume(this.$state.volume()+(i==="volumeUp"?+f:-f),t)}break;case"toggleFullscreen":this.a.remote.toggleFullscreen("prefer-media",t);break;case"speedUp":case"slowDown":const d=this.$state.playbackRate();this.a.remote.changePlaybackRate(Math.max(.25,Math.min(2,d+(i==="speedUp"?.25:-.25))),t);break;default:(u=(c=this.a.remote)[i])==null||u.call(c,t)}this.$state.lastKeyboardAction.set({action:i,event:t})}}ki(t){L1(t.target)&&this.$d(t).method&&t.preventDefault()}$d(t){const e={...this.$props.keyShortcuts(),...this.a.ariaKeys},i=Object.keys(e).find(n=>{var l;const r=e[n],o=xt(r)?r.join(" "):q(r)?r:r==null?void 0:r.keys;return(l=xt(o)?o:o==null?void 0:o.split(" "))==null?void 0:l.some(c=>R1(c).replace(/Control/g,"Ctrl").split("+").every(u=>gl.has(u)?t[u.toLowerCase()+"Key"]:t.key===u.replace("Space"," ")))});return{method:i,value:i?e[i]:null}}li(t,e){const i=t.shiftKey?10:5;return this.ed=Math.max(0,Math.min((this.ed??this.$state.currentTime())+(e==="seekForward"?+i:-i),this.$state.duration()))}eg(t,e){var i;(i=this.Jb)==null||i.dispatchEvent(new KeyboardEvent(t.type,{key:e?"Right":"Left",shiftKey:t.shiftKey,trigger:t}))}Ka(t,e,i){this.$state.canSeek()&&(this.Jb||(this.Jb=this.el.querySelector("[data-media-time-slider]")),this.Jb?this.eg(t,i):this.a.remote.seeking(this.li(t,e),t))}}const O1=["!","@","#","$","%","^","&","*","(",")"];function R1(s){return s.replace(/Shift\+(\d)/g,(t,e)=>O1[e-1])}class V1 extends me{constructor(t){super(),this.ae=t}onAttach(t){const{$props:e,ariaKeys:i}=at(),n=t.getAttribute("aria-keyshortcuts");if(n){i[this.ae]=n,P||Y(()=>{delete i[this.ae]});return}const r=e.keyShortcuts()[this.ae];if(r){const o=xt(r)?r.join(" "):q(r)?r:r==null?void 0:r.keys;t.setAttribute("aria-keyshortcuts",xt(o)?o.join(" "):o)}}}class _1{constructor(){this.name="audio"}canPlay(t){return Ma(t)?P||!q(t.src)||t.type==="?"||$c(this.target,t.type):!1}mediaType(){return"audio"}async load(t){if(P)throw Error("[vidstack] can not load audio provider server-side");return new(await ie(()=>import("./vidstack-Cs9WY57L-BXc9oK4Q.js"),__vite__mapDeps([4,1,2,5,6,7]))).AudioProvider(this.target,t)}}class ja{constructor(){this.name="video"}canPlay(t){return La(t)?P||!q(t.src)||t.type==="?"||Tc(this.target,t.type):!1}mediaType(){return"video"}async load(t){if(P)throw Error("[vidstack] can not load video provider server-side");return new(await Promise.resolve().then(function(){return em})).VideoProvider(this.target,t)}}let Pn=null,Fn=[],Bn=[];function Ga(){return Pn??(Pn=new AudioContext)}function F1(){const s=Ga(),t=s.createGain();return t.connect(s.destination),Fn.push(t),t}function B1(s,t){const e=Ga(),i=e.createMediaElementSource(s);return t&&i.connect(t),Bn.push(i),i}function q1(s){const t=Fn.indexOf(s);t!==-1&&(Fn.splice(t,1),s.disconnect(),Qc())}function H1(s){const t=Bn.indexOf(s);t!==-1&&(Bn.splice(t,1),s.disconnect(),Qc())}function Qc(){Pn&&Fn.length===0&&Bn.length===0&&Pn.close().then(()=>{Pn=null})}class j1{constructor(t,e){this.a=t,this.F=e,this.ya=null,this.La=null}get currentGain(){var t,e;return((e=(t=this.ya)==null?void 0:t.gain)==null?void 0:e.value)??null}get supported(){return!0}setGain(t){const e=this.currentGain;if(t!==this.currentGain){if(t===1&&e!==1){this.removeGain();return}this.ya||(this.ya=F1(),this.La&&this.La.connect(this.ya)),this.La||(this.La=B1(this.a,this.ya)),this.ya.gain.value=t,this.F(t)}}removeGain(){this.ya&&(this.La&&this.La.connect(Ga().destination),this.fg(),this.F(null))}destroy(){this.mi(),this.fg()}mi(){if(this.La)try{H1(this.La)}catch{}finally{this.La=null}}fg(){if(this.ya)try{q1(this.ya)}catch{}finally{this.ya=null}}}class Yc{constructor(t){this.Ma=t}Ya(){kt(this.za)&&this.gg()}aa(){Zt(this.za)&&window.cancelAnimationFrame(this.za),this.za=void 0}gg(){this.za=window.requestAnimationFrame(()=>{kt(this.za)||(this.Ma(),this.gg())})}}class G1{constructor(t,e){this.p=t,this.b=e,this.Za=Gf(),this.kc=!1,this.be=!1,this.ce=!1,this.ga=new Yc(this.lc.bind(this)),this.ig=void 0,this.Ai=void 0,this.ni(),m(this.oi.bind(this)),Y(this.de.bind(this))}get a(){return this.p.media}get c(){return this.b.delegate.c}de(){this.be=!1,this.ce=!1,this.ga.aa(),this.Za.empty()}lc(){const t=this.a.currentTime;this.b.$state.realCurrentTime()!==t&&this.Kb(t)}ni(){this.G("loadstart",this.Na),this.G("abort",this.hg),this.G("emptied",this.pi),this.G("error",this.R),this.G("volumechange",this.Oa)}qi(){this.be||(this.Za.add(this.G("loadeddata",this.ri),this.G("loadedmetadata",this.si),this.G("canplay",this.fd),this.G("canplaythrough",this.ti),this.G("durationchange",this.ee),this.G("play",this.hc),this.G("progress",this.ob),this.G("stalled",this.ui),this.G("suspend",this.vi),this.G("ratechange",this.wi)),this.be=!0)}xi(){this.ce||(this.Za.add(this.G("pause",this.jb),this.G("playing",this.yi),this.G("seeked",this.pb),this.G("seeking",this.zi),this.G("ended",this.mc),this.G("waiting",this.fe)),this.ce=!0)}G(t,e){return E(this.a,t,e.bind(this))}Bi(t){}Kb(t,e){const i={currentTime:Math.min(t,this.b.$state.seekableEnd()),played:this.a.played};this.c("time-update",i,e)}Na(t){if(this.a.networkState===3){this.hg(t);return}this.qi(),this.c("load-start",void 0,t)}hg(t){this.c("abort",void 0,t)}pi(){this.c("emptied",void 0,event)}ri(t){this.c("loaded-data",void 0,t)}si(t){this.xi(),this.c("loaded-metadata",void 0,t),(Pa||rp&&gi(this.b.$state.source()))&&this.b.delegate.Ha(this.ge(),t)}ge(){return{provider:$(this.b.$provider),duration:this.a.duration,buffered:this.a.buffered,seekable:this.a.seekable}}hc(t){this.b.$state.canPlay&&this.c("play",void 0,t)}jb(t){this.a.readyState===1&&!this.kc||(this.kc=!1,this.ga.aa(),this.c("pause",void 0,t))}fd(t){this.b.delegate.Ha(this.ge(),t)}ti(t){this.b.$state.started()||this.c("can-play-through",this.ge(),t)}yi(t){this.kc=!1,this.c("playing",void 0,t),this.ga.Ya()}ui(t){this.c("stalled",void 0,t),this.a.readyState<3&&(this.kc=!0,this.c("waiting",void 0,t))}fe(t){this.a.readyState<3&&(this.kc=!0,this.c("waiting",void 0,t))}mc(t){this.ga.aa(),this.Kb(this.a.duration,t),this.c("end",void 0,t),this.b.$state.loop()&&ff(this.a.controls)&&(this.a.controls=!1)}oi(){this.b.$state.paused()&&E(this.a,"timeupdate",this.nc.bind(this))}nc(t){this.Kb(this.a.currentTime,t)}ee(t){this.b.$state.ended()&&this.Kb(this.a.duration,t),this.c("duration-change",this.a.duration,t)}Oa(t){const e={volume:this.a.volume,muted:this.a.muted};this.c("volume-change",e,t)}pb(t){this.Kb(this.a.currentTime,t),this.c("seeked",this.a.currentTime,t),Math.trunc(this.a.currentTime)===Math.trunc(this.a.duration)&&oa(this.a.duration)>oa(this.a.currentTime)&&(this.Kb(this.a.duration,t),this.a.ended||this.b.player.dispatch(new lt("media-play-request",{trigger:t})))}zi(t){this.c("seeking",this.a.currentTime,t)}ob(t){const e={buffered:this.a.buffered,seekable:this.a.seekable};this.c("progress",e,t)}vi(t){this.c("suspend",void 0,t)}wi(t){this.c("rate-change",this.a.playbackRate,t)}R(t){const e=this.a.error;if(!e)return;const i={message:e.message,code:e.code,mediaError:e};this.c("error",i,t)}}class W1{constructor(t,e){this.p=t,this.b=e,this.oc.onaddtrack=this.Ci.bind(this),this.oc.onremovetrack=this.Di.bind(this),this.oc.onchange=this.Ei.bind(this),E(this.b.audioTracks,"change",this.Fi.bind(this))}get oc(){return this.p.media.audioTracks}Ci(t){const e=t.track;if(e.label==="")return;const i=e.id.toString()||`native-audio-${this.b.audioTracks.length}`,n={id:i,label:e.label,language:e.language,kind:e.kind,selected:!1};this.b.audioTracks[X.ea](n,t),e.enabled&&(n.selected=!0)}Di(t){const e=this.b.audioTracks.getById(t.track.id);e&&this.b.audioTracks[X.dc](e,t)}Ei(t){let e=this.jg();if(!e)return;const i=this.b.audioTracks.getById(e.id);i&&this.b.audioTracks[X.fa](i,!0,t)}jg(){return Array.from(this.oc).find(t=>t.enabled)}Fi(t){const{current:e}=t.detail;if(!e)return;const i=this.oc.getTrackById(e.id);if(i){const n=this.jg();n&&(n.enabled=!1),i.enabled=!0}}}class U1{constructor(t,e){this.a=t,this.b=e,this.scope=ri(),this.L=null,this.audioGain=new j1(this.a,i=>{this.b.delegate.c("audio-gain-change",i)})}setup(){new G1(this,this.b),"audioTracks"in this.media&&new W1(this,this.b),Y(()=>{this.audioGain.destroy(),this.a.srcObject=null,this.a.removeAttribute("src");for(const t of this.a.querySelectorAll("source"))t.remove();this.a.load()})}get type(){return""}get media(){return this.a}get currentSrc(){return this.L}setPlaybackRate(t){this.a.playbackRate=t}async play(){return this.a.play()}async pause(){return this.a.pause()}setMuted(t){this.a.muted=t}setVolume(t){this.a.volume=t}setCurrentTime(t){this.a.currentTime=t}setPlaysInline(t){G(this.a,"playsinline",t)}async loadSource({src:t,type:e},i){this.a.preload=i||"",vp(t)?(this.pc(),this.a.srcObject=t):(this.a.srcObject=null,q(t)?e!=="?"?this.he({src:t,type:e}):(this.pc(),this.a.src=this.kg(t)):(this.pc(),this.a.src=window.URL.createObjectURL(t))),this.a.load(),this.L={src:t,type:e}}he(t,e){const i=this.a.querySelector("source[data-vds]"),n=i??document.createElement("source");G(n,"src",this.kg(t.src)),G(n,"type",t.type!=="?"?t.type:e),G(n,"data-vds",""),i||this.a.append(n)}pc(){var t;(t=this.a.querySelector("source[data-vds]"))==null||t.remove()}kg(t){const{clipStartTime:e,clipEndTime:i}=this.b.$state,n=e(),r=i();return n>0&&r>0?`${t}#t=${n},${r}`:n>0?`${t}#t=${n}`:r>0?`${t}#t=0,${r}`:t}}class z1{constructor(t,e){this.a=t,this.b=e,this.qb=H(!1),this.ie()}get supported(){return this.qb()}ie(){var t;P||!((t=this.a)!=null&&t.remote)||!this.lg||(this.a.remote.watchAvailability(e=>{this.qb.set(e)}).catch(()=>{this.qb.set(!1)}),m(this.Gi.bind(this)))}Gi(){if(!this.qb())return;const t=["connecting","connect","disconnect"],e=this.je.bind(this);e(),E(this.a,"playing",e);for(const i of t)E(this.a.remote,i,e)}async prompt(){if(!this.supported)throw Error("Not supported on this platform.");return this.ma==="airplay"&&this.a.webkitShowPlaybackTargetPicker?this.a.webkitShowPlaybackTargetPicker():this.a.remote.prompt()}je(t){const e=this.a.remote.state;if(e===this.Aa)return;const i={type:this.ma,state:e};this.b.delegate.c("remote-playback-change",i,t),this.Aa=e}}class Z1 extends z1{constructor(){super(...arguments),this.ma="airplay"}get lg(){return"WebKitPlaybackTargetAvailabilityEvent"in window}}class K1{constructor(t,e){this.m=t,this.b=e,t.textTracks.onaddtrack=this.Zd.bind(this),Y(this.de.bind(this))}Zd(t){const e=t.track;if(!e||Q1(this.m,e))return;const i=new ai({id:e.id,kind:e.kind,label:e.label??"",language:e.language,type:"vtt"});i[Q.$]={track:e},i[Q.na]=2,i[Q.Nf]=!0;let n=0;const r=o=>{if(e.cues)for(let l=n;l<e.cues.length;l++)i.addCue(e.cues[l],o),n++};r(t),e.oncuechange=r,this.b.textTracks.add(i,t),i.setMode(e.mode,t)}de(){var t;this.m.textTracks.onaddtrack=null;for(const e of this.b.textTracks){const i=(t=e[Q.$])==null?void 0:t.track;i!=null&&i.oncuechange&&(i.oncuechange=null)}}}function Q1(s,t){return Array.from(s.children).find(e=>e.track===t)}class Y1{constructor(t,e){this.m=t,this.a=e,this.F=(i,n)=>{this.a.delegate.c("picture-in-picture-change",i,n)},E(this.m,"enterpictureinpicture",this.Hi.bind(this)),E(this.m,"leavepictureinpicture",this.Ii.bind(this))}get active(){return document.pictureInPictureElement===this.m}get supported(){return xc(this.m)}async enter(){return this.m.requestPictureInPicture()}exit(){return document.exitPictureInPicture()}Hi(t){this.F(!0,t)}Ii(t){this.F(!1,t)}}class X1{constructor(t,e){this.m=t,this.a=e,this.V="inline",E(this.m,"webkitpresentationmodechanged",this.ib.bind(this))}get qb(){return kc(this.m)}async gd(t){this.V!==t&&this.m.webkitSetPresentationMode(t)}ib(t){var i;const e=this.V;this.V=this.m.webkitPresentationMode,(i=this.a.player)==null||i.dispatch(new lt("video-presentation-change",{detail:this.V,trigger:t})),["fullscreen","picture-in-picture"].forEach(n=>{(this.V===n||e===n)&&this.a.delegate.c(`${n}-change`,this.V===n,t)})}}class J1{constructor(t){this.Pa=t}get active(){return this.Pa.V==="fullscreen"}get supported(){return this.Pa.qb}async enter(){this.Pa.gd("fullscreen")}async exit(){this.Pa.gd("inline")}}class tm{constructor(t){this.Pa=t}get active(){return this.Pa.V==="picture-in-picture"}get supported(){return this.Pa.qb}async enter(){this.Pa.gd("picture-in-picture")}async exit(){this.Pa.gd("inline")}}let Xc=class extends U1{constructor(t,e){super(t,e),this.$$PROVIDER_TYPE="VIDEO",Dt(()=>{if(this.airPlay=new Z1(t,e),kc(t)){const i=new X1(t,e);this.fullscreen=new J1(i),this.pictureInPicture=new tm(i)}else xc(t)&&(this.pictureInPicture=new Y1(t,e))},this.scope)}get type(){return"video"}setup(){super.setup(),Sc(this.video)&&new K1(this.video,this.b),this.b.textRenderers.Yf(this.video),Y(()=>{this.b.textRenderers.Yf(null)}),this.type==="video"&&this.b.delegate.c("provider-setup",this)}get video(){return this.a}};var em=Object.freeze({__proto__:null,VideoProvider:Xc});function yl(s){try{return new Intl.DisplayNames(navigator.languages,{type:"language"}).of(s)??null}catch{return null}}const sm=s=>`dash-${Jn(s)}`;class im{constructor(t,e){this.m=t,this.b=e,this.d=null,this.rb=null,this.sb={},this.tb=new Set,this.Lb=null,this.pe={},this.oa=-1}get instance(){return this.d}setup(t){this.d=t().create();const e=this.Ji.bind(this);for(const i of Object.values(t.events))this.d.on(i,e);this.d.on(t.events.ERROR,this.R.bind(this));for(const i of this.tb)i(this.d);this.b.player.dispatch("dash-instance",{detail:this.d}),this.d.initialize(this.m,void 0,!1),this.d.updateSettings({streaming:{text:{defaultEnabled:!1,dispatchForManualRendering:!0},buffer:{fastSwitchEnabled:!0}},...this.sb}),this.d.on(t.events.FRAGMENT_LOADING_STARTED,this.Ki.bind(this)),this.d.on(t.events.FRAGMENT_LOADING_COMPLETED,this.Li.bind(this)),this.d.on(t.events.MANIFEST_LOADED,this.Mi.bind(this)),this.d.on(t.events.QUALITY_CHANGE_RENDERED,this._a.bind(this)),this.d.on(t.events.TEXT_TRACKS_ADDED,this.Ni.bind(this)),this.d.on(t.events.TRACK_CHANGE_RENDERED,this.qc.bind(this)),this.b.qualities[he.Ja]=this.ke.bind(this),E(this.b.qualities,"change",this.le.bind(this)),E(this.b.audioTracks,"change",this.me.bind(this)),this.rb=m(this.ne.bind(this))}ba(t){return new lt(sm(t.type),{detail:t})}ne(){if(!this.b.$state.live())return;const t=new Yc(this.oe.bind(this));return t.Ya(),t.aa.bind(t)}oe(){if(!this.d)return;const t=this.d.duration()-this.d.time();this.b.$state.liveSyncPosition.set(isNaN(t)?1/0:t)}Ji(t){var e;(e=this.b.player)==null||e.dispatch(this.ba(t))}Oi(t){var l;const e=(l=this.Lb)==null?void 0:l[Q.$],i=(e==null?void 0:e.track).cues;if(!e||!i)return;const n=this.Lb.id,r=this.pe[n]??0,o=this.ba(t);for(let c=r;c<i.length;c++){const u=i[c];u.positionAlign||(u.positionAlign="auto"),this.Lb.addCue(u,o)}this.pe[n]=i.length}Ni(t){var r;if(!this.d)return;const e=t.tracks,i=[...this.m.textTracks].filter(o=>"manualMode"in o),n=this.ba(t);for(let o=0;o<i.length;o++){const l=e[o],c=i[o],u=`dash-${l.kind}-${o}`,h=new ai({id:u,label:(l==null?void 0:l.label)??((r=l.labels.find(d=>d.text))==null?void 0:r.text)??((l==null?void 0:l.lang)&&yl(l.lang))??(l==null?void 0:l.lang)??void 0,language:l.lang??void 0,kind:l.kind,default:l.defaultTrack});h[Q.$]={managed:!0,track:c},h[Q.na]=2,h[Q.ib]=()=>{this.d&&(h.mode==="showing"?(this.d.setTextTrack(o),this.Lb=h):(this.d.setTextTrack(-1),this.Lb=null))},this.b.textTracks.add(h,n)}}qc(t){const{mediaType:e,newMediaInfo:i}=t;if(e==="audio"){const n=this.b.audioTracks.getById(`dash-audio-${i.index}`);if(n){const r=this.ba(t);this.b.audioTracks[X.fa](n,!0,r)}}}_a(t){if(t.mediaType!=="video")return;const e=this.b.qualities[t.newQuality];if(e){const i=this.ba(t);this.b.qualities[X.fa](e,!0,i)}}Mi(t){if(this.b.$state.canPlay()||!this.d)return;const{type:e,mediaPresentationDuration:i}=t.data,n=this.ba(t);this.b.delegate.c("stream-type-change",e!=="static"?"live":"on-demand",n),this.b.delegate.c("duration-change",i,n),this.b.qualities[he.Xa](!0,n);const r=this.d.getVideoElement(),o=this.d.getTracksForTypeFromManifest("video",t.data),l=[...new Set(o.map(d=>d.mimeType))].find(d=>d&&Tc(r,d)),c=o.filter(d=>l===d.mimeType)[0];let u=this.d.getTracksForTypeFromManifest("audio",t.data);const h=[...new Set(u.map(d=>d.mimeType))].find(d=>d&&$c(r,d));if(u=u.filter(d=>h===d.mimeType),c.bitrateList.forEach((d,f)=>{var g;const p={id:((g=d.id)==null?void 0:g.toString())??`dash-bitrate-${f}`,width:d.width??0,height:d.height??0,bitrate:d.bandwidth??0,codec:c.codec,index:f};this.b.qualities[X.ea](p,n)}),Zt(c.index)){const d=this.b.qualities[c.index];d&&this.b.qualities[X.fa](d,!0,n)}u.forEach((d,f)=>{const g=d.labels.find(y=>navigator.languages.some(w=>y.lang&&w.toLowerCase().startsWith(y.lang.toLowerCase())))||d.labels[0],b={id:`dash-audio-${d==null?void 0:d.index}`,label:(g==null?void 0:g.text)??(d.lang&&yl(d.lang))??d.lang??"",language:d.lang??"",kind:"main",mimeType:d.mimeType,codec:d.codec,index:f};this.b.audioTracks[X.ea](b,n)}),r.dispatchEvent(new lt("canplay",{trigger:n}))}R(t){const{type:e,error:i}=t;switch(i.code){case 27:this.qe(i);break;default:this.rc(i);break}}Ki(){this.oa>=0&&this.$a()}Li(t){t.mediaType==="text"&&requestAnimationFrame(this.Oi.bind(this,t))}qe(t){var e;this.$a(),(e=this.d)==null||e.play(),this.oa=window.setTimeout(()=>{this.oa=-1,this.rc(t)},5e3)}$a(){clearTimeout(this.oa),this.oa=-1}rc(t){this.b.delegate.c("error",{message:t.message??"",code:1,error:t})}ke(){var e;this.mg("video",!0);const{qualities:t}=this.b;(e=this.d)==null||e.setQualityFor("video",t.selectedIndex,!0)}mg(t,e){var i;(i=this.d)==null||i.updateSettings({streaming:{abr:{autoSwitchBitrate:{[t]:e}}}})}le(){const{qualities:t}=this.b;!this.d||t.auto||!t.selected||(this.mg("video",!1),this.d.setQualityFor("video",t.selectedIndex,t.switch==="current"),wc&&(this.m.currentTime=this.m.currentTime))}me(){if(!this.d)return;const{audioTracks:t}=this.b,e=this.d.getTracksFor("audio").find(i=>t.selected&&t.selected.id===`dash-audio-${i.index}`);e&&this.d.setCurrentTrack(e)}A(){this.$a(),this.Lb=null,this.pe={}}loadSource(t){var e;this.A(),q(t.src)&&((e=this.d)==null||e.attachSource(t.src))}destroy(){var t,e;this.A(),(t=this.d)==null||t.destroy(),this.d=null,(e=this.rb)==null||e.call(this),this.rb=null}}function An(s){return s instanceof Error?s:Error(typeof s=="string"?s:JSON.stringify(s))}function Ye(s,t){if(!s)throw Error(t||"Assertion failed.")}class nm{constructor(t,e,i){this.M=t,this.b=e,this.Ma=i,this.re()}async re(){const t={onLoadStart:this.Na.bind(this),onLoaded:this.ub.bind(this),onLoadError:this.se.bind(this)};let e=await am(this.M,t);if(kt(e)&&!q(this.M)&&(e=await rm(this.M,t)),!e)return null;if(!window.dashjs.supportsMediaSource()){const i="[vidstack] `dash.js` is not supported in this environment";return this.b.player.dispatch(new lt("dash-unsupported")),this.b.delegate.c("error",{message:i,code:4}),null}return e}Na(){this.b.player.dispatch(new lt("dash-lib-load-start"))}ub(t){this.b.player.dispatch(new lt("dash-lib-loaded",{detail:t})),this.Ma(t)}se(t){const e=An(t);this.b.player.dispatch(new lt("dash-lib-load-error",{detail:e})),this.b.delegate.c("error",{message:e.message,code:4,error:e})}}async function rm(s,t={}){var e,i,n,r,o;if(!kt(s)){if((e=t.onLoadStart)==null||e.call(t),s.prototype&&s.prototype!==Function)return(i=t.onLoaded)==null||i.call(t,s),s;try{const l=(n=await s())==null?void 0:n.default;if(l)(r=t.onLoaded)==null||r.call(t,l);else throw Error("");return l}catch(l){(o=t.onLoadError)==null||o.call(t,l)}}}async function am(s,t={}){var e,i,n;if(q(s)){(e=t.onLoadStart)==null||e.call(t);try{if(await Cp(s),!ee(window.dashjs.MediaPlayer))throw Error("");const r=window.dashjs.MediaPlayer;return(i=t.onLoaded)==null||i.call(t,r),r}catch(r){(n=t.onLoadError)==null||n.call(t,r)}}}const om="https://cdn.jsdelivr.net";class Jc extends Xc{constructor(){super(...arguments),this.$$PROVIDER_TYPE="DASH",this.sc=null,this.e=new im(this.video,this.b),this.pa=`${om}/npm/dashjs@4.7.4/dist/dash.all.min.js`}get ctor(){return this.sc}get instance(){return this.e.instance}get type(){return"dash"}get canLiveSync(){return!0}get config(){return this.e.sb}set config(t){this.e.sb=t}get library(){return this.pa}set library(t){this.pa=t}preconnect(){q(this.pa)&&bi(this.pa)}setup(){super.setup(),new nm(this.pa,this.b,t=>{this.sc=t,this.e.setup(t),this.b.delegate.c("provider-setup",this);const e=$(this.b.$state.source);e&&this.loadSource(e)})}async loadSource(t,e){if(!q(t.src)){this.pc();return}this.a.preload=e||"",this.he(t,"application/x-mpegurl"),this.e.loadSource(t),this.L=t}onInstance(t){const e=this.e.instance;return e&&t(e),this.e.tb.add(t),()=>this.e.tb.delete(t)}destroy(){this.e.destroy()}}Jc.supported=Pc();var lm=Object.freeze({__proto__:null,DASHProvider:Jc});const tu=class eu extends ja{constructor(){super(...arguments),this.name="dash"}canPlay(t){return eu.supported&&Na(t)}async load(t){if(P)throw Error("[vidstack] can not load dash provider server-side");return new(await Promise.resolve().then(function(){return lm})).DASHProvider(this.target,t)}};tu.supported=Pc();let cm=tu;const su=class iu extends ja{constructor(){super(...arguments),this.name="hls"}canPlay(t){return iu.supported&&gi(t)}async load(t){if(P)throw Error("[vidstack] can not load hls provider server-side");return new(await ie(()=>import("./vidstack-Bswg46LY-DkFdumZQ.js"),__vite__mapDeps([8,1,2,5,6,7]))).HLSProvider(this.target,t)}};su.supported=Aa();let um=su;class hm{constructor(){this.name="vimeo"}preconnect(){const t=["https://i.vimeocdn.com","https://f.vimeocdn.com","https://fresnel.vimeocdn.com"];for(const e of t)bi(e)}canPlay(t){return q(t.src)&&t.type==="video/vimeo"}mediaType(){return"video"}async load(t){if(P)throw Error("[vidstack] can not load vimeo provider server-side");return new(await ie(()=>import("./vidstack-B9KOumdA-hXZRHia0.js"),__vite__mapDeps([9,10,11,1,2,5,6,7]))).VimeoProvider(this.target,t)}async loadPoster(t,e,i){const{resolveVimeoVideoId:n,getVimeoVideoInfo:r}=await ie(()=>import("./vidstack-BTBUzdbF-Cao5mZMB.js"),[]);if(!q(t.src))return null;const{videoId:o}=n(t.src);return o?r(o,i).then(l=>l?l.poster:null):null}}class dm{constructor(){this.name="youtube"}preconnect(){const t=["https://www.google.com","https://i.ytimg.com","https://googleads.g.doubleclick.net","https://static.doubleclick.net"];for(const e of t)bi(e)}canPlay(t){return q(t.src)&&t.type==="video/youtube"}mediaType(){return"video"}async load(t){if(P)throw Error("[vidstack] can not load youtube provider server-side");return new(await ie(()=>import("./vidstack-1WuajY3Z-CbJs2KuC.js"),__vite__mapDeps([12,10,13,1,2,5,6,7]))).YouTubeProvider(this.target,t)}async loadPoster(t,e,i){const{findYouTubePoster:n,resolveYouTubeVideoId:r}=await ie(()=>import("./vidstack-DscYSLiW-CA6XwpqT.js"),[]),o=q(t.src)&&r(t.src);return o?n(o,i):null}}const Fr=Symbol(0),fm=["autoPlay","canAirPlay","canFullscreen","canGoogleCast","canLoad","canLoadPoster","canPictureInPicture","canPlay","canSeek","ended","fullscreen","isAirPlayConnected","isGoogleCastConnected","live","liveEdge","loop","mediaType","muted","paused","pictureInPicture","playing","playsInline","remotePlaybackState","remotePlaybackType","seeking","started","streamType","viewType","waiting"],pm={artist:"",artwork:null,autoplay:!1,autoPlay:!1,clipStartTime:0,clipEndTime:0,controls:!1,currentTime:0,crossorigin:null,crossOrigin:null,duration:-1,fullscreenOrientation:"landscape",googleCast:{},load:"visible",posterLoad:"visible",logLevel:"silent",loop:!1,muted:!1,paused:!0,playsinline:!1,playsInline:!1,playbackRate:1,poster:"",preload:"metadata",preferNativeHLS:!1,src:"",title:"",controlsDelay:2e3,hideControlsOnMouseLeave:!1,viewType:"unknown",streamType:"unknown",volume:1,liveEdgeTolerance:10,minLiveDVRWindow:60,keyDisabled:!1,keyTarget:"player",keyShortcuts:N1,storage:null};class vl extends Vs{constructor(t,e){super(),this.ma=t,this.Ma=e}async onAttach(t){if(P)return;const e=this.$props[this.ma]();if(e==="eager")requestAnimationFrame(this.Ma);else if(e==="idle")zf(this.Ma);else if(e==="visible"){let i,n=new IntersectionObserver(r=>{this.scope&&r[0].isIntersecting&&(i==null||i(),i=void 0,this.Ma())});n.observe(t),i=Y(()=>n.disconnect())}}}class mm{constructor(t,e){this.W=t,this.a=e,this.c=(i,...n)=>{P||this.W(new lt(i,{detail:n==null?void 0:n[0],trigger:n==null?void 0:n[1]}))}}async Ha(t,e){if(!P)return ya(async()=>{var St,gt,ct,et,T,x,D;this.a;const{autoPlay:i,canPlay:n,started:r,duration:o,seekable:l,buffered:c,remotePlaybackInfo:u,playsInline:h,savedState:d,source:f}=this.a.$state;if(n())return;const p={duration:(t==null?void 0:t.duration)??o(),seekable:(t==null?void 0:t.seekable)??l(),buffered:(t==null?void 0:t.buffered)??c(),provider:this.a.$provider()};this.c("can-play",p,e),Ie();let g=this.a.$provider(),{storage:b,qualities:y}=this.a,{muted:w,volume:S,clipStartTime:N,playbackRate:j}=this.a.$props;await((St=b==null?void 0:b.onLoad)==null?void 0:St.call(b,f()));const V=(gt=d())==null?void 0:gt.currentTime,K=(ct=d())==null?void 0:ct.paused,ot=await(b==null?void 0:b.getTime()),C=V??ot??N(),k=K||K!==!1&&!r()&&i();if(g){g.setVolume(await(b==null?void 0:b.getVolume())??S()),g.setMuted(await(b==null?void 0:b.getMuted())??w());const st=await(b==null?void 0:b.getAudioGain())??1;st>1&&((T=(et=g.audioGain)==null?void 0:et.setGain)==null||T.call(et,st)),(x=g.setPlaybackRate)==null||x.call(g,await(b==null?void 0:b.getPlaybackRate())??j()),(D=g.setPlaysInline)==null||D.call(g,h()),C>0&&g.setCurrentTime(C)}const _=await(b==null?void 0:b.getVideoQuality());if(_&&y.length){let st=null,At=1/0;for(const Et of y){const Rt=Math.abs(_.width-Et.width)+Math.abs(_.height-Et.height)+(_.bitrate?Math.abs(_.bitrate-(Et.bitrate??0)):0);Rt<At&&(st=Et,At=Rt)}st&&(st.selected=!0)}n()&&k?await this.lj(e):ot&&ot>0&&this.c("started",void 0,e),u.set(null)})}async lj(t){const{player:e,$state:{autoPlaying:i,muted:n}}=this.a;i.set(!0);const r=new lt("auto-play-attempt",{trigger:t});try{await e.play(r)}catch{}}}class gm{constructor(){this.i=new Map}k(t,e){this.i.set(t,e)}ye(t){const e=this.sg(t);return this.i.delete(t),e}sg(t){return this.i.get(t)}vb(t){this.i.delete(t)}Qm(){this.i.clear()}}class nu{constructor(){this.xc=!1,this.ze=ea(),this.i=new Map}get Rm(){return this.i.size}get Sm(){return this.xc}async Tm(){this.xc||await this.ze.promise}k(t,e){if(this.xc){e();return}this.i.delete(t),this.i.set(t,e)}ye(t){var e;(e=this.i.get(t))==null||e(),this.i.delete(t)}Ya(){this.tg(),this.xc=!0,this.i.size>0&&this.tg()}aa(){this.xc=!1}A(){this.aa(),this.i.clear(),this.ug()}tg(){for(const t of this.i.keys())this.ye(t);this.ug()}ug(){this.ze.resolve(),this.ze=ea()}}class bm extends Vs{constructor(t,e,i){super(),this.Ca=t,this.g=e,this.a=i,this.Ac=new nu,this.Ge=!1,this.D=i.$provider,this.zc=new g1,this.qd=new ip,this.cb=new Lc}onAttach(){this.listen("fullscreen-change",this.Rd.bind(this))}onConnect(){const t=Object.getOwnPropertyNames(Object.getPrototypeOf(this)),e=this.Ij.bind(this);for(const i of t)i.startsWith("media-")&&this.listen(i,e);this.Jj(),m(this.Kj.bind(this)),m(this.Lj.bind(this)),m(this.Mj.bind(this)),m(this.Nj.bind(this)),m(this.Oj.bind(this)),m(this.Pj.bind(this)),m(this.Qj.bind(this))}onDestroy(){this.Ac.A()}Jj(){const{load:t}=this.$props,{canLoad:e}=this.$state;if(t()!=="play"||e())return;const i=this.listen("media-play-request",n=>{this.Hg(n),i()})}Kj(){const t=this.D(),e=this.$state.canPlay();return t&&e&&this.Ac.Ya(),()=>{this.Ac.aa()}}Ij(t){t.stopPropagation(),!t.defaultPrevented&&this[t.type]&&($(this.D)?this[t.type](t):this.Ac.k(t.type,()=>{$(this.D)&&this[t.type](t)}))}async Bc(t){if(P)return;const{canPlay:e,paused:i,autoPlaying:n}=this.$state;if(!this.Hg(t)&&$(i)){t&&this.g.i.k("media-play-request",t);try{const r=$(this.D);return Br(r,$(e)),await r.play()}catch(r){const o=this.createEvent("play-fail",{detail:An(r),trigger:t});throw o.autoPlay=n(),this.Ca.W(o),r}}}Hg(t){const{load:e}=this.$props,{canLoad:i}=this.$state;if(e()==="play"&&!i()){const n=this.createEvent("media-start-loading",{trigger:t});return this.dispatchEvent(n),this.Ac.k("media-play-request",async()=>{try{await this.Bc(n)}catch{}}),!0}return!1}async Fe(t){if(P)return;const{canPlay:e,paused:i}=this.$state;if(!$(i)){t&&this.g.i.k("media-pause-request",t);try{const n=$(this.D);return Br(n,$(e)),await n.pause()}catch(n){throw this.g.i.vb("media-pause-request"),n}}}Ig(t,e){const{audioGain:i,canSetAudioGain:n}=this.$state;if(i()===t)return;const r=this.D();if(!(r!=null&&r.audioGain)||!n())throw Error("[vidstack] audio gain api not available");e&&this.g.i.k("media-audio-gain-change-request",e),r.audioGain.setGain(t)}Jg(t){if(P)return;const{canPlay:e,live:i,liveEdge:n,canSeek:r,liveSyncPosition:o,seekableEnd:l,userBehindLiveEdge:c}=this.$state;if(c.set(!1),$(()=>!i()||n()||!r()))return;const u=$(this.D);Br(u,$(e)),t&&this.g.i.k("media-seek-request",t);const h=l()-2;u.setCurrentTime(Math.min(h,o()??h))}async Kg(t="prefer-media",e){if(P)return;const i=this.Lg(t);if(Cl(t,i),!i.active)return $(this.$state.pictureInPicture)&&(this.Ge=!0,await this.He(e)),e&&this.g.i.k("media-enter-fullscreen-request",e),i.enter()}async Mg(t="prefer-media",e){if(P)return;const i=this.Lg(t);if(Cl(t,i),!!i.active){e&&this.g.i.k("media-exit-fullscreen-request",e);try{const n=await i.exit();return this.Ge&&$(this.$state.canPictureInPicture)&&await this.Ie(),n}finally{this.Ge=!1}}}Lg(t){const e=$(this.D);return t==="prefer-media"&&this.qd.supported||t==="media"?this.qd:e==null?void 0:e.fullscreen}async Ie(t){if(!P&&(this.Ng(),!this.$state.pictureInPicture()))return t&&this.g.i.k("media-enter-pip-request",t),await this.D().pictureInPicture.enter()}async He(t){if(!P&&(this.Ng(),!!this.$state.pictureInPicture()))return t&&this.g.i.k("media-exit-pip-request",t),await this.D().pictureInPicture.exit()}Ng(){if(!this.$state.canPictureInPicture())throw Error("[vidstack] no pip support")}Lj(){this.zc.defaultDelay=this.$props.controlsDelay()}Mj(){var i,n;const{canSetAudioGain:t}=this.$state,e=!!((n=(i=this.D())==null?void 0:i.audioGain)!=null&&n.supported);t.set(e)}Nj(){var i,n;const{canAirPlay:t}=this.$state,e=!!((n=(i=this.D())==null?void 0:i.airPlay)!=null&&n.supported);t.set(e)}Oj(){const{canGoogleCast:t,source:e}=this.$state,i=wc&&!Pa&&yp(e());t.set(i)}Pj(){var i,n;const{canFullscreen:t}=this.$state,e=this.qd.supported||!!((n=(i=this.D())==null?void 0:i.fullscreen)!=null&&n.supported);t.set(e)}Qj(){var i,n;const{canPictureInPicture:t}=this.$state,e=!!((n=(i=this.D())==null?void 0:i.pictureInPicture)!=null&&n.supported);t.set(e)}async"media-airplay-request"(t){try{await this.Og(t)}catch{}}async Og(t){var e;try{const i=(e=this.D())==null?void 0:e.airPlay;if(!(i!=null&&i.supported))throw Error("No AirPlay adapter.");return t&&this.g.i.k("media-airplay-request",t),await i.prompt()}catch(i){throw this.g.i.vb("media-airplay-request"),i}}async"media-google-cast-request"(t){try{await this.Pg(t)}catch{}}async Pg(t){try{const{canGoogleCast:e}=this.$state;if(!$(e)){const n=Error("Cast not available.");throw n.code="CAST_NOT_AVAILABLE",n}if(bi("https://www.gstatic.com"),!this.rd){const n=await ie(()=>import("./vidstack-Ci28C5f8-CTyhVKy_.js"),__vite__mapDeps([14,1,2,5,6,7])).then(function(r){return r.d});this.rd=new n.GoogleCastLoader}await this.rd.prompt(this.a),t&&this.g.i.k("media-google-cast-request",t);const i=$(this.$state.remotePlaybackState)!=="disconnected";i&&this.$state.savedState.set({paused:$(this.$state.paused),currentTime:$(this.$state.currentTime)}),this.$state.remotePlaybackLoader.set(i?this.rd:null)}catch(e){throw this.g.i.vb("media-google-cast-request"),e}}"media-audio-track-change-request"(t){const{logger:e,audioTracks:i}=this.a;if(i.readonly)return;const n=t.detail,r=i[n];if(r){const o=t.type;this.g.i.k(o,t),r.selected=!0}}async"media-enter-fullscreen-request"(t){try{await this.Kg(t.detail,t)}catch(e){this._c(e,t)}}async"media-exit-fullscreen-request"(t){try{await this.Mg(t.detail,t)}catch(e){this._c(e,t)}}async Rd(t){const e=$(this.$props.fullscreenOrientation),i=t.detail;if(!(kt(e)||e==="none"||!this.cb.supported))if(i){if(this.cb.locked)return;this.dispatch("media-orientation-lock-request",{detail:e,trigger:t})}else this.cb.locked&&this.dispatch("media-orientation-unlock-request",{trigger:t})}_c(t,e){this.Ca.W(this.createEvent("fullscreen-error",{detail:An(t)}))}async"media-orientation-lock-request"(t){const e=t.type;try{this.g.i.k(e,t),await this.cb.lock(t.detail)}catch{this.g.i.vb(e)}}async"media-orientation-unlock-request"(t){const e=t.type;try{this.g.i.k(e,t),await this.cb.unlock()}catch{this.g.i.vb(e)}}async"media-enter-pip-request"(t){try{await this.Ie(t)}catch(e){this.Qg(e,t)}}async"media-exit-pip-request"(t){try{await this.He(t)}catch(e){this.Qg(e,t)}}Qg(t,e){this.Ca.W(this.createEvent("picture-in-picture-error",{detail:An(t)}))}"media-live-edge-request"(t){const{live:e,liveEdge:i,canSeek:n}=this.$state;if(!(!e()||i()||!n())){this.g.i.k("media-seek-request",t);try{this.Jg()}catch{this.g.i.vb("media-seek-request")}}}async"media-loop-request"(t){try{this.g.Pb=!0,this.g.Cc=!0,await this.Bc(t)}catch{this.g.Pb=!1}}"media-user-loop-change-request"(t){this.$state.userPrefersLoop.set(t.detail)}async"media-pause-request"(t){if(!this.$state.paused())try{await this.Fe(t)}catch{}}async"media-play-request"(t){if(this.$state.paused())try{await this.Bc(t)}catch{}}"media-rate-change-request"(t){const{playbackRate:e,canSetPlaybackRate:i}=this.$state;if(e()===t.detail||!i())return;const n=this.D();n!=null&&n.setPlaybackRate&&(this.g.i.k("media-rate-change-request",t),n.setPlaybackRate(t.detail))}"media-audio-gain-change-request"(t){try{this.Ig(t.detail,t)}catch{}}"media-quality-change-request"(t){var o,l;const{qualities:e,storage:i,logger:n}=this.a;if(e.readonly)return;this.g.i.k("media-quality-change-request",t);const r=t.detail;if(r<0)e.autoSelect(t),t.isOriginTrusted&&((o=i==null?void 0:i.setVideoQuality)==null||o.call(i,null));else{const c=e[r];c&&(c.selected=!0,t.isOriginTrusted&&((l=i==null?void 0:i.setVideoQuality)==null||l.call(i,{id:c.id,width:c.width,height:c.height,bitrate:c.bitrate})))}}"media-pause-controls-request"(t){const e=t.type;this.g.i.k(e,t),this.zc.pause(t)}"media-resume-controls-request"(t){const e=t.type;this.g.i.k(e,t),this.zc.resume(t)}"media-seek-request"(t){const{seekableStart:e,seekableEnd:i,ended:n,canSeek:r,live:o,userBehindLiveEdge:l,clipStartTime:c}=this.$state,u=t.detail;n()&&(this.g.Cc=!0);const h=t.type;this.g.Ka=!1,this.g.i.vb(h);const d=u+c(),f=Math.floor(d)===Math.floor(i()),p=f?i():Math.min(Math.max(e()+.1,d),i()-.1);!Number.isFinite(p)||!r()||(this.g.i.k(h,t),this.D().setCurrentTime(p),o()&&t.isOriginTrusted&&Math.abs(i()-p)>=2&&l.set(!0))}"media-seeking-request"(t){const e=t.type;this.g.i.k(e,t),this.$state.seeking.set(!0),this.g.Ka=!0}"media-start-loading"(t){if(this.$state.canLoad())return;const e=t.type;this.g.i.k(e,t),this.Ca.W(this.createEvent("can-load"))}"media-poster-start-loading"(t){if(this.$state.canLoadPoster())return;const e=t.type;this.g.i.k(e,t),this.Ca.W(this.createEvent("can-load-poster"))}"media-text-track-change-request"(t){const{index:e,mode:i}=t.detail,n=this.a.textTracks[e];if(n){const r=t.type;this.g.i.k(r,t),n.setMode(i,t)}}"media-mute-request"(t){if(this.$state.muted())return;const e=t.type;this.g.i.k(e,t),this.D().setMuted(!0)}"media-unmute-request"(t){const{muted:e,volume:i}=this.$state;if(!e())return;const n=t.type;this.g.i.k(n,t),this.a.$provider().setMuted(!1),i()===0&&(this.g.i.k(n,t),this.D().setVolume(.25))}"media-volume-change-request"(t){const{muted:e,volume:i}=this.$state,n=t.detail;if(i()===n)return;const r=t.type;this.g.i.k(r,t),this.D().setVolume(n),n>0&&e()&&(this.g.i.k(r,t),this.D().setMuted(!1))}Ra(t,e,i){}}function Br(s,t){if(!(s&&t))throw Error("[vidstack] media not ready")}function Cl(s,t){if(!(t!=null&&t.supported))throw Error("[vidstack] no fullscreen support")}class ym{constructor(){this.Ka=!1,this.Pb=!1,this.Cc=!1,this.i=new gm}}const vm=new Set(["auto-play","auto-play-fail","can-load","sources-change","source-change","load-start","abort","error","loaded-metadata","loaded-data","can-play","play","play-fail","pause","playing","seeking","seeked","waiting"]);class Cm extends Vs{constructor(t,e){super(),this.g=t,this.a=e,this.v=new Map,this.sd=!1,this.td=!1,this.Dc=!1,this.Le=null,this.seeking=_s(i=>{const{seeking:n,realCurrentTime:r,paused:o}=this.$state;n.set(!0),r.set(i.detail),this.E("media-seeking-request",i),o()&&(this.Qb=i,this.Me())},150,{leading:!0}),this.Me=qa(()=>{if(!this.Qb)return;this.td=!0;const{waiting:i,playing:n}=this.$state;i.set(!0),n.set(!1);const r=this.createEvent("waiting",{trigger:this.Qb});this.v.set("waiting",r),this.dispatch(r),this.Qb=void 0,this.td=!1},300)}onAttach(t){t.setAttribute("aria-busy","true"),this.listen("fullscreen-change",this["fullscreen-change"].bind(this)),this.listen("fullscreen-error",this["fullscreen-error"].bind(this)),this.listen("orientation-change",this["orientation-change"].bind(this))}onConnect(t){m(this.Rj.bind(this)),this.Sj(),this.Tj(),this.Uj(),this.Vj(),Y(this.Wj.bind(this))}onDestroy(){const{audioTracks:t,qualities:e,textTracks:i}=this.a;t[X.A](),e[X.A](),i[X.A](),this.Je()}W(t){if(!this.scope)return;const e=t.type;ya(()=>{var i;return(i=this[t.type])==null?void 0:i.call(this,t)}),P||(vm.has(e)&&this.v.set(e,t),this.dispatch(t))}Vj(){this.Dc&&(requestAnimationFrame(()=>{this.scope&&this.a.remote.play(new lt("dom-connect"))}),this.Dc=!1)}Wj(){var t;this.Dc||(this.Dc=!this.$state.paused(),(t=this.a.$provider())==null||t.pause())}wb(){this.Rg(),this.sd=!1,this.g.Cc=!1,this.g.Pb=!1,this.td=!1,this.Qb=void 0,this.v.clear()}E(t,e){const i=this.g.i.ye(t);i&&(e.request=i,e.triggers.add(i))}Sj(){this.Ke(),this.Sg();const t=this.a.textTracks;E(t,"add",this.Ke.bind(this)),E(t,"remove",this.Ke.bind(this)),E(t,"mode-change",this.Sg.bind(this))}Tj(){const t=this.a.qualities;E(t,"add",this.md.bind(this)),E(t,"remove",this.md.bind(this)),E(t,"change",this._a.bind(this)),E(t,"auto-change",this.Xj.bind(this)),E(t,"readonly-change",this.Yj.bind(this))}Uj(){const t=this.a.audioTracks;E(t,"add",this.Tg.bind(this)),E(t,"remove",this.Tg.bind(this)),E(t,"change",this.Zj.bind(this))}Ke(t){const{textTracks:e}=this.$state;e.set(this.a.textTracks.toArray()),this.dispatch("text-tracks-change",{detail:e(),trigger:t})}Sg(t){t&&this.E("media-text-track-change-request",t);const e=this.a.textTracks.selected,{textTrack:i}=this.$state;i()!==e&&(i.set(e),this.dispatch("text-track-change",{detail:e,trigger:t}))}Tg(t){const{audioTracks:e}=this.$state;e.set(this.a.audioTracks.toArray()),this.dispatch("audio-tracks-change",{detail:e(),trigger:t})}Zj(t){const{audioTrack:e}=this.$state;e.set(this.a.audioTracks.selected),t&&this.E("media-audio-track-change-request",t),this.dispatch("audio-track-change",{detail:e(),trigger:t})}md(t){const{qualities:e}=this.$state;e.set(this.a.qualities.toArray()),this.dispatch("qualities-change",{detail:e(),trigger:t})}_a(t){const{quality:e}=this.$state;e.set(this.a.qualities.selected),t&&this.E("media-quality-change-request",t),this.dispatch("quality-change",{detail:e(),trigger:t})}Xj(){const{qualities:t}=this.a,e=t.auto;this.$state.autoQuality.set(e),e||this.Je()}Ug(){this.Je(),this.Le=m(()=>{const{qualities:t}=this.a,{mediaWidth:e,mediaHeight:i}=this.$state,n=e(),r=i();if(n===0||r===0)return;let o=null,l=1/0;for(const c of t){const u=Math.abs(c.width-n)+Math.abs(c.height-r);u<l&&(l=u,o=c)}o&&t[X.fa](o,!0,new lt("resize",{detail:{width:n,height:r}}))})}Je(){var t;(t=this.Le)==null||t.call(this),this.Le=null}Yj(){this.$state.canSetQuality.set(!this.a.qualities.readonly)}Rj(){const{canSetVolume:t,isGoogleCastConnected:e}=this.$state;if(e()){t.set(!1);return}op().then(t.set)}"provider-change"(t){var n,r;const e=this.a.$provider(),i=t.detail;(e==null?void 0:e.type)!==(i==null?void 0:i.type)&&((n=e==null?void 0:e.destroy)==null||n.call(e),(r=e==null?void 0:e.scope)==null||r.dispose(),this.a.$provider.set(t.detail),e&&t.detail===null&&this.Vg(t))}"provider-loader-change"(t){}"auto-play"(t){this.$state.autoPlayError.set(null)}"auto-play-fail"(t){this.$state.autoPlayError.set(t.detail),this.wb()}"can-load"(t){this.$state.canLoad.set(!0),this.v.set("can-load",t),this.a.textTracks[Q._](),this.E("media-start-loading",t)}"can-load-poster"(t){this.$state.canLoadPoster.set(!0),this.v.set("can-load-poster",t),this.E("media-poster-start-loading",t)}"media-type-change"(t){const e=this.v.get("source-change");e&&t.triggers.add(e);const i=this.$state.viewType();this.$state.mediaType.set(t.detail);const n=this.$state.providedViewType(),r=n==="unknown"?t.detail:n;i!==r&&(P?this.$state.inferredViewType.set(r):setTimeout(()=>{requestAnimationFrame(()=>{this.scope&&(this.$state.inferredViewType.set(t.detail),this.dispatch("view-type-change",{detail:r,trigger:t}))})},0))}"stream-type-change"(t){const e=this.v.get("source-change");e&&t.triggers.add(e);const{streamType:i,inferredStreamType:n}=this.$state;n.set(t.detail),t.detail=i()}"rate-change"(t){var n;const{storage:e}=this.a,{canPlay:i}=this.$state;this.$state.playbackRate.set(t.detail),this.E("media-rate-change-request",t),i()&&((n=e==null?void 0:e.setPlaybackRate)==null||n.call(e,t.detail))}"remote-playback-change"(t){const{remotePlaybackState:e,remotePlaybackType:i}=this.$state,{type:n,state:r}=t.detail,o=r==="connected";i.set(n),e.set(r);const l=n==="airplay"?"media-airplay-request":"media-google-cast-request";if(o)this.E(l,t);else{const c=this.g.i.sg(l);c&&(t.request=c,t.triggers.add(c))}}"sources-change"(t){const e=this.$state.sources(),i=t.detail;this.$state.sources.set(i),this._j(e,i,t)}_j(t,e,i){let{qualities:n}=this.a,r=!1,o=!1;for(const l of t){if(!ia(l))continue;if(!e.some(u=>u.src===l.src)){const u=n.getBySrc(l.src);u&&(n[X.dc](u,i),o=!0)}}o&&!n.length&&(this.$state.savedState.set(null),n[X.A](i));for(const l of e){if(!ia(l)||n.getBySrc(l.src))continue;const c={id:l.id??l.height+"p",bitrate:null,codec:null,...l,selected:!1};n[X.ea](c,i),r=!0}r&&!n[he.Ja]&&(this.Ug(),n[he.Ja]=this.Ug.bind(this),n[he.Xa](!0,i))}"source-change"(t){var i,n;t.isQualityChange=((i=t.originEvent)==null?void 0:i.type)==="quality-change";const e=t.detail;this.Vg(t,t.isQualityChange),this.v.set(t.type,t),this.$state.source.set(e),(n=this.el)==null||n.setAttribute("aria-busy","true")}Vg(t,e=!1){const{audioTracks:i,qualities:n}=this.a;if(!e){i[X.A](t),n[X.A](t),cl(this.$state,e),this.wb();return}cl(this.$state,e),this.wb()}abort(t){const e=this.v.get("source-change");e&&t.triggers.add(e);const i=this.v.get("can-load");i&&!t.triggers.hasType("can-load")&&t.triggers.add(i)}"load-start"(t){const e=this.v.get("source-change");e&&t.triggers.add(e)}error(t){this.$state.error.set(t.detail);const e=this.v.get("abort");e&&t.triggers.add(e)}"loaded-metadata"(t){const e=this.v.get("load-start");e&&t.triggers.add(e)}"loaded-data"(t){const e=this.v.get("load-start");e&&t.triggers.add(e)}"can-play"(t){var i;const e=this.v.get("loaded-metadata");e&&t.triggers.add(e),this.Wg(t.detail),(i=this.el)==null||i.setAttribute("aria-busy","false")}"can-play-through"(t){this.Wg(t.detail);const e=this.v.get("can-play");e&&t.triggers.add(e)}Wg(t){const{seekable:e,buffered:i,intrinsicDuration:n,canPlay:r}=this.$state;r.set(!0),i.set(t.buffered),e.set(t.seekable);const o=Ni(t.seekable)??1/0;n.set(o)}"duration-change"(t){const{live:e,intrinsicDuration:i,ended:n}=this.$state,r=t.detail;if(!e()){const o=Number.isNaN(r)?0:r;i.set(o),n()&&this.Xg(t)}}progress(t){const{buffered:e,bufferedEnd:i,seekable:n,seekableEnd:r,live:o,intrinsicDuration:l}=this.$state,{buffered:c,seekable:u}=t.detail,h=Ni(c)??1/0,d=c.length!==e().length,f=h>i(),p=Ni(u)??1/0,g=u.length!==n().length,b=p>r();(d||f)&&e.set(c),(g||b)&&n.set(u),o()&&(l.set(p),this.dispatch("duration-change",{detail:p,trigger:t}))}play(t){const{paused:e,autoPlayError:i,ended:n,autoPlaying:r,playsInline:o,pointer:l,muted:c,viewType:u,live:h,userBehindLiveEdge:d}=this.$state;if(this.$j(),!e()){t.stopImmediatePropagation();return}t.autoPlay=r();const f=this.v.get("waiting");f&&t.triggers.add(f),this.E("media-play-request",t),this.v.set("play",t),e.set(!1),i.set(null),t.autoPlay&&(this.W(this.createEvent("auto-play",{detail:{muted:c()},trigger:t})),r.set(!1)),(n()||this.g.Cc)&&(this.g.Cc=!1,n.set(!1),this.W(this.createEvent("replay",{trigger:t}))),!o()&&u()==="video"&&l()==="coarse"&&this.a.remote.enterFullscreen("prefer-media",t),h()&&!d()&&this.a.remote.seekToLiveEdge(t)}$j(t){if(!$(this.a.$provider))return;const{ended:i,seekableStart:n,clipStartTime:r,clipEndTime:o,realCurrentTime:l,duration:c}=this.$state,u=l()<r()||o()>0&&l()>=o()||Math.abs(l()-c())<.1||i();return u&&this.dispatch("media-seek-request",{detail:(r()>0?0:n())+.1,trigger:t}),u}"play-fail"(t){const{muted:e,autoPlaying:i}=this.$state,n=this.v.get("play");n&&t.triggers.add(n),this.E("media-play-request",t);const{paused:r,playing:o}=this.$state;r.set(!0),o.set(!1),this.wb(),this.v.set("play-fail",t),t.autoPlay&&(this.W(this.createEvent("auto-play-fail",{detail:{muted:e(),error:t.detail},trigger:t})),i.set(!1))}playing(t){const e=this.v.get("play"),i=this.v.get("seeked");e?t.triggers.add(e):i&&t.triggers.add(i),setTimeout(()=>this.wb(),0);const{paused:n,playing:r,live:o,liveSyncPosition:l,seekableEnd:c,started:u,currentTime:h,seeking:d,ended:f}=this.$state;if(n.set(!1),r.set(!0),d.set(!1),f.set(!1),this.g.Pb){this.g.Pb=!1;return}if(o()&&!u()&&h()===0){const p=l()??c()-2;Number.isFinite(p)&&this.a.$provider().setCurrentTime(p)}this.started(t)}started(t){const{started:e}=this.$state;e()||(e.set(!0),this.W(this.createEvent("started",{trigger:t})))}pause(t){var r;(r=this.el)!=null&&r.isConnected||(this.Dc=!0),this.E("media-pause-request",t);const e=this.v.get("seeked");e&&t.triggers.add(e);const{paused:i,playing:n}=this.$state;i.set(!0),n.set(!1),this.sd&&setTimeout(()=>{this.W(this.createEvent("end",{trigger:t})),this.sd=!1},0),this.wb()}"time-update"(t){if(this.g.Pb){t.stopImmediatePropagation();return}const{realCurrentTime:e,played:i,waiting:n,clipEndTime:r}=this.$state,o=r(),l=t.detail;e.set(l.currentTime),i.set(l.played),n.set(!1);for(const c of this.a.textTracks)c[Q.Fb](l.currentTime,t);o>0&&l.currentTime>=o&&(this.sd=!0,this.dispatch("media-pause-request",{trigger:t})),this.ak()}Xg(t){const{duration:e,played:i}=this.$state,n=na(i())??0;this.W(this.createEvent("time-update",{detail:{currentTime:e(),played:new kn(n,e())},trigger:t}))}ak(){var n;const{storage:t}=this.a,{canPlay:e,realCurrentTime:i}=this.$state;e()&&((n=t==null?void 0:t.setTime)==null||n.call(t,i()))}"audio-gain-change"(t){var r;const{storage:e}=this.a,{canPlay:i,audioGain:n}=this.$state;n.set(t.detail),this.E("media-audio-gain-change-request",t),i()&&((r=e==null?void 0:e.setAudioGain)==null||r.call(e,n()))}"volume-change"(t){var l,c;const{storage:e}=this.a,{volume:i,muted:n,canPlay:r}=this.$state,o=t.detail;i.set(o.volume),n.set(o.muted||o.volume===0),this.E("media-volume-change-request",t),this.E(o.muted?"media-mute-request":"media-unmute-request",t),r()&&((l=e==null?void 0:e.setVolume)==null||l.call(e,i()),(c=e==null?void 0:e.setMuted)==null||c.call(e,n()))}seeked(t){const{seeking:e,currentTime:i,realCurrentTime:n,paused:r,seekableEnd:o,ended:l}=this.$state;if(this.g.Ka)e.set(!0),t.stopImmediatePropagation();else if(e()){const c=this.v.get("waiting");c&&t.triggers.add(c);const u=this.v.get("seeking");u&&!t.triggers.has(u)&&t.triggers.add(u),r()&&this.Rg(),e.set(!1),n.set(t.detail),this.E("media-seek-request",t);const h=t==null?void 0:t.originEvent;h!=null&&h.isTrusted&&!/seek/.test(h.type)&&this.started(t)}Math.floor(i())!==Math.floor(o())?l.set(!1):this.end(t)}waiting(t){this.td||this.g.Ka||(t.stopImmediatePropagation(),this.Qb=t,this.Me())}end(t){const{loop:e,ended:i}=this.$state;if(!(!e()&&i())){if(e()){setTimeout(()=>{requestAnimationFrame(()=>{this.dispatch("media-loop-request",{trigger:t})})},10);return}setTimeout(()=>this.mc(t),0)}}mc(t){var l;const{storage:e}=this.a,{paused:i,seeking:n,ended:r,duration:o}=this.$state;this.Xg(t),i()||this.dispatch("pause",{trigger:t}),n()&&this.dispatch("seeked",{detail:o(),trigger:t}),r.set(!0),this.wb(),(l=e==null?void 0:e.setTime)==null||l.call(e,o(),!0),this.dispatch("ended",{trigger:t})}Rg(){this.Me.cancel(),this.$state.waiting.set(!1)}"fullscreen-change"(t){const e=t.detail;this.$state.fullscreen.set(e),this.E(e?"media-enter-fullscreen-request":"media-exit-fullscreen-request",t)}"fullscreen-error"(t){this.E("media-enter-fullscreen-request",t),this.E("media-exit-fullscreen-request",t)}"orientation-change"(t){const e=t.detail.lock;this.E(e?"media-orientation-lock-request":"media-orientation-unlock-request",t)}"picture-in-picture-change"(t){const e=t.detail;this.$state.pictureInPicture.set(e),this.E(e?"media-enter-pip-request":"media-exit-pip-request",t)}"picture-in-picture-error"(t){this.E("media-enter-pip-request",t),this.E("media-exit-pip-request",t)}"title-change"(t){t.trigger&&(t.stopImmediatePropagation(),this.$state.inferredTitle.set(t.detail))}"poster-change"(t){t.trigger&&(t.stopImmediatePropagation(),this.$state.inferredPoster.set(t.detail))}}class wm extends Vs{onSetup(){this.Ib(),!P&&(m(this.ck.bind(this)),m(this.dk.bind(this)),m(this.ek.bind(this)),m(this.Yd.bind(this)),m(this.Da.bind(this)),m(this.fk.bind(this)),m(this.gk.bind(this)),m(this.hk.bind(this)),m(this.ik.bind(this)),m(this.jk.bind(this)),m(this.Ne.bind(this)),m(this.kk.bind(this)),m(this.lk.bind(this)),m(this.ud.bind(this)))}Ib(){var i;const t={duration:"providedDuration",loop:"providedLoop",poster:"providedPoster",streamType:"providedStreamType",title:"providedTitle",viewType:"providedViewType"},e=new Set(["currentTime","paused","playbackRate","volume"]);for(const n of Object.keys(this.$props))e.has(n)||(i=this.$state[t[n]??n])==null||i.set(this.$props[n]());this.$state.muted.set(this.$props.muted()||this.$props.volume()===0)}lk(){const{viewType:t,streamType:e,title:i,poster:n,loop:r}=this.$props,o=this.$state;o.providedPoster.set(n()),o.providedStreamType.set(e()),o.providedViewType.set(t()),o.providedTitle.set(i()),o.providedLoop.set(r())}bk(){}ck(){const{artist:t,artwork:e}=this.$props;this.$state.artist.set(t()),this.$state.artwork.set(e())}ud(){const{title:t}=this.$state;this.dispatch("title-change",{detail:t()})}dk(){const t=this.$props.autoPlay()||this.$props.autoplay();this.$state.autoPlay.set(t),this.dispatch("auto-play-change",{detail:t})}jk(){const t=this.$state.loop();this.dispatch("loop-change",{detail:t})}Yd(){const t=this.$props.controls();this.$state.controls.set(t)}kk(){const{poster:t}=this.$state;this.dispatch("poster-change",{detail:t()})}Da(){const t=this.$props.crossOrigin()??this.$props.crossorigin(),e=t===!0?"":t;this.$state.crossOrigin.set(e)}fk(){const{providedDuration:t}=this.$state;t.set(this.$props.duration())}Ne(){const t=this.$props.playsInline()||this.$props.playsinline();this.$state.playsInline.set(t),this.dispatch("plays-inline-change",{detail:t})}ek(){const{clipStartTime:t,clipEndTime:e}=this.$props;this.$state.clipStartTime.set(t()),this.$state.clipEndTime.set(e())}gk(){this.dispatch("live-change",{detail:this.$state.live()})}ik(){this.$state.liveEdgeTolerance.set(this.$props.liveEdgeTolerance()),this.$state.minLiveDVRWindow.set(this.$props.minLiveDVRWindow())}hk(){this.dispatch("live-edge-change",{detail:this.$state.liveEdge()})}}const ru=class ca extends Vs{constructor(){super()}onConnect(){m(this.mk.bind(this)),m(this.nk.bind(this));const t=this.ok.bind(this);for(const e of ca.Yg)navigator.mediaSession.setActionHandler(e,t);Y(this.Ga.bind(this))}Ga(){for(const t of ca.Yg)navigator.mediaSession.setActionHandler(t,null)}mk(){const{title:t,artist:e,artwork:i,poster:n}=this.$state;navigator.mediaSession.metadata=new MediaMetadata({title:t(),artist:e(),artwork:i()??[{src:n()}]})}nk(){const{canPlay:t,paused:e}=this.$state;navigator.mediaSession.playbackState=t()?e()?"paused":"playing":"none"}ok(t){const e=new lt("media-session-action",{detail:t});switch(t.action){case"play":this.dispatch("media-play-request",{trigger:e});break;case"pause":this.dispatch("media-pause-request",{trigger:e});break;case"seekto":case"seekforward":case"seekbackward":this.dispatch("media-seek-request",{detail:Zt(t.seekTime)?t.seekTime:this.$state.currentTime()+(t.seekOffset??10),trigger:e});break}}};ru.Yg=["play","pause","seekforward","seekbackward","seekto"];let Em=ru,qn=H(!1);P||(E(document,"pointerdown",()=>{qn.set(!1)}),E(document,"keydown",s=>{s.metaKey||s.altKey||s.ctrlKey||qn.set(!0)}));class xe extends me{constructor(){super(...arguments),this.Ec=H(!1)}onConnect(t){m(()=>{if(!qn()){this.Ec.set(!1),Cn(t,!1),this.listen("pointerenter",this.Pe.bind(this)),this.listen("pointerleave",this.Qe.bind(this));return}const e=document.activeElement===t;this.Ec.set(e),Cn(t,e),this.listen("focus",this.Fc.bind(this)),this.listen("blur",this.rk.bind(this))})}focused(){return this.Ec()}Fc(){this.Ec.set(!0),Cn(this.el,!0)}rk(){this.Ec.set(!1),Cn(this.el,!1)}Pe(){wl(this.el,!0)}Qe(){wl(this.el,!1)}}function Cn(s,t){G(s,"data-focus",t),G(s,"data-hocus",t)}function wl(s,t){G(s,"data-hocus",t),G(s,"data-hover",t)}var $m=Object.defineProperty,Tm=Object.getOwnPropertyDescriptor,mt=(s,t,e,i)=>{for(var n=i>1?void 0:i?Tm(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&$m(t,e,n),n};const dt=class Mn extends W{constructor(){super(),this.canPlayQueue=new nu,this.Se=!1,new wm;const t={player:this,qualities:new x1,audioTracks:new $1,storage:null,$provider:H(null),$providerSetup:H(!1),$props:this.$props,$state:this.$state};t.remote=this.remoteControl=new Dp(void 0),t.remote.setPlayer(this),t.textTracks=new E1,t.textTracks[Q.Eb]=this.$state.crossOrigin,t.textRenderers=new C1(t),t.ariaKeys={},this.a=t,Wt(Zi,t),this.orientation=new Lc,new xe,new I1(t);const e=new ym;this.Ca=new Cm(e,t),this.X=new bm(this.Ca,e,t),t.delegate=new mm(this.Ca.W.bind(this.Ca),t),new Em,new vl("load",this.startLoading.bind(this)),new vl("posterLoad",this.startLoadingPoster.bind(this))}get p(){return this.a.$provider()}get wd(){return this.$props}onSetup(){this.sk(),m(this.tk.bind(this)),m(this.uk.bind(this)),m(this.gc.bind(this)),m(this.Gc.bind(this)),m(this.Rb.bind(this)),m(this.Ne.bind(this)),m(this.Re.bind(this))}onAttach(t){t.setAttribute("data-media-player",""),ht(t,"tabindex","0"),ht(t,"role","region"),m(this.vk.bind(this)),P?this.ud():m(this.ud.bind(this)),P?this.Zg():m(this.Zg.bind(this)),E(t,"find-media-player",this.wk.bind(this))}onConnect(t){Cc&&G(t,"data-iphone","");const e=window.matchMedia("(pointer: coarse)");this._g(e),e.onchange=this._g.bind(this);const i=new ResizeObserver(Rs(this.qa.bind(this)));i.observe(t),m(this.qa.bind(this)),this.dispatch("media-player-connect",{detail:this,bubbles:!0,composed:!0}),Y(()=>{i.disconnect(),e.onchange=null})}onDestroy(){this.a.player=null,this.canPlayQueue.A()}ud(){const t=this.$el,{title:e,live:i,viewType:n,providedTitle:r}=this.$state,o=i(),l=Wi(n()),c=l!=="Unknown"?`${o?"Live ":""}${l}`:o?"Live":"Media",u=e();G(this.el,"aria-label",`${c} Player`+(u?` - ${u}`:"")),!P&&(t!=null&&t.hasAttribute("title"))&&(this.Se=!0,t==null||t.removeAttribute("title"))}Zg(){const t=this.orientation.landscape?"landscape":"portrait";this.$state.orientation.set(t),G(this.el,"data-orientation",t),this.qa()}tk(){this.$state.canPlay()&&this.p?this.canPlayQueue.Ya():this.canPlayQueue.aa()}sk(){if(Mn[Fr]){this.setAttributes(Mn[Fr]);return}const t={"data-load":function(){return this.$props.load()},"data-captions":function(){const i=this.$state.textTrack();return!!i&&It(i)},"data-ios-controls":function(){return this.$state.iOSControls()},"data-controls":function(){return this.controls.showing},"data-buffering":function(){const{canLoad:i,canPlay:n,waiting:r}=this.$state;return i()&&(!n()||r())},"data-error":function(){const{error:i}=this.$state;return!!i()},"data-autoplay-error":function(){const{autoPlayError:i}=this.$state;return!!i()}},e={autoPlay:"autoplay",canAirPlay:"can-airplay",canPictureInPicture:"can-pip",pictureInPicture:"pip",playsInline:"playsinline",remotePlaybackState:"remote-state",remotePlaybackType:"remote-type",isAirPlayConnected:"airplay",isGoogleCastConnected:"google-cast"};for(const i of fm){const n="data-"+(e[i]??Jn(i));t[n]=function(){return this.$state[i]()}}delete t.title,Mn[Fr]=t,this.setAttributes(t)}wk(t){t.detail(this)}qa(){if(P||!this.el)return;const t=this.el.clientWidth,e=this.el.clientHeight;this.$state.width.set(t),this.$state.height.set(e),Gt(this.el,"--player-width",t+"px"),Gt(this.el,"--player-height",e+"px")}_g(t){if(P)return;const e=t.matches?"coarse":"fine";G(this.el,"data-pointer",e),this.$state.pointer.set(e),this.qa()}get provider(){return this.p}get controls(){return this.X.zc}set controls(t){this.wd.controls.set(t)}get title(){return $(this.$state.providedTitle)}set title(t){if(this.Se){this.Se=!1;return}this.$state.providedTitle.set(t)}get qualities(){return this.a.qualities}get audioTracks(){return this.a.audioTracks}get textTracks(){return this.a.textTracks}get textRenderers(){return this.a.textRenderers}get duration(){return this.$state.duration()}set duration(t){this.wd.duration.set(t)}get paused(){return $(this.$state.paused)}set paused(t){this.$g(t)}gc(){this.$g(this.$props.paused())}$g(t){t?this.canPlayQueue.k("paused",()=>this.X.Fe()):this.canPlayQueue.k("paused",()=>this.X.Bc())}get muted(){return $(this.$state.muted)}set muted(t){this.wd.muted.set(t)}uk(){this.xk(this.$props.muted())}xk(t){this.canPlayQueue.k("muted",()=>{this.p&&this.p.setMuted(t)})}get currentTime(){return $(this.$state.currentTime)}set currentTime(t){this.ah(t)}Rb(){this.ah(this.$props.currentTime())}ah(t){this.canPlayQueue.k("currentTime",()=>{const{currentTime:e,clipStartTime:i,seekableStart:n,seekableEnd:r}=this.$state;t!==$(e)&&$(()=>{if(!this.p)return;const o=t+i(),l=Math.floor(o)===Math.floor(r()),c=l?r():Math.min(Math.max(n()+.1,o),r()-.1);Number.isFinite(c)&&this.p.setCurrentTime(c)})})}get volume(){return $(this.$state.volume)}set volume(t){this.wd.volume.set(t)}Gc(){this.yk(this.$props.volume())}yk(t){const e=Fa(0,t,1);this.canPlayQueue.k("volume",()=>{this.p&&this.p.setVolume(e)})}get playbackRate(){return $(this.$state.playbackRate)}set playbackRate(t){this.bh(t)}Re(){this.bh(this.$props.playbackRate())}bh(t){this.canPlayQueue.k("rate",()=>{var e,i;this.p&&((i=(e=this.p).setPlaybackRate)==null||i.call(e,t))})}Ne(){this.zk(this.$props.playsInline())}zk(t){this.canPlayQueue.k("playsinline",()=>{var e,i;this.p&&((i=(e=this.p).setPlaysInline)==null||i.call(e,t))})}vk(){var i;let t=this.$props.storage(),e=q(t)?new y1:t;if(e!=null&&e.onChange){const{source:n}=this.$state,r=q(t)?t:(i=this.el)==null?void 0:i.id,o=es(this.Ak.bind(this));m(()=>e.onChange(n(),o(),r||void 0))}this.a.storage=e,this.a.textTracks.setStorage(e),Y(()=>{var n;(n=e==null?void 0:e.onDestroy)==null||n.call(e),this.a.storage=null,this.a.textTracks.setStorage(null)})}Ak(){const{clipStartTime:t,clipEndTime:e}=this.$props,{source:i}=this.$state,n=i();return n.src?`${n.src}:${t()}:${e()}`:null}async play(t){return this.X.Bc(t)}async pause(t){return this.X.Fe(t)}async enterFullscreen(t,e){return this.X.Kg(t,e)}async exitFullscreen(t,e){return this.X.Mg(t,e)}enterPictureInPicture(t){return this.X.Ie(t)}exitPictureInPicture(t){return this.X.He(t)}seekToLiveEdge(t){this.X.Jg(t)}startLoading(t){this.a.delegate.c("can-load",void 0,t)}startLoadingPoster(t){this.a.delegate.c("can-load-poster",void 0,t)}requestAirPlay(t){return this.X.Og(t)}requestGoogleCast(t){return this.X.Pg(t)}setAudioGain(t,e){return this.X.Ig(t,e)}destroy(){this.a.remote.setPlayer(null),this.dispatch("destroy")}};dt.props=pm;dt.state=zi;mt([J],dt.prototype,"canPlayQueue",2);mt([J],dt.prototype,"remoteControl",2);mt([J],dt.prototype,"provider",1);mt([J],dt.prototype,"controls",1);mt([J],dt.prototype,"orientation",2);mt([J],dt.prototype,"title",1);mt([J],dt.prototype,"qualities",1);mt([J],dt.prototype,"audioTracks",1);mt([J],dt.prototype,"textTracks",1);mt([J],dt.prototype,"textRenderers",1);mt([J],dt.prototype,"duration",1);mt([J],dt.prototype,"paused",1);mt([J],dt.prototype,"muted",1);mt([J],dt.prototype,"currentTime",1);mt([J],dt.prototype,"volume",1);mt([J],dt.prototype,"playbackRate",1);mt([Tt],dt.prototype,"play",1);mt([Tt],dt.prototype,"pause",1);mt([Tt],dt.prototype,"enterFullscreen",1);mt([Tt],dt.prototype,"exitFullscreen",1);mt([Tt],dt.prototype,"enterPictureInPicture",1);mt([Tt],dt.prototype,"exitPictureInPicture",1);mt([Tt],dt.prototype,"seekToLiveEdge",1);mt([Tt],dt.prototype,"startLoading",1);mt([Tt],dt.prototype,"startLoadingPoster",1);mt([Tt],dt.prototype,"requestAirPlay",1);mt([Tt],dt.prototype,"requestGoogleCast",1);mt([Tt],dt.prototype,"setAudioGain",1);let Sm=dt;function xm(s,t){return fetch(s,t).then(e=>e.text()).then(e=>/type="static"/.test(e)?"on-demand":"live")}function au(s,t){return fetch(s,t).then(e=>e.text()).then(e=>{const i=km(e);if(i)return au(/^https?:/.test(i)?i:new URL(i,s).href,t);const n=/EXT-X-PLAYLIST-TYPE:\s*VOD/.test(e)?"on-demand":"live";return n==="live"&&Pm(e)>=10&&(/#EXT-X-DVR-ENABLED:\s*true/.test(e)||e.includes("#EXT-X-DISCONTINUITY"))?"live:dvr":n})}function km(s){const t=s.match(/#EXT-X-STREAM-INF:[^\n]+(\n[^\n]+)*/g);return t?t[0].split(`
`)[1].trim():null}function Pm(s){const t=s.split(`
`);for(const e of t)if(e.startsWith("#EXT-X-TARGETDURATION")){const i=parseFloat(e.split(":")[1]);if(!isNaN(i))return i}return-1}const ua=new Map;class Am{constructor(t,e,i,n=[]){this.xd=t,this.a=e,this.Y=i,this.Te=!1;const r=new cm,o=new um,l=new ja,c=new _1,u=new dm,h=new hm,d=[u,h];this.Ue=es(()=>{const p=e.$state.remotePlaybackLoader(),g=e.$props.preferNativeHLS()?[l,c,r,o,...d,...n]:[o,l,c,r,...d,...n];return p?[p,...g]:g});const{$state:f}=e;f.sources.set(El(e.$props.src()));for(const p of f.sources()){const g=this.Ue().find(y=>y.canPlay(p));if(!g)continue;const b=g.mediaType(p);this.a.$state.source.set(p),this.a.$state.mediaType.set(b),this.a.$state.inferredViewType.set(b),this.Y.set(g),this.Te=!0}}get c(){return this.a.delegate.c}connect(){const t=this.Y();this.Te&&(this.ch(this.a.$state.source(),t),this.dh(t),this.Te=!1),m(this.Bk.bind(this)),m(this.Ck.bind(this)),m(this.Dk.bind(this)),m(this.Ek.bind(this)),m(this.Fk.bind(this))}Bk(){this.c("sources-change",[...El(this.a.$props.src()),...this.xd()])}Ck(){var o;const{$state:t}=this.a,e=t.sources(),i=$(t.source),n=this.eh(i,e);if(((o=e[0])==null?void 0:o.src)&&!n.src&&!n.type){const{crossOrigin:l}=t,c=Li(l()),u=new AbortController;return Promise.all(e.map(h=>q(h.src)&&h.type==="?"?fetch(h.src,{method:"HEAD",credentials:c,signal:u.signal}).then(d=>(h.type=d.headers.get("content-type")||"??",ua.set(h.src,h.type),h)).catch(()=>h):h)).then(h=>{u.signal.aborted||(this.eh($(t.source),h),Ie())}),()=>u.abort()}Ie()}eh(t,e){let i={src:"",type:""},n=null,r=new lt("sources-change",{detail:{sources:e}}),o=this.Ue(),{started:l,paused:c,currentTime:u,quality:h,savedState:d}=this.a.$state;for(const f of e){const p=o.find(g=>g.canPlay(f));if(p){i=f,n=p;break}}if(ia(i)){const f=h(),p=e.find(g=>g.src===(f==null?void 0:f.src));$(l)?d.set({paused:$(c),currentTime:$(u)}):d.set(null),p&&(i=p,r=new lt("quality-change",{detail:{quality:f}}))}return Tl(t,i)||this.ch(i,n,r),n!==$(this.Y)&&this.dh(n,r),i}ch(t,e,i){this.c("source-change",t,i),this.c("media-type-change",(e==null?void 0:e.mediaType(t))||"unknown",i)}dh(t,e){this.a.$providerSetup.set(!1),this.c("provider-change",null,e),t&&$(()=>{var i;return(i=t.preconnect)==null?void 0:i.call(t,this.a)}),this.Y.set(t),this.c("provider-loader-change",t,e)}Dk(){const t=this.a.$provider();if(!(!t||$(this.a.$providerSetup))){if(this.a.$state.canLoad()){Dt(()=>t.setup(),t.scope),this.a.$providerSetup.set(!0);return}$(()=>{var e;return(e=t.preconnect)==null?void 0:e.call(t)})}}Ek(){if(!this.a.$providerSetup())return;const t=this.a.$provider(),e=this.a.$state.source(),i=$(this.a.$state.crossOrigin),n=$(this.a.$props.preferNativeHLS);if(!Tl(t==null?void 0:t.currentSrc,e)){if(this.a.$state.canLoad()){const r=new AbortController;return gi(e)?(n||!Aa())&&au(e.src,{credentials:Li(i),signal:r.signal}).then(o=>{this.c("stream-type-change",o)}).catch(fe):Na(e)?xm(e.src,{credentials:Li(i),signal:r.signal}).then(o=>{this.c("stream-type-change",o)}).catch(fe):this.c("stream-type-change","on-demand"),$(()=>{const o=$(this.a.$state.preload);return t==null?void 0:t.loadSource(e,o).catch(l=>{})}),()=>r.abort()}try{q(e.src)&&bi(new URL(e.src).origin)}catch{}}}Fk(){const t=this.Y(),{providedPoster:e,source:i,canLoadPoster:n}=this.a.$state;if(!t||!t.loadPoster||!i()||!n()||e())return;const r=new AbortController,o=new lt("source-change",{detail:i});return t.loadPoster(i(),this.a,r).then(l=>{this.c("poster-change",l||"",o)}).catch(()=>{this.c("poster-change","",o)}),()=>{r.abort()}}}function El(s){return(xt(s)?s:[s]).map(t=>q(t)?{src:t,type:$l(t)}:{...t,type:$l(t.src,t.type)}).sort(t=>t.type==="?"?1:-1)}function $l(s,t){return q(t)&&t.length?t:q(s)&&ua.has(s)?ua.get(s):!t&&gi({src:s,type:""})?"application/x-mpegurl":!t&&Na({src:s,type:""})?"application/dash+xml":!q(s)||s.startsWith("blob:")?"video/object":s.includes("youtube")||s.includes("youtu.be")?"video/youtube":s.includes("vimeo")&&!s.includes("progressive_redirect")&&!s.includes(".m3u8")?"video/vimeo":"?"}function Tl(s,t){return(s==null?void 0:s.src)===(t==null?void 0:t.src)&&(s==null?void 0:s.type)===(t==null?void 0:t.type)}class Mm{constructor(t,e){this.yd=t,this.a=e,this.fh=[],m(this.Gk.bind(this))}Gk(){const t=this.yd();for(const e of this.fh)if(!t.some(i=>i.id===e.id)){const i=e.id&&this.a.textTracks.getById(e.id);i&&this.a.textTracks.remove(i)}for(const e of t){const i=e.id||ai.createId(e);this.a.textTracks.getById(i)||(e.id=i,this.a.textTracks.add(e))}this.fh=t}}var Lm=Object.defineProperty,Nm=Object.getOwnPropertyDescriptor,Dm=(s,t,e,i)=>{for(var n=i>1?void 0:i?Nm(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&Lm(t,e,n),n};let nr=class extends W{constructor(){super(...arguments),this.xd=H([]),this.yd=H([]),this.Y=null,this.Ve=-1}onSetup(){this.a=at(),this.gh=new Am(this.xd,this.a,this.$state.loader,this.$props.loaders())}onAttach(t){t.setAttribute("data-media-provider","")}onConnect(t){this.gh.connect(),new Mm(this.yd,this.a);const e=new ResizeObserver(Rs(this.qa.bind(this)));e.observe(t);const i=new MutationObserver(this.Hc.bind(this));i.observe(t,{attributes:!0,childList:!0}),this.qa(),this.Hc(),Y(()=>{e.disconnect(),i.disconnect()})}load(t){window.cancelAnimationFrame(this.Ve),this.Ve=requestAnimationFrame(()=>this.Hk(t)),Y(()=>{window.cancelAnimationFrame(this.Ve)})}Hk(t){if(!this.scope)return;const e=this.$state.loader(),{$provider:i}=this.a;this.Y===e&&(e==null?void 0:e.target)===t&&$(i)||(this.hh(),this.Y=e,e&&(e.target=t||null),!(!e||!t)&&e.load(this.a).then(n=>{this.scope&&$(this.$state.loader)===e&&this.a.delegate.c("provider-change",n)}))}onDestroy(){this.Y=null,this.hh()}hh(){this.a.delegate.c("provider-change",null)}qa(){if(!this.el)return;const{player:t,$state:e}=this.a,i=this.el.offsetWidth,n=this.el.offsetHeight;t&&(e.mediaWidth.set(i),e.mediaHeight.set(n),t.el&&(Gt(t.el,"--media-width",i+"px"),Gt(t.el,"--media-height",n+"px")))}Hc(){const t=[],e=[],i=this.el.children;for(const n of i)if(!n.hasAttribute("data-vds")){if(n instanceof HTMLSourceElement){const r={id:n.id,src:n.src,type:n.type};for(const o of["id","src","width","height","bitrate","codec"]){const l=n.getAttribute(`data-${o}`);q(l)&&(r[o]=/id|src|codec/.test(o)?l:Number(l))}t.push(r)}else if(n instanceof HTMLTrackElement){const r={src:n.src,kind:n.track.kind,language:n.srclang,label:n.label,default:n.default,type:n.getAttribute("data-type")};e.push({id:n.id||ai.createId(r),...r})}}this.xd.set(t),this.yd.set(e),Ie()}};nr.props={loaders:[]};nr.state=new ys({loader:null});Dm([Tt],nr.prototype,"load",1);function qr(s,t){const e=String(s),i=e.length;if(i<t){const r=t-i;return`${"0".repeat(r)}${s}`}return e}function ou(s){const t=Math.trunc(s/3600),e=Math.trunc(s%3600/60),i=Math.trunc(s%60),n=Number((s-Math.trunc(s)).toPrecision(3));return{hours:t,minutes:e,seconds:i,fraction:n}}function Qi(s,{padHrs:t=null,padMins:e=null,showHrs:i=!1,showMs:n=!1}={}){const{hours:r,minutes:o,seconds:l,fraction:c}=ou(s),u=t?qr(r,2):r,h=e||bs(e)&&s>=3600?qr(o,2):o,d=qr(l,2),f=n&&c>0?`.${String(c).replace(/^0?\./,"")}`:"",p=`${h}:${d}${f}`;return r>0||i?`${u}:${p}`:p}function Fi(s){const t=[],{hours:e,minutes:i,seconds:n}=ou(s);return e>0&&t.push(`${e} hour`),i>0&&t.push(`${i} min`),(n>0||t.length===0)&&t.push(`${n} sec`),t.join(" ")}let Wa=class extends W{constructor(){super(...arguments),this.We=!1,this.Ic=-1,this.Ye=-1}onSetup(){this.a=at()}onAttach(t){t.style.display="contents"}onConnect(t){t.setAttribute("data-media-announcer",""),ht(t,"role","status"),ht(t,"aria-live","polite");const{busy:e}=this.$state;this.setAttributes({"aria-busy":()=>e()?"true":null}),this.We=!0,m(this.gc.bind(this)),m(this.Gc.bind(this)),m(this.Ik.bind(this)),m(this.Jk.bind(this)),m(this.Kk.bind(this)),m(this.Lk.bind(this)),m(this.Mk.bind(this)),Ie(),this.We=!1}gc(){const{paused:t}=this.a.$state;this.Sb(t()?"Pause":"Play")}Jk(){const{fullscreen:t}=this.a.$state;this.Sb(t()?"Enter Fullscreen":"Exit Fullscreen")}Kk(){const{pictureInPicture:t}=this.a.$state;this.Sb(t()?"Enter PiP":"Exit PiP")}Ik(){const{textTrack:t}=this.a.$state;this.Sb(t()?"Closed-Captions On":"Closed-Captions Off")}Gc(){const{muted:t,volume:e,audioGain:i}=this.a.$state;this.Sb(t()||e()===0?"Mute":`${Math.round(e()*(i()??1)*100)}% ${this.Xe("Volume")}`)}Lk(){const{seeking:t,currentTime:e}=this.a.$state,i=t();this.Ic>0?(window.clearTimeout(this.Ye),this.Ye=window.setTimeout(()=>{const n=$(e),r=Math.abs(n-this.Ic);if(r>=1){const o=n>=this.Ic,l=Fi(r);this.Sb(`${this.Xe(o?"Seek Forward":"Seek Backward")} ${l}`)}this.Ic=-1,this.Ye=-1},300)):i&&(this.Ic=$(e))}Xe(t){var i;const{translations:e}=this.$props;return((i=e==null?void 0:e())==null?void 0:i[t||""])??t}Mk(){const{label:t,busy:e}=this.$state,i=this.Xe(t());if(this.We)return;e.set(!0);const n=window.setTimeout(()=>void e.set(!1),150);return this.el&&G(this.el,"aria-label",i),q(i)&&this.dispatch("change",{detail:i}),()=>window.clearTimeout(n)}Sb(t){const{label:e}=this.$state;e.set(t)}};Wa.props={translations:null};Wa.state=new ys({label:null,busy:!1});class lu extends W{onSetup(){this.a=at(),m(this.Nk.bind(this))}onAttach(t){const{pictureInPicture:e,fullscreen:i}=this.a.$state;Gt(t,"pointer-events","none"),ht(t,"role","group"),this.setAttributes({"data-visible":this.ih.bind(this),"data-fullscreen":i,"data-pip":e}),m(()=>{this.dispatch("change",{detail:this.ih()})}),m(this.Ok.bind(this)),m(()=>{const n=i();for(const r of["top","right","bottom","left"])Gt(t,`padding-${r}`,n&&`env(safe-area-inset-${r})`)})}Ok(){if(!this.el)return;const{nativeControls:t}=this.a.$state,e=t();G(this.el,"aria-hidden",e?"true":null),Gt(this.el,"display",e?"none":null)}Nk(){const{controls:t}=this.a.player,{hideDelay:e,hideOnMouseLeave:i}=this.$props;t.defaultDelay=e()===2e3?this.a.$props.controlsDelay():e(),t.hideOnMouseLeave=i()}ih(){const{controlsVisible:t}=this.a.$state;return t()}}lu.props={hideDelay:2e3,hideOnMouseLeave:!1};class Im extends W{onAttach(t){t.style.pointerEvents||Gt(t,"pointer-events","auto")}}class cu extends me{constructor(t){super(),this.j=t,this.Ad=-1,this.Bd=-1,this.xb=null,m(this.Pk.bind(this))}onDestroy(){var t;(t=this.xb)==null||t.call(this),this.xb=null}Pk(){const t=this.j.N();if(!t){this.hide();return}const e=this.show.bind(this),i=this.hide.bind(this);this.j.zd(t,e,i)}show(t){var e,i,n;this.Ze(),window.cancelAnimationFrame(this.Bd),this.Bd=-1,(e=this.xb)==null||e.call(this),this.xb=null,this.Ad=window.setTimeout(()=>{this.Ad=-1;const r=this.j.q();r&&r.style.removeProperty("display"),$(()=>this.j.F(!0,t))},((n=(i=this.j).jh)==null?void 0:n.call(i))??0)}hide(t){this.Ze(),$(()=>this.j.F(!1,t)),this.Bd=requestAnimationFrame(()=>{var i;this.Ze(),this.Bd=-1;const e=this.j.q();if(e){const n=()=>{e.style.display="none",this.xb=null};if(m1(e)){(i=this.xb)==null||i.call(this);const o=E(e,"animationend",n,{once:!0});this.xb=o}else n()}})}Ze(){window.clearTimeout(this.Ad),this.Ad=-1}}const ii=is();let Om=0;class uu extends W{constructor(){super(),this.za=`media-tooltip-${++Om}`,this.N=H(null),this.q=H(null),new xe;const{showDelay:t}=this.$props;new cu({N:this.N,q:this.q,jh:t,zd(e,i,n){E(e,"touchstart",r=>r.preventDefault(),{passive:!1}),m(()=>{qn()&&E(e,"focus",i),E(e,"blur",n)}),E(e,"mouseenter",i),E(e,"mouseleave",n)},F:this.Qk.bind(this)})}onAttach(t){t.style.setProperty("display","contents")}onSetup(){Wt(ii,{N:this.N,q:this.q,_e:this._e.bind(this),$e:this.$e.bind(this),af:this.af.bind(this),bf:this.bf.bind(this)})}_e(t){var i;this.N.set(t);let e=t.getAttribute("data-media-tooltip");e&&((i=this.el)==null||i.setAttribute(`data-media-${e}-tooltip`,"")),G(t,"data-describedby",this.za)}$e(t){t.removeAttribute("data-describedby"),t.removeAttribute("aria-describedby"),this.N.set(null)}af(t){t.setAttribute("id",this.za),t.style.display="none",ht(t,"role","tooltip"),this.q.set(t)}bf(t){t.removeAttribute("id"),t.removeAttribute("role"),this.q.set(null)}Qk(t){const e=this.N(),i=this.q();e&&G(e,"aria-describedby",t?this.za:null);for(const n of[this.el,e,i])n&&G(n,"data-visible",t)}}uu.props={showDelay:700};class Rm extends W{constructor(){super(),new xe}onConnect(t){Y(Vi(()=>{if(!this.connectScope)return;this.yb();const e=Ct(ii);Y(()=>{const i=this.Cd();i&&e.$e(i)})}))}yb(){const t=this.Cd(),e=Ct(ii);t&&e._e(t)}Cd(){const t=this.el.firstElementChild;return(t==null?void 0:t.localName)==="button"||(t==null?void 0:t.getAttribute("role"))==="button"?t:this.el}}class hu extends W{constructor(){super(),new xe;const{placement:t}=this.$props;this.setAttributes({"data-placement":t})}onAttach(t){this.yb(t),Object.assign(t.style,{position:"absolute",top:0,left:0,width:"max-content"})}onConnect(t){this.yb(t);const e=Ct(ii);Y(()=>e.bf(t)),Y(Vi(()=>{this.connectScope&&m(this.cf.bind(this))}))}yb(t){Ct(ii).af(t)}cf(){const{placement:t,offset:e,alignOffset:i}=this.$props;return Zc(this.el,this.Rk(),t(),{offsetVarName:"media-tooltip",xOffset:i(),yOffset:e()})}Rk(){return Ct(ii).N()}}hu.props={placement:"top center",offset:0,alignOffset:0};class qt extends me{constructor(t){super(),this.j=t,new xe,t.Tb&&new V1(t.Tb)}onSetup(){const{disabled:t}=this.$props;this.setAttributes({"data-pressed":this.j.o,"aria-pressed":this.Sk.bind(this),"aria-disabled":()=>t()?"true":null})}onAttach(t){ht(t,"tabindex","0"),ht(t,"role","button"),ht(t,"type","button")}onConnect(t){ms(t,this.Tk.bind(this));for(const e of["click","touchstart"])this.listen(e,this.Uk.bind(this))}Sk(){return In(this.j.o())}Vk(t){df(this.j.o)&&this.j.o.set(e=>!e)}Tk(t){if(this.$props.disabled()||this.el.hasAttribute("data-disabled")){t.preventDefault(),t.stopImmediatePropagation();return}t.preventDefault(),(this.j.s??this.Vk).call(this,t)}Uk(t){this.$props.disabled()&&(t.preventDefault(),t.stopImmediatePropagation())}}qt.props={disabled:!1};var Vm=Object.defineProperty,_m=Object.getOwnPropertyDescriptor,Fm=(s,t,e,i)=>{for(var n=i>1?void 0:i?_m(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&Vm(t,e,n),n};let Ua=class extends W{constructor(){super(),this.kh=H(!1),new qt({o:this.kh})}get pressed(){return this.kh()}};Ua.props={disabled:!1,defaultPressed:!1};Fm([J],Ua.prototype,"pressed",1);function Bm(s){return s?"true":"false"}function Bt(s){return()=>Bm(s())}let du=class extends W{constructor(){super(),new qt({o:this.o.bind(this),s:this.s.bind(this)})}onSetup(){this.a=at();const{canAirPlay:t,isAirPlayConnected:e}=this.a.$state;this.setAttributes({"data-active":e,"data-supported":t,"data-state":this.Jc.bind(this),"aria-hidden":Bt(()=>!t())})}onAttach(t){t.setAttribute("data-media-tooltip","airplay"),vs(t,this.Kc.bind(this))}s(t){this.a.remote.requestAirPlay(t)}o(){const{remotePlaybackType:t,remotePlaybackState:e}=this.a.$state;return t()==="airplay"&&e()!=="disconnected"}Jc(){const{remotePlaybackType:t,remotePlaybackState:e}=this.a.$state;return t()==="airplay"&&e()}Kc(){const{remotePlaybackState:t}=this.a.$state;return`AirPlay ${t()}`}};du.props=qt.props;let fu=class extends W{constructor(){super(),new qt({o:this.o.bind(this),s:this.s.bind(this)})}onSetup(){this.a=at();const{canGoogleCast:t,isGoogleCastConnected:e}=this.a.$state;this.setAttributes({"data-active":e,"data-supported":t,"data-state":this.Jc.bind(this),"aria-hidden":Bt(()=>!t())})}onAttach(t){t.setAttribute("data-media-tooltip","google-cast"),vs(t,this.Kc.bind(this))}s(t){this.a.remote.requestGoogleCast(t)}o(){const{remotePlaybackType:t,remotePlaybackState:e}=this.a.$state;return t()==="google-cast"&&e()!=="disconnected"}Jc(){const{remotePlaybackType:t,remotePlaybackState:e}=this.a.$state;return t()==="google-cast"&&e()}Kc(){const{remotePlaybackState:t}=this.a.$state;return`Google Cast ${t()}`}};fu.props=qt.props;let pu=class extends W{constructor(){super(),new qt({o:this.o.bind(this),Tb:"togglePaused",s:this.s.bind(this)})}onSetup(){this.a=at();const{paused:t,ended:e}=this.a.$state;this.setAttributes({"data-paused":t,"data-ended":e})}onAttach(t){t.setAttribute("data-media-tooltip","play"),vs(t,"Play")}s(t){const e=this.a.remote;this.o()?e.pause(t):e.play(t)}o(){const{paused:t}=this.a.$state;return!t()}};pu.props=qt.props;let mu=class extends W{constructor(){super(),new qt({o:this.o.bind(this),Tb:"toggleCaptions",s:this.s.bind(this)})}onSetup(){this.a=at(),this.setAttributes({"data-active":this.o.bind(this),"data-supported":()=>!this.Ub(),"aria-hidden":Bt(this.Ub.bind(this))})}onAttach(t){t.setAttribute("data-media-tooltip","caption"),vs(t,"Captions")}s(t){this.a.remote.toggleCaptions(t)}o(){const{textTrack:t}=this.a.$state,e=t();return!!e&&It(e)}Ub(){const{hasCaptions:t}=this.a.$state;return!t()}};mu.props=qt.props;let gu=class extends W{constructor(){super(),new qt({o:this.o.bind(this),Tb:"toggleFullscreen",s:this.s.bind(this)})}onSetup(){this.a=at();const{fullscreen:t}=this.a.$state,e=this.Lc.bind(this);this.setAttributes({"data-active":t,"data-supported":e,"aria-hidden":Bt(()=>!e())})}onAttach(t){t.setAttribute("data-media-tooltip","fullscreen"),vs(t,"Fullscreen")}s(t){const e=this.a.remote,i=this.$props.target();this.o()?e.exitFullscreen(i,t):e.enterFullscreen(i,t)}o(){const{fullscreen:t}=this.a.$state;return t()}Lc(){const{canFullscreen:t}=this.a.$state;return t()}};gu.props={...qt.props,target:"prefer-media"};let bu=class extends W{constructor(){super(),new qt({o:this.o.bind(this),Tb:"toggleMuted",s:this.s.bind(this)})}onSetup(){this.a=at(),this.setAttributes({"data-muted":this.o.bind(this),"data-state":this.Jc.bind(this)})}onAttach(t){t.setAttribute("data-media-mute-button",""),t.setAttribute("data-media-tooltip","mute"),vs(t,"Mute")}s(t){const e=this.a.remote;this.o()?e.unmute(t):e.mute(t)}o(){const{muted:t,volume:e}=this.a.$state;return t()||e()===0}Jc(){const{muted:t,volume:e}=this.a.$state,i=e();if(t()||i===0)return"muted";if(i>=.5)return"high";if(i<.5)return"low"}};bu.props=qt.props;let yu=class extends W{constructor(){super(),new qt({o:this.o.bind(this),Tb:"togglePictureInPicture",s:this.s.bind(this)})}onSetup(){this.a=at();const{pictureInPicture:t}=this.a.$state,e=this.Lc.bind(this);this.setAttributes({"data-active":t,"data-supported":e,"aria-hidden":Bt(()=>!e())})}onAttach(t){t.setAttribute("data-media-tooltip","pip"),vs(t,"PiP")}s(t){const e=this.a.remote;this.o()?e.exitPictureInPicture(t):e.enterPictureInPicture(t)}o(){const{pictureInPicture:t}=this.a.$state;return t()}Lc(){const{canPictureInPicture:t}=this.a.$state;return t()}};yu.props=qt.props;let vu=class extends W{constructor(){super(),new xe}onSetup(){this.a=at();const{seeking:t}=this.a.$state,{seconds:e}=this.$props,i=this.Lc.bind(this);this.setAttributes({seconds:e,"data-seeking":t,"data-supported":i,"aria-hidden":Bt(()=>!i())})}onAttach(t){ht(t,"tabindex","0"),ht(t,"role","button"),ht(t,"type","button"),t.setAttribute("data-media-tooltip","seek"),vs(t,this.Kc.bind(this))}onConnect(t){ms(t,this.s.bind(this))}Lc(){const{canSeek:t}=this.a.$state;return t()}Kc(){const{seconds:t}=this.$props;return`Seek ${t()>0?"forward":"backward"} ${t()} seconds`}s(t){const{seconds:e,disabled:i}=this.$props;if(i())return;const{currentTime:n}=this.a.$state,r=n()+e();this.a.remote.seek(r,t)}};vu.props={disabled:!1,seconds:30};let Cu=class extends W{constructor(){super(),new xe}onSetup(){this.a=at();const{disabled:t}=this.$props,{live:e,liveEdge:i}=this.a.$state,n=()=>!e();this.setAttributes({"data-edge":i,"data-hidden":n,"aria-disabled":Bt(()=>t()||i()),"aria-hidden":Bt(n)})}onAttach(t){ht(t,"tabindex","0"),ht(t,"role","button"),ht(t,"type","button"),t.setAttribute("data-media-tooltip","live")}onConnect(t){ms(t,this.s.bind(this))}s(t){const{disabled:e}=this.$props,{liveEdge:i}=this.a.$state;e()||i()||this.a.remote.seekToLiveEdge(t)}};Cu.props={disabled:!1};const Fs=new ys({min:0,max:100,value:0,step:1,pointerValue:0,focused:!1,dragging:!1,pointing:!1,hidden:!1,get active(){return this.dragging||this.focused||this.pointing},get fillRate(){return Sl(this.min,this.max,this.value)},get fillPercent(){return this.fillRate*100},get pointerRate(){return Sl(this.min,this.max,this.pointerValue)},get pointerPercent(){return this.pointerRate*100}});function Sl(s,t,e){const i=t-s,n=e-s;return i>0?n/i:0}class wu extends me{constructor(t){super(),this.Ib=t}onConnect(t){this.Sa=new IntersectionObserver(e=>{var i,n;(n=(i=this.Ib).callback)==null||n.call(i,e,this.Sa)},this.Ib),this.Sa.observe(t),Y(this.Wk.bind(this))}Wk(){var t;(t=this.Sa)==null||t.disconnect(),this.Sa=void 0}}const za=is(),Di=is();function qm(s,t,e,i){return Fa(s,Ot(e,oa(i)),t)}function Hm(s,t,e,i){const n=Fa(0,e,1),r=t-s,o=r*n,l=o/i,c=i*Math.round(l);return s+c}const xl={Left:-1,ArrowLeft:-1,Up:1,ArrowUp:1,Right:1,ArrowRight:1,Down:-1,ArrowDown:-1};class jm extends me{constructor(t,e){super(),this.j=t,this.a=e,this.p=null,this.db=null,this.Vb=null,this.dl=_s(i=>{this.eb(this.Dd(i),i)},20,{leading:!0})}onSetup(){ne(Di)&&(this.Sa=Ct(Di))}onConnect(){m(this.Xk.bind(this)),m(this.Yk.bind(this)),this.j.lh&&m(this.Zk.bind(this))}Zk(){var e;const{pointer:t}=this.a.$state;if(t()!=="coarse"||!this.j.lh()){this.p=null;return}this.p=(e=this.a.player.el)==null?void 0:e.querySelector("media-provider,[data-media-provider]"),this.p&&(E(this.p,"touchstart",this._k.bind(this),{passive:!0}),E(this.p,"touchmove",this.$k.bind(this),{passive:!1}))}_k(t){this.db=t.touches[0]}$k(t){if(bs(this.db)||Ba(t))return;const e=t.touches[0],i=e.clientX-this.db.clientX,n=e.clientY-this.db.clientY,r=this.$state.dragging();!r&&Math.abs(n)>5||r||(t.preventDefault(),Math.abs(i)>20&&(this.db=e,this.Vb=this.$state.value(),this.df(this.Vb,t)))}Xk(){const{hidden:t}=this.$props;this.listen("focus",this.Fc.bind(this)),this.listen("keydown",this.jc.bind(this)),this.listen("keyup",this.ic.bind(this)),!(t()||this.j.z())&&(this.listen("pointerenter",this.Pe.bind(this)),this.listen("pointermove",this.al.bind(this)),this.listen("pointerleave",this.Qe.bind(this)),this.listen("pointerdown",this.bl.bind(this)))}Yk(){this.j.z()||!this.$state.dragging()||(E(document,"pointerup",this.cl.bind(this)),E(document,"pointermove",this.dl.bind(this)),E(document,"touchmove",this.fl.bind(this),{passive:!1}))}Fc(){this.eb(this.$state.value())}ef(t,e){var u,h,d,f;const{value:i,min:n,max:r,dragging:o}=this.$state,l=Math.max(n(),Math.min(t,r()));i.set(l);const c=this.createEvent("value-change",{detail:l,trigger:e});if(this.dispatch(c),(h=(u=this.j).l)==null||h.call(u,c),o()){const p=this.createEvent("drag-value-change",{detail:l,trigger:e});this.dispatch(p),(f=(d=this.j).T)==null||f.call(d,p)}}eb(t,e){const{pointerValue:i,dragging:n}=this.$state;i.set(t),this.dispatch("pointer-value-change",{detail:t,trigger:e}),n()&&this.ef(t,e)}Dd(t){let e,i=this.el.getBoundingClientRect(),{min:n,max:r}=this.$state;if(this.$props.orientation()==="vertical"){const{bottom:o,height:l}=i;e=(o-t.clientY)/l}else if(this.db&&Zt(this.Vb)){const{width:o}=this.p.getBoundingClientRect(),l=(t.clientX-this.db.clientX)/o,c=r()-n(),u=c*Math.abs(l);e=(l<0?this.Vb-u:this.Vb+u)/c}else{const{left:o,width:l}=i;e=(t.clientX-o)/l}return Math.max(n(),Math.min(r(),this.j.Ea(Hm(n(),r(),e,this.j.ra()))))}Pe(t){this.$state.pointing.set(!0)}al(t){const{dragging:e}=this.$state;e()||this.eb(this.Dd(t),t)}Qe(t){this.$state.pointing.set(!1)}bl(t){if(t.button!==0)return;const e=this.Dd(t);this.df(e,t),this.eb(e,t)}df(t,e){var r,o,l,c;const{dragging:i}=this.$state;if(i())return;i.set(!0),this.a.remote.pauseControls(e);const n=this.createEvent("drag-start",{detail:t,trigger:e});this.dispatch(n),(o=(r=this.j).ff)==null||o.call(r,n),(c=(l=this.Sa)==null?void 0:l.onDragStart)==null||c.call(l)}mh(t,e){var r,o,l,c;const{dragging:i}=this.$state;if(!i())return;i.set(!1),this.a.remote.resumeControls(e);const n=this.createEvent("drag-end",{detail:t,trigger:e});this.dispatch(n),(o=(r=this.j).Ed)==null||o.call(r,n),this.db=null,this.Vb=null,(c=(l=this.Sa)==null?void 0:l.onDragEnd)==null||c.call(l)}jc(t){const{key:e}=t,{min:i,max:n}=this.$state;let r;if(e==="Home"||e==="PageUp"?r=i():e==="End"||e==="PageDown"?r=n():!t.metaKey&&/^[0-9]$/.test(e)&&(r=(n()-i())/10*Number(e)),!kt(r)){this.eb(r,t),this.ef(r,t);return}const o=this.nh(t);if(kt(o))return;const l=e===this.gf;!this.$state.dragging()&&l&&this.df(o,t),this.eb(o,t),l||this.ef(o,t),this.gf=e}ic(t){this.gf="";const{dragging:e,value:i}=this.$state;if(!e())return;const n=this.nh(t)??i();this.eb(n),this.mh(n,t)}nh(t){var b,y;const{key:e,shiftKey:i}=t;if(!Object.keys(xl).includes(e))return;t.preventDefault(),t.stopPropagation();const{shiftKeyMultiplier:r}=this.$props,{value:o,min:l,max:c}=this.$state,u=this.j.ra(),h=this.j.fb(),d=i?h*r():h,f=Number(xl[e]),p=d*f,g=((((y=(b=this.j).Z)==null?void 0:y.call(b))??o())+p)/u;return Math.max(l(),Math.min(c(),Number((u*g).toFixed(3))))}cl(t){if(t.button!==0)return;const e=this.Dd(t);this.eb(e,t),this.mh(e,t)}fl(t){t.preventDefault()}}const ci=is(()=>({}));class re extends me{constructor(t){super(),this.j=t,this.Mc=H(!0),this.Nc=H(!0),this.kl=Rs((e,i)=>{var n,r;(n=this.el)==null||n.style.setProperty("--slider-fill",e+"%"),(r=this.el)==null||r.style.setProperty("--slider-pointer",i+"%")})}onSetup(){this.a=at();const t=new xe;t.attach(this),this.$state.focused=t.focused.bind(t),ne(ci)||Wt(ci,{default:"value"}),Wt(za,{cb:this.$props.orientation,Fd:this.j.z,oh:H(null)}),m(this.O.bind(this)),m(this.gl.bind(this)),m(this.Oc.bind(this)),this.il(),new jm(this.j,this.a).attach(this),new wu({callback:this.hf.bind(this)}).attach(this)}onAttach(t){ht(t,"role","slider"),ht(t,"tabindex","0"),ht(t,"autocomplete","off"),P?this.ph():m(this.ph.bind(this))}onConnect(t){Y(Uc(t,this.Mc.set)),m(this.Fa.bind(this))}hf(t){this.Nc.set(t[0].isIntersecting)}Fa(){const{hidden:t}=this.$props;this.$state.hidden.set(t()||!this.Mc()||!this.Nc.bind(this))}O(){const{dragging:t,value:e,min:i,max:n}=this.$state;$(t)||e.set(qm(i(),n(),e(),this.j.ra()))}gl(){this.$state.step.set(this.j.ra())}Oc(){if(!this.j.z())return;const{dragging:t,pointing:e}=this.$state;t.set(!1),e.set(!1)}jl(){return In(this.j.z())}il(){const{orientation:t}=this.$props,{dragging:e,active:i,pointing:n}=this.$state;this.setAttributes({"data-dragging":e,"data-pointing":n,"data-active":i,"aria-disabled":this.jl.bind(this),"aria-valuemin":this.j.Um??this.$state.min,"aria-valuemax":this.j.jf??this.$state.max,"aria-valuenow":this.j.P,"aria-valuetext":this.j.Q,"aria-orientation":t})}ph(){const{fillPercent:t,pointerPercent:e}=this.$state;this.kl(Ot(t(),3),Ot(e(),3))}}re.props={hidden:!1,disabled:!1,step:1,keyStep:1,orientation:"horizontal",shiftKeyMultiplier:5};class Bs extends W{constructor(){super(),new re({ra:this.$props.step,fb:this.$props.keyStep,Ea:Math.round,z:this.$props.disabled,P:this.P.bind(this),Q:this.Q.bind(this)})}onSetup(){m(this.O.bind(this)),m(this.Pc.bind(this))}P(){const{value:t}=this.$state;return Math.round(t())}Q(){const{value:t,max:e}=this.$state;return Ot(t()/e()*100,2)+"%"}O(){const{value:t}=this.$props;this.$state.value.set(t())}Pc(){const{min:t,max:e}=this.$props;this.$state.min.set(t()),this.$state.max.set(e())}}Bs.props={...re.props,min:0,max:100,value:0};Bs.state=Fs;const Xe=new Map,wn=new Map;class Za{constructor(t,e,i){this.$src=t,this.$crossOrigin=e,this.a=i,this.$images=H([]),m(this.ll.bind(this))}static create(t,e){const i=at();return new Za(t,e,i)}ll(){var n;const{canLoad:t}=this.a.$state;if(!t())return;const e=this.$src(),i=new AbortController;if(e){if(q(e)&&Xe.has(e)){const r=Xe.get(e);if(Xe.delete(e),Xe.set(e,r),Xe.size>30){const o=Xe.keys().next().value;Xe.delete(o)}this.$images.set(Xe.get(e))}else if(q(e)){const r=this.$crossOrigin(),o=e+"::"+r;if(!wn.has(o)){const l=new Promise(async(c,u)=>{try{const h=await fetch(e,{signal:i.signal,credentials:Li(r)});if(h.headers.get("content-type")==="application/json"){const f=await h.json();if(xt(f))if(f[0]&&"text"in f[0])c(this.qh(f));else{for(let p=0;p<f.length;p++){const g=f[p];Ye(pf(g),!1),Ye("url"in g&&q(g.url),!1),Ye("startTime"in g&&Zt(g.startTime),!1)}c(f)}else c(this.rh(f));return}ie(()=>import("./prod-Dyxyokbu.js").then(f=>f.d),__vite__mapDeps([3,1,2])).then(async({parseResponse:f})=>{try{const{cues:p}=await f(h);c(this.qh(p))}catch(p){u(p)}})}catch(h){u(h)}}).then(c=>(i.signal.aborted||Xe.set(o,c),c)).catch(c=>{i.signal.aborted||this.R(e,c)}).finally(()=>{q(o)&&wn.delete(o)});wn.set(o,l)}(n=wn.get(o))==null||n.then(l=>{i.signal.aborted||this.$images.set(l||[])})}else if(xt(e))try{this.$images.set(this.ml(e))}catch(r){this.R(e,r)}else try{this.$images.set(this.rh(e))}catch(r){this.R(e,r)}return()=>{i.abort(),this.$images.set([])}}}ml(t){const e=this.sh();return t.map((i,n)=>(Ye(i.url&&q(i.url),!1),Ye("startTime"in i&&Zt(i.startTime),!1),{...i,url:q(i.url)?this.th(i.url,e):i.url}))}rh(t){var o;Ye(q(t.url),!1),Ye(xt(t.tiles)&&((o=t.tiles)==null?void 0:o.length),!1);const e=new URL(t.url),i=[],n="tile_width"in t?t.tile_width:t.tileWidth,r="tile_height"in t?t.tile_height:t.tileHeight;for(const l of t.tiles)i.push({url:e,startTime:"start"in l?l.start:l.startTime,width:n,height:r,coords:{x:l.x,y:l.y}});return i}qh(t){for(let n=0;n<t.length;n++){const r=t[n];Ye("startTime"in r&&Zt(r.startTime),!1),Ye("text"in r&&q(r.text),!1)}const e=[],i=this.sh();for(const n of t){const[r,o]=n.text.split("#"),l=this.nl(o);e.push({url:this.th(r,i),startTime:n.startTime,endTime:n.endTime,width:l==null?void 0:l.w,height:l==null?void 0:l.h,coords:l&&Zt(l.x)&&Zt(l.y)?{x:l.x,y:l.y}:void 0})}return e}sh(){let t=$(this.$src);return!q(t)||!/^https?:/.test(t)?location.href:t}th(t,e){return/^https?:/.test(t)?new URL(t):new URL(t,e)}nl(t){if(!t)return{};const[e,i]=t.split("="),n=i==null?void 0:i.split(","),r={};if(!e||!n)return null;for(let o=0;o<e.length;o++){const l=+n[o];isNaN(l)||(r[e[o]]=l)}return r}R(t,e){}}let rr=class extends W{constructor(){super(...arguments),this.kf=[]}onSetup(){this.a=at(),this.Y=Za.create(this.$props.src,this.$state.crossOrigin),this.Da(),this.setAttributes({"data-loading":this.Qc.bind(this),"data-error":this.gb.bind(this),"data-hidden":this.$state.hidden,"aria-hidden":Bt(this.$state.hidden)})}onConnect(t){m(this.lf.bind(this)),m(this.Fa.bind(this)),m(this.Da.bind(this)),m(this.Na.bind(this)),m(this.ol.bind(this)),m(this.uh.bind(this))}lf(){const t=this.$state.img();t&&(E(t,"load",this.ub.bind(this)),E(t,"error",this.R.bind(this)))}Da(){const{crossOrigin:t}=this.$props,{crossOrigin:e}=this.$state,{crossOrigin:i}=this.a.$state,n=t()!==null?t():i();e.set(n===!0?"anonymous":n)}Na(){const{src:t,loading:e,error:i}=this.$state;return t()&&(e.set(!0),i.set(null)),()=>{this.pl(),e.set(!1),i.set(null)}}ub(){const{loading:t,error:e}=this.$state;this.uh(),t.set(!1),e.set(null)}R(t){const{loading:e,error:i}=this.$state;e.set(!1),i.set(t)}Qc(){const{loading:t,hidden:e}=this.$state;return!e()&&t()}gb(){const{error:t}=this.$state;return!bs(t())}Fa(){const{hidden:t}=this.$state,{duration:e}=this.a.$state,i=this.Y.$images();t.set(this.gb()||!Number.isFinite(e())||i.length===0)}vh(){return this.$props.time()}ol(){let t=this.Y.$images();if(!t.length)return;let e=this.vh(),{src:i,activeThumbnail:n}=this.$state,r=-1,o=null;for(let l=t.length-1;l>=0;l--){const c=t[l];if(e>=c.startTime&&(!c.endTime||e<c.endTime)){r=l;break}}t[r]&&(o=t[r]),n.set(o),i.set((o==null?void 0:o.url.href)||"")}uh(){if(!this.scope||this.$state.hidden())return;const t=this.el,e=this.$state.img(),i=this.$state.activeThumbnail();if(!e||!i||!t)return;let n=i.width??e.naturalWidth,r=(i==null?void 0:i.height)??e.naturalHeight,{maxWidth:o,maxHeight:l,minWidth:c,minHeight:u,width:h,height:d}=getComputedStyle(this.el);c==="100%"&&(c=parseFloat(h)+""),u==="100%"&&(u=parseFloat(d)+"");let f=Math.max(parseInt(c)/n,parseInt(u)/r),p=Math.min(Math.max(parseInt(c),parseInt(o))/n,Math.max(parseInt(u),parseInt(l))/r),g=!isNaN(p)&&p<1?p:f>1?f:1;this.Wb(t,"--thumbnail-width",`${n*g}px`),this.Wb(t,"--thumbnail-height",`${r*g}px`),this.Wb(e,"width",`${e.naturalWidth*g}px`),this.Wb(e,"height",`${e.naturalHeight*g}px`),this.Wb(e,"transform",i.coords?`translate(-${i.coords.x*g}px, -${i.coords.y*g}px)`:""),this.Wb(e,"max-width","none")}Wb(t,e,i){t.style.setProperty(e,i),this.kf.push(()=>t.style.removeProperty(e))}pl(){for(const t of this.kf)t();this.kf=[]}};rr.props={src:null,time:0,crossOrigin:null};rr.state=new ys({src:"",img:null,thumbnails:[],activeThumbnail:null,crossOrigin:null,loading:!1,error:null,hidden:!1});class Gm extends rr{onAttach(t){this.ja=Gi(Bs.state)}vh(){const{duration:t,clipStartTime:e}=this.a.$state;return e()+this.ja.pointerRate()*t()}}var Wm=Object.defineProperty,Um=Object.getOwnPropertyDescriptor,zm=(s,t,e,i)=>{for(var n=i>1?void 0:i?Um(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&Wm(t,e,n),n};class ar extends W{get video(){return this.$state.video()}onSetup(){this.a=at(),this.ja=Gi(Bs.state),this.Da(),this.setAttributes({"data-loading":this.Qc.bind(this),"data-hidden":this.$state.hidden,"data-error":this.gb.bind(this),"aria-hidden":Bt(this.$state.hidden)})}onAttach(t){m(this.ql.bind(this)),m(this.Nb.bind(this)),m(this.Da.bind(this)),m(this.Fa.bind(this)),m(this.rl.bind(this)),m(this.sl.bind(this))}ql(){const t=this.$state.video();t&&(t.readyState>=2&&this.fd(),E(t,"canplay",this.fd.bind(this)),E(t,"error",this.R.bind(this)))}Nb(){const{src:t}=this.$state,{canLoad:e}=this.a.$state;t.set(e()?this.$props.src():null)}Da(){const{crossOrigin:t}=this.$props,{crossOrigin:e}=this.$state,{crossOrigin:i}=this.a.$state,n=t()!==null?t():i();e.set(n===!0?"anonymous":n)}Qc(){const{canPlay:t,hidden:e}=this.$state;return!t()&&!e()}gb(){const{error:t}=this.$state;return!bs(t)}Fa(){const{src:t,hidden:e}=this.$state,{canLoad:i,duration:n}=this.a.$state;e.set(i()&&(!t()||this.gb()||!Number.isFinite(n())))}rl(){const{src:t,canPlay:e,error:i}=this.$state;t(),e.set(!1),i.set(null)}fd(t){const{canPlay:e,error:i}=this.$state;e.set(!0),i.set(null),this.dispatch("can-play",{trigger:t})}R(t){const{canPlay:e,error:i}=this.$state;e.set(!1),i.set(t),this.dispatch("error",{trigger:t})}sl(){const{video:t,canPlay:e}=this.$state,{duration:i}=this.a.$state,{pointerRate:n}=this.ja,r=t();e()&&r&&Number.isFinite(i())&&Number.isFinite(n())&&(r.currentTime=n()*i())}}ar.props={src:null,crossOrigin:null};ar.state=new ys({video:null,src:null,crossOrigin:null,canPlay:!1,error:null,hidden:!1});zm([J],ar.prototype,"video",1);var Zm=Object.defineProperty,Km=Object.getOwnPropertyDescriptor,Qm=(s,t,e,i)=>{for(var n=i>1?void 0:i?Km(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&Zm(t,e,n),n};class Ka extends W{onSetup(){this.ja=Gi(Bs.state),this.Rc=Ct(ci),this.tl=es(this.getValueText.bind(this))}getValueText(){var g,b;const{type:t,format:e,decimalPlaces:i,padHours:n,padMinutes:r,showHours:o,showMs:l}=this.$props,{value:c,pointerValue:u,min:h,max:d}=this.ja,f=e()??this.Rc.default,p=t()==="current"?c():u();if(f==="percent"){const y=d()-h(),w=p/y*100;return(this.Rc.percent??Ot)(w,i())+"%"}else return f==="time"?(this.Rc.time??Qi)(p,{padHrs:n(),padMins:r(),showHrs:o(),showMs:l()}):(((b=(g=this.Rc).value)==null?void 0:b.call(g,p))??p.toFixed(2))+""}}Ka.props={type:"pointer",format:null,showHours:!1,showMs:!1,padHours:null,padMinutes:null,decimalPlaces:2};Qm([Tt],Ka.prototype,"getValueText",1);class Eu extends W{constructor(){super(...arguments),this.wh=Rs(()=>{const{Fd:t,cb:e}=this.ja;if(t())return;const i=this.el,{offset:n,noClamp:r}=this.$props;i&&Ym(i,{clamp:!r(),offset:n(),orientation:e()})})}onSetup(){this.ja=Ct(za);const{active:t}=Gi(Bs.state);this.setAttributes({"data-visible":t})}onAttach(t){Object.assign(t.style,{position:"absolute",top:0,left:0,width:"max-content"})}onConnect(t){const{oh:e}=this.ja;e.set(t),Y(()=>e.set(null)),m(this.wh.bind(this));const i=new ResizeObserver(this.wh.bind(this));i.observe(t),Y(()=>i.disconnect())}}Eu.props={offset:0,noClamp:!1};function Ym(s,{clamp:t,offset:e,orientation:i}){const n=getComputedStyle(s),r=parseFloat(n.width),o=parseFloat(n.height),l={top:null,right:null,bottom:null,left:null};if(l[i==="horizontal"?"bottom":"left"]=`calc(100% + var(--media-slider-preview-offset, ${e}px))`,i==="horizontal"){const c=r/2;if(!t)l.left=`calc(var(--slider-pointer) - ${c}px)`;else{const u=`max(0px, calc(var(--slider-pointer) - ${c}px))`,h=`calc(100% - ${r}px)`;l.left=`min(${u}, ${h})`}}else{const c=o/2;if(!t)l.bottom=`calc(var(--slider-pointer) - ${c}px)`;else{const u=`max(${c}px, calc(var(--slider-pointer) - ${c}px))`,h=`calc(100% - ${o}px)`;l.bottom=`min(${u}, ${h})`}}Object.assign(s.style,l)}class Qa extends W{constructor(){super(...arguments),this.xh=_s(this.Oa.bind(this),25)}onSetup(){this.a=at();const{audioGain:t}=this.a.$state;Wt(ci,{default:"percent",value(e){return(e*(t()??1)).toFixed(2)},percent(e){return Math.round(e*(t()??1))}}),new re({ra:this.$props.step,fb:this.$props.keyStep,Ea:Math.round,z:this.z.bind(this),jf:this.jf.bind(this),P:this.P.bind(this),Q:this.Q.bind(this),T:this.T.bind(this),l:this.l.bind(this)}).attach(this),m(this.Gc.bind(this))}onAttach(t){t.setAttribute("data-media-volume-slider",""),ht(t,"aria-label","Volume");const{canSetVolume:e}=this.a.$state;this.setAttributes({"data-supported":e,"aria-hidden":Bt(()=>!e())})}P(){const{value:t}=this.$state,{audioGain:e}=this.a.$state;return Math.round(t()*(e()??1))}Q(){const{value:t,max:e}=this.$state,{audioGain:i}=this.a.$state;return Ot(t()/e()*(i()??1)*100,2)+"%"}jf(){const{audioGain:t}=this.a.$state;return this.$state.max()*(t()??1)}z(){const{disabled:t}=this.$props,{canSetVolume:e}=this.a.$state;return t()||!e()}Gc(){const{muted:t,volume:e}=this.a.$state,i=t()?0:e()*100;this.$state.value.set(i),this.dispatch("value-change",{detail:i})}Oa(t){if(!t.trigger)return;const e=Ot(t.detail/100,3);this.a.remote.changeVolume(e,t)}l(t){this.xh(t)}T(t){this.xh(t)}}Qa.props={...re.props,keyStep:5,shiftKeyMultiplier:2};Qa.state=Fs;class Ya extends W{onSetup(){this.a=at(),Wt(ci,{default:"percent",percent:(t,e)=>Ot(this.$state.value(),e)+"%"}),new re({ra:this.$props.step,fb:this.$props.keyStep,Ea:Math.round,z:this.z.bind(this),P:this.P.bind(this),Q:this.Q.bind(this),T:this.T.bind(this),l:this.l.bind(this)}).attach(this),m(this.Pc.bind(this)),m(this.ul.bind(this))}onAttach(t){t.setAttribute("data-media-audio-gain-slider",""),ht(t,"aria-label","Audio Boost");const{canSetAudioGain:e}=this.a.$state;this.setAttributes({"data-supported":e,"aria-hidden":Bt(()=>!e())})}P(){const{value:t}=this.$state;return Math.round(t())}Q(){const{value:t}=this.$state;return t()+"%"}Pc(){const{min:t,max:e}=this.$props;this.$state.min.set(t()),this.$state.max.set(e())}ul(){const{audioGain:t}=this.a.$state,e=((t()??1)-1)*100;this.$state.value.set(e),this.dispatch("value-change",{detail:e})}z(){const{disabled:t}=this.$props,{canSetAudioGain:e}=this.a.$state;return t()||!e()}yh(t){if(!t.trigger)return;const e=Ot(1+t.detail/100,2);this.a.remote.changeAudioGain(e,t)}l(t){this.yh(t)}T(t){this.yh(t)}}Ya.props={...re.props,step:25,keyStep:25,shiftKeyMultiplier:2,min:0,max:300};Ya.state=Fs;class Xa extends W{constructor(){super(...arguments),this.zh=_s(this.vl.bind(this),25)}onSetup(){this.a=at(),new re({ra:this.$props.step,fb:this.$props.keyStep,Ea:this.Ea,z:this.z.bind(this),P:this.P.bind(this),Q:this.Q.bind(this),T:this.T.bind(this),l:this.l.bind(this)}).attach(this),m(this.Pc.bind(this)),m(this.Re.bind(this))}onAttach(t){t.setAttribute("data-media-speed-slider",""),ht(t,"aria-label","Speed");const{canSetPlaybackRate:e}=this.a.$state;this.setAttributes({"data-supported":e,"aria-hidden":Bt(()=>!e())})}P(){const{value:t}=this.$state;return t()}Q(){const{value:t}=this.$state;return t()+"x"}Pc(){const{min:t,max:e}=this.$props;this.$state.min.set(t()),this.$state.max.set(e())}Re(){const{playbackRate:t}=this.a.$state,e=t();this.$state.value.set(e),this.dispatch("value-change",{detail:e})}Ea(t){return Ot(t,2)}z(){const{disabled:t}=this.$props,{canSetPlaybackRate:e}=this.a.$state;return t()||!e()}vl(t){if(!t.trigger)return;const e=t.detail;this.a.remote.changePlaybackRate(e,t)}l(t){this.zh(t)}T(t){this.zh(t)}}Xa.props={...re.props,step:.25,keyStep:.25,shiftKeyMultiplier:2,min:0,max:2};Xa.state=Fs;class Ja extends W{constructor(){super(...arguments),this.Sc=es(()=>{const{qualities:t}=this.a.$state;return Ha(t())}),this.Ah=_s(this._a.bind(this),25)}onSetup(){this.a=at(),new re({ra:this.$props.step,fb:this.$props.keyStep,Ea:Math.round,z:this.z.bind(this),P:this.P.bind(this),Q:this.Q.bind(this),T:this.T.bind(this),l:this.l.bind(this)}).attach(this),m(this.wl.bind(this)),m(this.xl.bind(this))}onAttach(t){t.setAttribute("data-media-quality-slider",""),ht(t,"aria-label","Video Quality");const{qualities:e,canSetQuality:i}=this.a.$state,n=es(()=>i()&&e().length>0);this.setAttributes({"data-supported":n,"aria-hidden":Bt(()=>!n())})}P(){const{value:t}=this.$state;return t()}Q(){const{quality:t}=this.a.$state;if(!t())return"";const{height:e,bitrate:i}=t(),n=i&&i>0?`${(i/1e6).toFixed(2)} Mbps`:null;return e?`${e}p${n?` (${n})`:""}`:"Auto"}wl(){const t=this.Sc();this.$state.max.set(Math.max(0,t.length-1))}xl(){let{quality:t}=this.a.$state,e=this.Sc(),i=Math.max(0,e.indexOf(t()));this.$state.value.set(i),this.dispatch("value-change",{detail:i})}z(){const{disabled:t}=this.$props,{canSetQuality:e,qualities:i}=this.a.$state;return t()||i().length<=1||!e()}_a(t){if(!t.trigger)return;const{qualities:e}=this.a,i=$(this.Sc)[t.detail];this.a.remote.changeQuality(e.indexOf(i),t)}l(t){this.Ah(t)}T(t){this.Ah(t)}}Ja.props={...re.props,step:1,keyStep:1,shiftKeyMultiplier:1};Ja.state=Fs;class or extends W{constructor(){super(),this.Bh=H(null),this.nf=!1;const{noSwipeGesture:t}=this.$props;new re({lh:()=>!t(),Z:this.Z.bind(this),ra:this.ra.bind(this),fb:this.fb.bind(this),Ea:this.Ea,z:this.z.bind(this),P:this.P.bind(this),Q:this.Q.bind(this),ff:this.ff.bind(this),T:this.T.bind(this),Ed:this.Ed.bind(this),l:this.l.bind(this)})}onSetup(){this.a=at(),Wt(ci,{default:"time",value:this.yl.bind(this),time:this.zl.bind(this)}),this.setAttributes({"data-chapters":this.Al.bind(this)}),this.setStyles({"--slider-progress":this.Bl.bind(this)}),m(this.Rb.bind(this)),m(this.Cl.bind(this))}onAttach(t){t.setAttribute("data-media-time-slider",""),ht(t,"aria-label","Seek")}onConnect(t){m(this.Dl.bind(this)),er(this.a.textTracks,"chapters",this.Bh.set)}Bl(){const{bufferedEnd:t,duration:e}=this.a.$state;return Ot(Math.min(t()/Math.max(e(),1),1)*100,3)+"%"}Al(){var e;const{duration:t}=this.a.$state;return((e=this.Bh())==null?void 0:e.cues.length)&&Number.isFinite(t())&&t()>0}Cl(){this.mf=_s(this.Ka.bind(this),this.$props.seekingRequestThrottle())}Rb(){if(this.$state.hidden())return;const{value:t,dragging:e}=this.$state,i=this.Z();$(e)||(t.set(i),this.dispatch("value-change",{detail:i}))}Dl(){const t=this.a.player.el,{oh:e}=Ct(za);t&&e()&&G(t,"data-preview",this.$state.active())}Ka(t,e){this.a.remote.seeking(t,e)}El(t,e,i){this.mf.cancel();const{live:n}=this.a.$state;if(n()&&e>=99){this.a.remote.seekToLiveEdge(i);return}this.a.remote.seek(t,i)}ff(t){const{pauseWhileDragging:e}=this.$props;if(e()){const{paused:i}=this.a.$state;this.nf=!i(),this.a.remote.pause(t)}}T(t){this.mf(this.Xb(t.detail),t)}Ed(t){const{seeking:e}=this.a.$state;$(e)||this.Ka(this.Xb(t.detail),t);const i=t.detail;this.El(this.Xb(i),i,t);const{pauseWhileDragging:n}=this.$props;n()&&this.nf&&(this.a.remote.play(t),this.nf=!1)}l(t){const{dragging:e}=this.$state;e()||!t.trigger||this.Ed(t)}Z(){const{currentTime:t}=this.a.$state;return this.Fl(t())}ra(){const t=this.$props.step()/this.a.$state.duration()*100;return Number.isFinite(t)?t:1}fb(){const t=this.$props.keyStep()/this.a.$state.duration()*100;return Number.isFinite(t)?t:1}Ea(t){return Ot(t,3)}z(){const{disabled:t}=this.$props,{canSeek:e}=this.a.$state;return t()||!e()}P(){const{value:t}=this.$state;return Math.round(t())}Q(){const t=this.Xb(this.$state.value()),{duration:e}=this.a.$state;return Number.isFinite(t)?`${Fi(t)} out of ${Fi(e())}`:"live"}Xb(t){const{duration:e}=this.a.$state;return Ot(t/100*e(),5)}Fl(t){const{liveEdge:e,duration:i}=this.a.$state,n=Math.max(0,Math.min(1,e()?1:Math.min(t,i())/i()));return Number.isNaN(n)?0:Number.isFinite(n)?n*100:100}yl(t){const e=this.Xb(t),{live:i,duration:n}=this.a.$state;return Number.isFinite(e)?(i()?e-n():e).toFixed(0):"LIVE"}zl(t,e){const i=this.Xb(t),{live:n,duration:r}=this.a.$state,o=n()?i-r():i;return Number.isFinite(i)?`${o<0?"-":""}${Qi(Math.abs(o),e)}`:"LIVE"}}or.props={...re.props,step:.1,keyStep:5,shiftKeyMultiplier:2,pauseWhileDragging:!1,noSwipeGesture:!1,seekingRequestThrottle:100};or.state=Fs;var Xm=Object.defineProperty,Jm=Object.getOwnPropertyDescriptor,lr=(s,t,e,i)=>{for(var n=i>1?void 0:i?Jm(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&Xm(t,e,n),n};class yi extends W{constructor(){super(...arguments),this.zb=null,this.ka=[],this.Hd=H(null),this.la=H([]),this.Yb=H(-1),this.Id=H(-1),this.Tc=0,this.Nl=Rs(t=>{var l;let e,i=this.la(),{clipStartTime:n}=this.a.$state,r=n(),o=this.rf(i);for(let c=this.Tc;c<this.ka.length;c++)if(e=this.qf(i[c],t,r,o),(l=this.ka[c])==null||l.style.setProperty("--chapter-progress",e+"%"),e<100){this.Tc=c;break}}),this.Ol=es(this.Pl.bind(this)),this.Jd=qa(()=>{const t=$(this.Hd);!this.scope||!t||!t.cues.length||(this.la.set(this.Ql(t.cues)),this.Yb.set(0),this.Tc=0)},150,!0)}get cues(){return this.la()}get activeCue(){return this.la()[this.Yb()]||null}get activePointerCue(){return this.la()[this.Id()]||null}onSetup(){this.a=at(),this.Gd=Gi(or.state)}onAttach(t){er(this.a.textTracks,"chapters",this.Ch.bind(this)),m(this.Gl.bind(this))}onConnect(){Y(()=>this.A.bind(this))}onDestroy(){this.Ch(null)}setRefs(t){var e;if(this.ka=t,(e=this.of)==null||e.dispose(),this.ka.length===1){const i=this.ka[0];i.style.width="100%",i.style.setProperty("--chapter-fill","var(--slider-fill)"),i.style.setProperty("--chapter-progress","var(--slider-progress)")}else this.ka.length>0&&Dt(()=>this.Hl(),this.of=ri())}Ch(t){$(this.Hd)!==t&&(this.A(),this.Hd.set(t))}A(){var t;this.ka=[],this.la.set([]),this.Yb.set(-1),this.Id.set(-1),this.Tc=0,(t=this.of)==null||t.dispose()}Hl(){this.ka.length&&m(this.Il.bind(this))}Il(){const{hidden:t}=this.Gd;t()||(m(this.Jl.bind(this)),m(this.Kl.bind(this)),m(this.Ll.bind(this)),m(this.Ml.bind(this)))}Jl(){const t=this.la();if(!t.length)return;let e,{clipStartTime:i,clipEndTime:n}=this.a.$state,r=i(),o=n()||t[t.length-1].endTime,l=o-r,c=100;for(let u=0;u<t.length;u++)if(e=t[u],this.ka[u]){const h=u===t.length-1?c:Ot((e.endTime-Math.max(r,e.startTime))/l*100,3);this.ka[u].style.width=h+"%",c-=h}}Kl(){let{liveEdge:t,clipStartTime:e,duration:i}=this.a.$state,{fillPercent:n,value:r}=this.Gd,o=this.la(),l=t(),c=$(this.Yb),u=o[c],h=l?this.la.length-1:this.Dh(u&&u.startTime/i()*100<=$(r)?c:0,n());l||!u?this.pf(0,o.length,"100%"):h>c?this.pf(c,h,"100%"):h<c&&this.pf(h+1,c+1,"0%");const d=l?"100%":this.qf(o[h],n(),e(),this.rf(o))+"%";this.Eh(this.ka[h],d),this.Yb.set(h)}Ll(){let{pointing:t,pointerPercent:e}=this.Gd;if(!t()){this.Id.set(-1);return}const i=this.Dh(0,e());this.Id.set(i)}pf(t,e,i){for(let n=t;n<e;n++)this.Eh(this.ka[n],i)}Eh(t,e){t&&t.style.setProperty("--chapter-fill",e)}Dh(t,e){let i=0,n=this.la();if(e===0)return 0;if(e===100)return n.length-1;let{clipStartTime:r}=this.a.$state,o=r(),l=this.rf(n);for(let c=t;c<n.length;c++)if(i=this.qf(n[c],e,o,l),i>=0&&i<100)return c;return 0}Ml(){this.Nl(this.Ol())}Pl(){const{bufferedEnd:t,duration:e}=this.a.$state;return Ot(Math.min(t()/Math.max(e(),1),1),3)*100}rf(t){var n;const{clipEndTime:e}=this.a.$state,i=e();return i>0?i:((n=t[t.length-1])==null?void 0:n.endTime)||0}qf(t,e,i,n){if(this.la().length===0)return 0;const o=n-i,l=Math.max(0,t.startTime-i),c=Math.min(n,t.endTime)-i,u=l/o,h=u*100,d=Math.min(1,u+(c-l)/o)*100;return Math.max(0,Ot(e>=d?100:(e-h)/(d-h)*100,3))}Ql(t){let e=[],{clipStartTime:i,clipEndTime:n,duration:r}=this.a.$state,o=i(),l=n()||1/0;t=t.filter(h=>h.startTime<=l&&h.endTime>=o);const c=t[0];c&&c.startTime>o&&e.push(new window.VTTCue(o,c.startTime,""));for(let h=0;h<t.length-1;h++){const d=t[h],f=t[h+1];if(e.push(d),f){const p=f.startTime-d.endTime;p>0&&e.push(new window.VTTCue(d.endTime,d.endTime+p,""))}}const u=t[t.length-1];if(u){e.push(u);const h=r();h>=0&&h-u.endTime>1&&e.push(new window.VTTCue(u.endTime,r(),""))}return e}Gl(){const{source:t}=this.a.$state;t(),this.qc()}qc(){if(!this.scope)return;const{disabled:t}=this.$props;if(t()){this.la.set([]),this.Yb.set(0),this.Tc=0;return}const e=this.Hd();if(e){const i=this.Jd.bind(this);i(),Y(E(e,"add-cue",i)),Y(E(e,"remove-cue",i)),m(this.Rl.bind(this))}return this.zb=this.Sl(),this.zb&&m(this.Tl.bind(this)),()=>{this.zb&&(this.zb.textContent="",this.zb=null)}}Rl(){this.a.$state.duration(),this.Jd()}Tl(){const t=this.activePointerCue||this.activeCue;this.zb&&(this.zb.textContent=(t==null?void 0:t.text)||"")}Ul(){let t=this.el;for(;t&&t.getAttribute("role")!=="slider";)t=t.parentElement;return t}Sl(){const t=this.Ul();return t?t.querySelector('[data-part="chapter-title"]'):null}}yi.props={disabled:!1};lr([J],yi.prototype,"cues",1);lr([J],yi.prototype,"activeCue",1);lr([J],yi.prototype,"activePointerCue",1);lr([Tt],yi.prototype,"setRefs",1);const Nt=is(),kl=s=>typeof s=="object"&&s!=null&&s.nodeType===1,Pl=(s,t)=>(!t||s!=="hidden")&&s!=="visible"&&s!=="clip",Hr=(s,t)=>{if(s.clientHeight<s.scrollHeight||s.clientWidth<s.scrollWidth){const e=getComputedStyle(s,null);return Pl(e.overflowY,t)||Pl(e.overflowX,t)||(i=>{const n=(r=>{if(!r.ownerDocument||!r.ownerDocument.defaultView)return null;try{return r.ownerDocument.defaultView.frameElement}catch{return null}})(i);return!!n&&(n.clientHeight<i.scrollHeight||n.clientWidth<i.scrollWidth)})(s)}return!1},En=(s,t,e,i,n,r,o,l)=>r<s&&o>t||r>s&&o<t?0:r<=s&&l<=e||o>=t&&l>=e?r-s-i:o>t&&l<e||r<s&&l>e?o-t+n:0,t6=s=>{const t=s.parentElement;return t??(s.getRootNode().host||null)},e6=(s,t)=>{var e,i,n,r;if(typeof document>"u")return[];const{scrollMode:o,block:l,inline:c,boundary:u,skipOverflowHiddenElements:h}=t,d=typeof u=="function"?u:x=>x!==u;if(!kl(s))throw new TypeError("Invalid target");const f=document.scrollingElement||document.documentElement,p=[];let g=s;for(;kl(g)&&d(g);){if(g=t6(g),g===f){p.push(g);break}g!=null&&g===document.body&&Hr(g)&&!Hr(document.documentElement)||g!=null&&Hr(g,h)&&p.push(g)}const b=(i=(e=window.visualViewport)==null?void 0:e.width)!=null?i:innerWidth,y=(r=(n=window.visualViewport)==null?void 0:n.height)!=null?r:innerHeight,{scrollX:w,scrollY:S}=window,{height:N,width:j,top:V,right:K,bottom:ot,left:C}=s.getBoundingClientRect(),{top:k,right:_,bottom:St,left:gt}=(x=>{const D=window.getComputedStyle(x);return{top:parseFloat(D.scrollMarginTop)||0,right:parseFloat(D.scrollMarginRight)||0,bottom:parseFloat(D.scrollMarginBottom)||0,left:parseFloat(D.scrollMarginLeft)||0}})(s);let ct=l==="start"||l==="nearest"?V-k:l==="end"?ot+St:V+N/2-k+St,et=c==="center"?C+j/2-gt+_:c==="end"?K+_:C-gt;const T=[];for(let x=0;x<p.length;x++){const D=p[x],{height:st,width:At,top:Et,right:Rt,bottom:Vt,left:_t}=D.getBoundingClientRect();if(o==="if-needed"&&V>=0&&C>=0&&ot<=y&&K<=b&&V>=Et&&ot<=Vt&&C>=_t&&K<=Rt)return T;const $t=getComputedStyle(D),L=parseInt($t.borderLeftWidth,10),I=parseInt($t.borderTopWidth,10),U=parseInt($t.borderRightWidth,10),vt=parseInt($t.borderBottomWidth,10);let bt=0,ut=0;const Qt="offsetWidth"in D?D.offsetWidth-D.clientWidth-L-U:0,be="offsetHeight"in D?D.offsetHeight-D.clientHeight-I-vt:0,ye="offsetWidth"in D?D.offsetWidth===0?0:At/D.offsetWidth:0,qe="offsetHeight"in D?D.offsetHeight===0?0:st/D.offsetHeight:0;if(f===D)bt=l==="start"?ct:l==="end"?ct-y:l==="nearest"?En(S,S+y,y,I,vt,S+ct,S+ct+N,N):ct-y/2,ut=c==="start"?et:c==="center"?et-b/2:c==="end"?et-b:En(w,w+b,b,L,U,w+et,w+et+j,j),bt=Math.max(0,bt+S),ut=Math.max(0,ut+w);else{bt=l==="start"?ct-Et-I:l==="end"?ct-Vt+vt+be:l==="nearest"?En(Et,Vt,st,I,vt+be,ct,ct+N,N):ct-(Et+st/2)+be/2,ut=c==="start"?et-_t-L:c==="center"?et-(_t+At/2)+Qt/2:c==="end"?et-Rt+U+Qt:En(_t,Rt,At,L,U+Qt,et,et+j,j);const{scrollLeft:yt,scrollTop:ws}=D;bt=qe===0?0:Math.max(0,Math.min(ws+bt/qe,D.scrollHeight-st/qe+be)),ut=ye===0?0:Math.max(0,Math.min(yt+ut/ye,D.scrollWidth-At/ye+Qt)),ct+=ws-bt,et+=yt-ut}T.push({el:D,top:bt,left:ut})}return T};function s6(s,t){const e=e6(s,t);for(const{el:i,top:n,left:r}of e)i.scroll({top:n,left:r,behavior:t.behavior})}function i6(s,t={}){s6(s,{scrollMode:"if-needed",block:"center",inline:"center",...t})}const n6=["a[href]","[tabindex]","input","select","button"].map(s=>`${s}:not([aria-hidden='true'])`).join(","),r6=new Set(["Escape","Tab","ArrowUp","ArrowDown","Home","PageUp","End","PageDown","Enter"," "]);class a6{constructor(t){this.j=t,this.Uc=-1,this.Ta=null,this.sa=[]}get B(){return this.sa}Vl(t){E(t,"focus",this.Fc.bind(this)),this.Ta=t,Y(()=>{this.Ta=null})}zd(){this.Ta&&(this.Ia(),E(this.Ta,"keyup",this.ic.bind(this)),E(this.Ta,"keydown",this.jc.bind(this)),Y(()=>{this.Uc=-1,this.sa=[]}))}Ia(){this.Uc=0,this.sa=this.Wl()}Fh(t=this.Gh()){const e=this.sa[t];e&&requestAnimationFrame(()=>{requestAnimationFrame(()=>{i6(e,{behavior:"smooth",boundary:i=>!i.hasAttribute("data-root")})})})}Hh(t=!0){const e=this.Gh();this.Zb(e>=0?e:0,t)}Zb(t,e=!0){var i;this.Uc=t,this.sa[t]?(this.sa[t].focus({preventScroll:!0}),e&&this.Fh(t)):(i=this.Ta)==null||i.focus({preventScroll:!0})}Gh(){return this.sa.findIndex(t=>document.activeElement===t||t.getAttribute("role")==="menuitemradio"&&t.getAttribute("aria-checked")==="true")}Fc(){this.Uc>=0||(this.Ia(),this.Hh())}Ih(t){const e=t.target;if(Cf(t)&&e instanceof Element){const i=e.getAttribute("role");return!/a|input|select|button/.test(e.localName)&&!i}return r6.has(t.key)}ic(t){this.Ih(t)&&(t.stopPropagation(),t.preventDefault())}jc(t){if(this.Ih(t))switch(t.stopPropagation(),t.preventDefault(),t.key){case"Escape":this.j.Xl(t);break;case"Tab":this.Zb(this.sf(t.shiftKey?-1:1));break;case"ArrowUp":this.Zb(this.sf(-1));break;case"ArrowDown":this.Zb(this.sf(1));break;case"Home":case"PageUp":this.Zb(0);break;case"End":case"PageDown":this.Zb(this.sa.length-1);break}}sf(t){var i;let e=this.Uc;do e=(e+t+this.sa.length)%this.sa.length;while(((i=this.sa[e])==null?void 0:i.offsetParent)===null);return e}Wl(){if(!this.Ta)return[];const t=this.Ta.querySelectorAll(n6),e=[],i=n=>n.getAttribute("role")==="menu";for(const n of t)_i(n)&&n.offsetParent!==null&&zc(this.Ta,n,i)&&e.push(n);return e}}var o6=Object.defineProperty,l6=Object.getOwnPropertyDescriptor,Yi=(s,t,e,i)=>{for(var n=i>1?void 0:i?l6(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&o6(t,e,n),n};let c6=0;class qs extends W{constructor(){super(),this.U=H(!1),this.Fd=H(!1),this.N=H(null),this.q=H(null),this.Wc=new Set,this.Kd=null,this.Md=!1,this.Jh=H(!1),this.Nd=new Set,this.Af=!1,this.jm=this.km.bind(this),this.Df=!1,this.hm=this.lm.bind(this),this.im=this.mm.bind(this),this.qa=Rs(()=>{const e=$(this.q);if(!e||P)return;let i=0,n=getComputedStyle(e),r=[...e.children];for(const o of["paddingTop","paddingBottom","borderTopWidth","borderBottomWidth"])i+=parseFloat(n[o])||0;for(const o of r)if(_i(o)&&o.style.display==="contents")r.push(...o.children);else if(o.nodeType===3)i+=parseFloat(getComputedStyle(o).fontSize);else if(_i(o)){if(!Wc(o))continue;const l=getComputedStyle(o);i+=o.offsetHeight+(parseFloat(l.marginTop)||0)+(parseFloat(l.marginBottom)||0)}Gt(e,"--menu-height",i+"px")}),this.Cf=!1;const{showDelay:t}=this.$props;this.Ld=new cu({N:this.N,q:this.q,jh:t,zd:(e,i,n)=>{ms(e,o=>{this.U()?n(o):i(o)});const r=this.Yl();r&&ms(r,o=>{o.stopPropagation(),n(o)})},F:this.Zl.bind(this)})}get triggerElement(){return this.N()}get contentElement(){return this.q()}get isSubmenu(){return!!this.Vc}onSetup(){this.a=at();const t=++c6;this.tf=`media-menu-${t}`,this.uf=`media-menu-button-${t}`,this._b=new a6({Xl:this.close.bind(this)}),ne(Nt)&&(this.Vc=Ct(Nt)),this._l(),this.setAttributes({"data-open":this.U,"data-root":!this.isSubmenu,"data-submenu":this.isSubmenu,"data-disabled":this.z.bind(this)}),Wt(Nt,{$l:this.N,q:this.q,U:this.U,$b:H(""),Vm:!!this.Vc,hb:this.hb.bind(this),vf:this.vf.bind(this),wf:this.wf.bind(this),xf:this.xf.bind(this),yf:this.yf.bind(this),zf:this.zf.bind(this),am:e=>{this.Nd.add(e),Y(()=>{this.Nd.delete(e)})}})}onAttach(t){t.style.setProperty("display","contents")}onConnect(t){var e;m(this.bm.bind(this)),this.isSubmenu&&((e=this.Vc)==null||e.zf(this))}onDestroy(){this.N.set(null),this.q.set(null),this.Kd=null,this.Nd.clear()}_l(){let t=-1,e=ne(Di)?Ct(Di):null;Wt(Di,{onDragStart:()=>{var i;(i=e==null?void 0:e.onDragStart)==null||i.call(e),window.clearTimeout(t),t=-1,this.Md=!0},onDragEnd:()=>{var i;(i=e==null?void 0:e.onDragEnd)==null||i.call(e),t=window.setTimeout(()=>{this.Md=!1,t=-1},300)}})}bm(){const t=this.cm();this.isSubmenu||this.qa(),this.Kh(t),t&&(m(()=>{const{height:e}=this.a.$state,i=this.q();i&&Gt(i,"--player-height",e()+"px")}),this._b.zd(),this.listen("pointerup",this.dm.bind(this)),E(window,"pointerup",this.em.bind(this)))}vf(t){const e=t.el,i=this.isSubmenu,n=Bt(this.z.bind(this));ht(e,"tabindex",i?"-1":"0"),ht(e,"role",i?"menuitem":"button"),G(e,"id",this.uf),G(e,"aria-haspopup","menu"),G(e,"aria-expanded","false"),G(e,"data-root",!this.isSubmenu),G(e,"data-submenu",this.isSubmenu);const r=()=>{G(e,"data-open",this.U()),G(e,"aria-disabled",n())};P?r():m(r),this.N.set(e),Y(()=>{this.N.set(null)})}wf(t){var r;const e=t.el;e.style.setProperty("display","none"),G(e,"id",this.tf),ht(e,"role","menu"),ht(e,"tabindex","-1"),G(e,"data-root",!this.isSubmenu),G(e,"data-submenu",this.isSubmenu),this.q.set(e),Y(()=>this.q.set(null));const i=()=>G(e,"data-open",this.U());P?i():m(i),this._b.Vl(e),this.Kh(!1);const n=this.fm.bind(this);this.isSubmenu?(r=this.Vc)==null||r.am(n):(t.listen("transitionstart",n),t.listen("transitionend",n),t.listen("animationend",this.qa),t.listen("vds-menu-resize",this.qa))}xf(t){this.Kd=t}Kh(t){const e=$(this.q);e&&G(e,"aria-hidden",In(!t))}yf(t){this.Jh.set(t)}Zl(t,e){var r,o,l,c,u,h;if(this.Af=Yn(e),e==null||e.stopPropagation(),this.U()===t)return;if(this.z()){t&&this.Ld.hide(e);return}(r=this.el)==null||r.dispatchEvent(new Event("vds-menu-resize",{bubbles:!0,composed:!0}));const i=this.N(),n=this.q();if(i&&(G(i,"aria-controls",t&&this.tf),G(i,"aria-expanded",In(t))),n&&G(n,"aria-labelledby",t&&this.uf),this.U.set(t),this.gm(e),Ie(),this.Af){t?n==null||n.focus():i==null||i.focus();for(const d of[this.el,n])d&&d.setAttribute("data-keyboard","")}else for(const d of[this.el,n])d&&d.removeAttribute("data-keyboard");if(this.dispatch(t?"open":"close",{trigger:e}),t)!this.isSubmenu&&this.a.activeMenu!==this&&((o=this.a.activeMenu)==null||o.close(e),this.a.activeMenu=this),(c=(l=this.Kd)==null?void 0:l.Bf)==null||c.call(l,e);else{if(this.isSubmenu)for(const d of this.Wc)d.close(e);else this.a.activeMenu=null;(h=(u=this.Kd)==null?void 0:u.Wm)==null||h.call(u,e)}t&&requestAnimationFrame(this.Lh.bind(this))}Lh(){this.Cf||this.Df||(this._b.Ia(),requestAnimationFrame(()=>{this.Af?this._b.Hh():this._b.Fh()}))}cm(){return!this.z()&&this.U()}z(){return this.Fd()||this.Jh()}hb(t){this.Fd.set(t)}dm(t){const e=this.q();this.Md||e&&ml(e,t)||t.stopPropagation()}em(t){const e=this.q();this.Md||e&&ml(e,t)||this.close(t)}Yl(){var e;const t=(e=this.el)==null?void 0:e.querySelector('[data-part="close-target"]');return this.el&&t&&zc(this.el,t,i=>i.getAttribute("role")==="menu")?t:null}gm(t){this.isSubmenu||(this.U()?this.a.remote.pauseControls(t):this.a.remote.resumeControls(t))}zf(t){this.Wc.add(t),E(t,"open",this.hm),E(t,"close",this.im),Y(this.jm)}km(t){this.Wc.delete(t)}lm(t){var i;this.Df=!0;const e=this.q();this.isSubmenu&&((i=this.triggerElement)==null||i.setAttribute("aria-hidden","true"));for(const n of this.Wc)if(n!==t.target)for(const r of[n.el,n.triggerElement])r==null||r.setAttribute("aria-hidden","true");if(e){const n=t.target.el;for(const r of e.children)r.contains(n)?r.setAttribute("data-open",""):r!==n&&r.setAttribute("data-hidden","")}}mm(t){var i;this.Df=!1;const e=this.q();this.isSubmenu&&((i=this.triggerElement)==null||i.setAttribute("aria-hidden","false"));for(const n of this.Wc)for(const r of[n.el,n.triggerElement])r==null||r.setAttribute("aria-hidden","false");if(e)for(const n of e.children)n.removeAttribute("data-open"),n.removeAttribute("data-hidden")}fm(t){const e=this.q();e&&t.propertyName==="height"&&(this.Cf=t.type==="transitionstart",G(e,"data-transition",this.Cf?"height":null),this.U()&&this.Lh());for(const i of this.Nd)i(t)}open(t){$(this.U)||(this.Ld.show(t),Ie())}close(t){$(this.U)&&(this.Ld.hide(t),Ie())}}qs.props={showDelay:0};Yi([J],qs.prototype,"triggerElement",1);Yi([J],qs.prototype,"contentElement",1);Yi([J],qs.prototype,"isSubmenu",1);Yi([Tt],qs.prototype,"open",1);Yi([Tt],qs.prototype,"close",1);var u6=Object.defineProperty,h6=Object.getOwnPropertyDescriptor,d6=(s,t,e,i)=>{for(var n=i>1?void 0:i?h6(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&u6(t,e,n),n};class cr extends W{constructor(){super(),this.Mh=H(null),new xe}get expanded(){var t;return((t=this.n)==null?void 0:t.U())??!1}onSetup(){this.n=Ct(Nt)}onAttach(t){this.n.vf(this),m(this.Oc.bind(this)),ht(t,"type","button")}onConnect(t){m(this.nm.bind(this)),this.Hc();const e=new MutationObserver(this.Hc.bind(this));e.observe(t,{attributeFilter:["data-part"],childList:!0,subtree:!0}),Y(()=>e.disconnect()),ms(t,i=>{this.dispatch("select",{trigger:i})})}Oc(){this.n.yf(this.$props.disabled())}nm(){const t=this.Mh();t&&m(()=>{const e=this.n.$b();e&&(t.textContent=e)})}Hc(){var e;const t=(e=this.el)==null?void 0:e.querySelector('[data-part="hint"]');this.Mh.set(t??null)}}cr.props={disabled:!1};d6([J],cr.prototype,"expanded",1);class f6 extends cr{}class p6 extends W{constructor(){super(...arguments),this.H=null}onSetup(){this.a=at(),Wt(Ln,{yb:this.om.bind(this)})}onAttach(t){t.style.setProperty("display","contents")}onConnect(t){}onDestroy(){var t;(t=this.H)==null||t.remove(),this.H=null}om(t){this.Nh(!1),this.H=t,Vi(()=>{Vi(()=>{this.connectScope&&m(this.Oc.bind(this))})})}Oc(){const{fullscreen:t}=this.a.$state,{disabled:e}=this.$props,i=e();this.Nh(i==="fullscreen"?!t():!i)}Nh(t){var n;if(!this.H)return;let e=this.pm(this.$props.container());if(!e)return;const i=this.H.parentElement===e;G(this.H,"data-portal",t),t?i||(this.H.remove(),e.append(this.H)):i&&this.H.parentElement===e&&(this.H.remove(),(n=this.el)==null||n.append(this.H))}pm(t){return _i(t)?t:t?document.querySelector(t):document.body}}p6.props={container:null,disabled:!1};const Ln=is();class $u extends W{constructor(){super(),new xe;const{placement:t}=this.$props;this.setAttributes({"data-placement":t})}onAttach(t){if(this.n=Ct(Nt),this.n.wf(this),ne(Ln)){const e=Ct(Ln);e&&(Wt(Ln,null),e.yb(t),Y(()=>e.yb(null)))}}onConnect(t){m(this.cf.bind(this))}cf(){if(!this.el)return;const t=this.$props.placement();if(t){Object.assign(this.el.style,{position:"absolute",top:0,left:0,width:"max-content"});const{offset:e,alignOffset:i}=this.$props;return Zc(this.el,this.Cd(),t,{offsetVarName:"media-menu",xOffset:i(),yOffset:e()})}else this.el.removeAttribute("style"),this.el.style.display="none"}Cd(){return this.n.$l()}}$u.props={placement:null,offset:0,alignOffset:0};const ha=is();class Hs extends me{constructor(){super(...arguments),this.ac=new Set,this.Ua=H(""),this.e=null,this.tm=this.F.bind(this)}get qm(){return Array.from(this.ac).map(t=>t.Ua())}get value(){return this.Ua()}set value(t){this.F(t)}onSetup(){Wt(ha,{add:this.rm.bind(this),remove:this.sm.bind(this)})}onAttach(t){ne(Nt)||ht(t,"role","radiogroup"),this.setAttributes({value:this.Ua})}onDestroy(){this.ac.clear()}rm(t){this.ac.has(t)||(this.ac.add(t),t.Od=this.tm,t.Xc(t.Ua()===this.Ua()))}sm(t){t.Od=null,this.ac.delete(t)}F(t,e){var o;const i=$(this.Ua);if(!t||t===i)return;const n=this.Oh(i),r=this.Oh(t);n==null||n.Xc(!1,e),r==null||r.Xc(!0,e),this.Ua.set(t),(o=this.l)==null||o.call(this,t,e)}Oh(t){for(const e of this.ac)if(t===$(e.Ua))return e;return null}}var m6=Object.defineProperty,g6=Object.getOwnPropertyDescriptor,Tu=(s,t,e,i)=>{for(var n=i>1?void 0:i?g6(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&m6(t,e,n),n};class ur extends W{get values(){return this.e.qm}get value(){return this.e.value}set value(t){this.e.value=t}constructor(){super(),this.e=new Hs,this.e.l=this.l.bind(this)}onSetup(){P?this.O():m(this.O.bind(this))}O(){this.e.value=this.$props.value()}l(t,e){const i=this.createEvent("change",{detail:t,trigger:e});this.dispatch(i)}}ur.props={value:""};Tu([J],ur.prototype,"values",1);Tu([J],ur.prototype,"value",1);var b6=Object.defineProperty,y6=Object.getOwnPropertyDescriptor,v6=(s,t,e,i)=>{for(var n=i>1?void 0:i?y6(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&b6(t,e,n),n};class to extends W{constructor(){super(),this.Ab=H(!1),this.e={Ua:this.$props.value,Xc:this.Xc.bind(this),Od:null},new xe}get checked(){return this.Ab()}onSetup(){this.setAttributes({value:this.$props.value,"data-checked":this.Ab,"aria-checked":Bt(this.Ab)})}onAttach(t){const e=ne(Nt);ht(t,"tabindex",e?"-1":"0"),ht(t,"role",e?"menuitemradio":"radio"),m(this.O.bind(this))}onConnect(t){this.um(),ms(t,this.s.bind(this)),Y(this.Ga.bind(this))}Ga(){Dt(()=>{Ct(ha).remove(this.e)},this.connectScope)}um(){Ct(ha).add(this.e)}O(){var i,n;const{value:t}=this.$props,e=t();$(this.Ab)&&((n=(i=this.e).Od)==null||n.call(i,e))}s(t){var e,i;$(this.Ab)||(this.F(!0,t),this.vm(t),(i=(e=this.e).Od)==null||i.call(e,$(this.$props.value),t))}Xc(t,e){$(this.Ab)!==t&&this.F(t,e)}F(t,e){this.Ab.set(t),this.dispatch("change",{detail:t,trigger:e})}vm(t){this.dispatch("select",{trigger:t})}}to.props={value:""};v6([J],to.prototype,"checked",1);var C6=Object.defineProperty,w6=Object.getOwnPropertyDescriptor,eo=(s,t,e,i)=>{for(var n=i>1?void 0:i?w6(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&C6(t,e,n),n};class hr extends W{constructor(){super(),this.K=H(null),this.C=H([]),this.e=new Hs,this.e.l=this.l.bind(this)}get value(){return this.e.value}get disabled(){var t;return!((t=this.C())!=null&&t.length)}onSetup(){this.a=at(),ne(Nt)&&(this.n=Ct(Nt));const{thumbnails:t}=this.$props;this.setAttributes({"data-thumbnails":()=>!!t()})}onAttach(t){var e;(e=this.n)==null||e.xf({Bf:this.Bf.bind(this)})}getOptions(){const{clipStartTime:t,clipEndTime:e}=this.a.$state,i=t(),n=e()||1/0;return this.C().map((r,o)=>({cue:r,value:o.toString(),label:r.text,startTime:Qi(Math.max(0,r.startTime-i)),duration:Fi(Math.min(n,r.endTime)-Math.max(i,r.startTime))}))}Bf(){$(()=>this.Rb())}onConnect(t){m(this.Rb.bind(this)),m(this.ta.bind(this)),m(this.wm.bind(this)),er(this.a.textTracks,"chapters",this.K.set)}wm(){const t=this.K();if(!t)return;const e=this.Jd.bind(this,t);return e(),E(t,"add-cue",e),E(t,"remove-cue",e),()=>{this.C.set([])}}Jd(t){const{clipStartTime:e,clipEndTime:i}=this.a.$state,n=e(),r=i()||1/0;this.C.set([...t.cues].filter(o=>o.startTime<=r&&o.endTime>=n))}Rb(){var u;if(!((u=this.n)!=null&&u.U()))return;if(!this.K()){this.e.value="-1";return}const{realCurrentTime:e,clipStartTime:i,clipEndTime:n}=this.a.$state,r=i(),o=n()||1/0,l=e(),c=this.C().findIndex(h=>ra(h,l));this.e.value=c.toString(),c>=0&&Vi(()=>{if(!this.connectScope)return;const h=this.C()[c],d=this.el.querySelector("[aria-checked='true']"),f=Math.max(r,h.startTime),p=Math.min(o,h.endTime)-f,g=Math.max(0,l-f)/p*100;d&&Gt(d,"--progress",Ot(g,3)+"%")})}ta(){var t;(t=this.n)==null||t.hb(this.disabled)}l(t,e){if(this.disabled||!e)return;const i=+t,n=this.C(),{clipStartTime:r}=this.a.$state;Zt(i)&&(n!=null&&n[i])&&(this.e.value=i.toString(),this.a.remote.seek(n[i].startTime-r(),e),this.dispatch("change",{detail:n[i],trigger:e}))}}hr.props={thumbnails:null};eo([J],hr.prototype,"value",1);eo([J],hr.prototype,"disabled",1);eo([Tt],hr.prototype,"getOptions",1);var E6=Object.defineProperty,$6=Object.getOwnPropertyDescriptor,so=(s,t,e,i)=>{for(var n=i>1?void 0:i?$6(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&E6(t,e,n),n};class dr extends W{get value(){return this.e.value}get disabled(){const{audioTracks:t}=this.a.$state;return t().length<=1}constructor(){super(),this.e=new Hs,this.e.l=this.l.bind(this)}onSetup(){this.a=at(),ne(Nt)&&(this.n=Ct(Nt))}onConnect(t){m(this.O.bind(this)),m(this.ta.bind(this)),m(this.Va.bind(this))}getOptions(){const{audioTracks:t}=this.a.$state;return t().map(e=>({track:e,label:e.label,value:e.label.toLowerCase()}))}O(){this.e.value=this.Z()}Va(){var n;const{emptyLabel:t}=this.$props,{audioTrack:e}=this.a.$state,i=e();(n=this.n)==null||n.$b.set((i==null?void 0:i.label)??t())}ta(){var t;(t=this.n)==null||t.hb(this.disabled)}Z(){const{audioTrack:t}=this.a.$state,e=t();return e?e.label.toLowerCase():""}l(t,e){if(this.disabled)return;const i=this.a.audioTracks.toArray().findIndex(n=>n.label.toLowerCase()===t);if(i>=0){const n=this.a.audioTracks[i];this.a.remote.changeAudioTrack(i,e),this.dispatch("change",{detail:n,trigger:e})}}}dr.props={emptyLabel:"Default"};so([J],dr.prototype,"value",1);so([J],dr.prototype,"disabled",1);so([Tt],dr.prototype,"getOptions",1);var T6=Object.defineProperty,S6=Object.getOwnPropertyDescriptor,io=(s,t,e,i)=>{for(var n=i>1?void 0:i?S6(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&T6(t,e,n),n};const x6=[1,1.25,1.5,1.75,2,2.5,3,4];class fr extends W{get value(){return this.e.value}get disabled(){const{gains:t}=this.$props,{canSetAudioGain:e}=this.a.$state;return!e()||t().length===0}constructor(){super(),this.e=new Hs,this.e.l=this.l.bind(this)}onSetup(){this.a=at(),ne(Nt)&&(this.n=Ct(Nt))}onConnect(t){m(this.O.bind(this)),m(this.Va.bind(this)),m(this.ta.bind(this))}getOptions(){const{gains:t,normalLabel:e}=this.$props;return t().map(i=>({label:i===1||i===null?e:String(i*100)+"%",value:i.toString()}))}O(){this.e.value=this.Z()}Va(){var n;const{normalLabel:t}=this.$props,{audioGain:e}=this.a.$state,i=e();(n=this.n)==null||n.$b.set(i===1||i==null?t():String(i*100)+"%")}ta(){var t;(t=this.n)==null||t.hb(this.disabled)}Z(){var e;const{audioGain:t}=this.a.$state;return((e=t())==null?void 0:e.toString())??"1"}l(t,e){if(this.disabled)return;const i=+t;this.a.remote.changeAudioGain(i,e),this.dispatch("change",{detail:i,trigger:e})}}fr.props={normalLabel:"Disabled",gains:x6};io([J],fr.prototype,"value",1);io([J],fr.prototype,"disabled",1);io([Tt],fr.prototype,"getOptions",1);var k6=Object.defineProperty,P6=Object.getOwnPropertyDescriptor,no=(s,t,e,i)=>{for(var n=i>1?void 0:i?P6(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&k6(t,e,n),n};class pr extends W{get value(){return this.e.value}get disabled(){const{hasCaptions:t}=this.a.$state;return!t()}constructor(){super(),this.e=new Hs,this.e.l=this.l.bind(this)}onSetup(){this.a=at(),ne(Nt)&&(this.n=Ct(Nt))}onConnect(t){var e;(e=super.onConnect)==null||e.call(this,t),m(this.O.bind(this)),m(this.ta.bind(this)),m(this.Va.bind(this))}getOptions(){const{offLabel:t}=this.$props,{textTracks:e}=this.a.$state;return[{value:"off",label:t},...e().filter(It).map(i=>({track:i,label:i.label,value:this.Ef(i)}))]}O(){this.e.value=this.Z()}Va(){var n;const{offLabel:t}=this.$props,{textTrack:e}=this.a.$state,i=e();(n=this.n)==null||n.$b.set(i&&It(i)&&i.mode==="showing"?i.label:t())}ta(){var t;(t=this.n)==null||t.hb(this.disabled)}Z(){const{textTrack:t}=this.a.$state,e=t();return e&&It(e)&&e.mode==="showing"?this.Ef(e):"off"}l(t,e){if(this.disabled)return;if(t==="off"){const n=this.a.textTracks.selected;if(n){const r=this.a.textTracks.indexOf(n);this.a.remote.changeTextTrackMode(r,"disabled",e),this.dispatch("change",{detail:null,trigger:e})}return}const i=this.a.textTracks.toArray().findIndex(n=>this.Ef(n)===t);if(i>=0){const n=this.a.textTracks[i];this.a.remote.changeTextTrackMode(i,"showing",e),this.dispatch("change",{detail:n,trigger:e})}}Ef(t){return t.id+":"+t.kind+"-"+t.label.toLowerCase()}}pr.props={offLabel:"Off"};no([J],pr.prototype,"value",1);no([J],pr.prototype,"disabled",1);no([Tt],pr.prototype,"getOptions",1);var A6=Object.defineProperty,M6=Object.getOwnPropertyDescriptor,ro=(s,t,e,i)=>{for(var n=i>1?void 0:i?M6(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&A6(t,e,n),n};const L6=[.25,.5,.75,1,1.25,1.5,1.75,2];class mr extends W{get value(){return this.e.value}get disabled(){const{rates:t}=this.$props,{canSetPlaybackRate:e}=this.a.$state;return!e()||t().length===0}constructor(){super(),this.e=new Hs,this.e.l=this.l.bind(this)}onSetup(){this.a=at(),ne(Nt)&&(this.n=Ct(Nt))}onConnect(t){m(this.O.bind(this)),m(this.Va.bind(this)),m(this.ta.bind(this))}getOptions(){const{rates:t,normalLabel:e}=this.$props;return t().map(i=>({label:i===1?e:i+"×",value:i.toString()}))}O(){this.e.value=this.Z()}Va(){var n;const{normalLabel:t}=this.$props,{playbackRate:e}=this.a.$state,i=e();(n=this.n)==null||n.$b.set(i===1?t():i+"×")}ta(){var t;(t=this.n)==null||t.hb(this.disabled)}Z(){const{playbackRate:t}=this.a.$state;return t().toString()}l(t,e){if(this.disabled)return;const i=+t;this.a.remote.changePlaybackRate(i,e),this.dispatch("change",{detail:i,trigger:e})}}mr.props={normalLabel:"Normal",rates:L6};ro([J],mr.prototype,"value",1);ro([J],mr.prototype,"disabled",1);ro([Tt],mr.prototype,"getOptions",1);var N6=Object.defineProperty,D6=Object.getOwnPropertyDescriptor,ao=(s,t,e,i)=>{for(var n=i>1?void 0:i?D6(t,e):t,r=s.length-1,o;r>=0;r--)(o=s[r])&&(n=(i?o(t,e,n):o(n))||n);return i&&n&&N6(t,e,n),n};class gr extends W{constructor(){super(),this.Sc=es(()=>{const{sort:t}=this.$props,{qualities:e}=this.a.$state;return Ha(e(),t()==="descending")}),this.e=new Hs,this.e.l=this.l.bind(this)}get value(){return this.e.value}get disabled(){const{canSetQuality:t,qualities:e}=this.a.$state;return!t()||e().length<=1}onSetup(){this.a=at(),ne(Nt)&&(this.n=Ct(Nt))}onConnect(t){m(this.O.bind(this)),m(this.ta.bind(this)),m(this.Va.bind(this))}getOptions(){const{autoLabel:t,hideBitrate:e}=this.$props;return[{value:"auto",label:t},...this.Sc().map(i=>{const n=i.bitrate&&i.bitrate>=0?`${Ot(i.bitrate/1e6,2)} Mbps`:null;return{quality:i,label:i.height+"p",value:this.Ff(i),bitrate:()=>e()?null:n}})]}O(){this.e.value=this.Z()}Va(){var r;const{autoLabel:t}=this.$props,{autoQuality:e,quality:i}=this.a.$state,n=i()?i().height+"p":"";(r=this.n)==null||r.$b.set(e()?t()+(n?` (${n})`:""):n)}ta(){var t;(t=this.n)==null||t.hb(this.disabled)}l(t,e){if(this.disabled)return;if(t==="auto"){this.a.remote.changeQuality(-1,e),this.dispatch("change",{detail:"auto",trigger:e});return}const{qualities:i}=this.a.$state,n=$(i).findIndex(r=>this.Ff(r)===t);if(n>=0){const r=$(i)[n];this.a.remote.changeQuality(n,e),this.dispatch("change",{detail:r,trigger:e})}}Z(){const{quality:t,autoQuality:e}=this.a.$state;if(e())return"auto";const i=t();return i?this.Ff(i):"auto"}Ff(t){return t.height+"_"+t.bitrate}}gr.props={autoLabel:"Auto",hideBitrate:!1,sort:"descending"};ao([J],gr.prototype,"value",1);ao([J],gr.prototype,"disabled",1);ao([Tt],gr.prototype,"getOptions",1);let Su=class extends W{constructor(){super(...arguments),this.p=null,this.Bb=0,this.Ph=-1}onSetup(){this.a=at();const{event:t,action:e}=this.$props;this.setAttributes({event:t,action:e})}onAttach(t){t.setAttribute("data-media-gesture",""),t.style.setProperty("pointer-events","none")}onConnect(t){var e;this.p=(e=this.a.player.el)==null?void 0:e.querySelector("[data-media-provider]"),m(this.xm.bind(this))}xm(){let t=this.$props.event(),e=this.$props.disabled();!this.p||!t||e||(/^dbl/.test(t)&&(t=t.split(/^dbl/)[1]),(t==="pointerup"||t==="pointerdown")&&this.a.$state.pointer()==="coarse"&&(t=t==="pointerup"?"touchend":"touchstart"),E(this.p,t,this.ym.bind(this),{passive:!1}))}ym(t){if(this.$props.disabled()||Qo(t)&&(t.button!==0||this.a.activeMenu)||Sn(t)&&this.a.activeMenu||Ba(t)||!this.zm(t))return;t.MEDIA_GESTURE=!0,t.preventDefault();const e=$(this.$props.event);if(!(e==null?void 0:e.startsWith("dbl")))this.Bb===0&&setTimeout(()=>{this.Bb===1&&this.Qh(t)},250);else if(this.Bb===1){queueMicrotask(()=>this.Qh(t)),clearTimeout(this.Ph),this.Bb=0;return}this.Bb===0&&(this.Ph=window.setTimeout(()=>{this.Bb=0},275)),this.Bb++}Qh(t){this.el.setAttribute("data-triggered",""),requestAnimationFrame(()=>{this.Am()&&this.Bm($(this.$props.action),t),requestAnimationFrame(()=>{this.el.removeAttribute("data-triggered")})})}zm(t){if(!this.el)return!1;if(Qo(t)||vf(t)||Sn(t)){const e=Sn(t)?t.changedTouches[0]??t.touches[0]:void 0,i=(e==null?void 0:e.clientX)??t.clientX,n=(e==null?void 0:e.clientY)??t.clientY,r=this.el.getBoundingClientRect(),o=n>=r.top&&n<=r.bottom&&i>=r.left&&i<=r.right;return t.type.includes("leave")?!o:o}return!0}Am(){const t=this.a.player.el.querySelectorAll("[data-media-gesture][data-triggered]");return Array.from(t).sort((e,i)=>+getComputedStyle(i).zIndex-+getComputedStyle(e).zIndex)[0]===this.el}Bm(t,e){if(!t)return;const i=new lt("will-trigger",{detail:t,cancelable:!0,trigger:e});if(this.dispatchEvent(i),i.defaultPrevented)return;const[n,r]=t.replace(/:([a-z])/,"-$1").split(":");t.includes(":fullscreen")?this.a.remote.toggleFullscreen("prefer-media",e):t.includes("seek:")?this.a.remote.seek($(this.a.$state.currentTime)+(+r||0),e):this.a.remote[mc(n)](e),this.dispatch("trigger",{detail:t,trigger:e})}};Su.props={disabled:!1,event:void 0,action:void 0};class I6{constructor(t){this.da=t,this.priority=10,this.K=null,this.Za=mi()}attach(){}canRender(){return!0}detach(){this.Za.empty(),this.da.reset(),this.K=null}changeTrack(t){!t||this.K===t||(this.Za.empty(),t.readyState<2?(this.da.reset(),this.Za.add(E(t,"load",()=>this.Rh(t),{once:!0}))):this.Rh(t),this.Za.add(E(t,"add-cue",e=>{this.da.addCue(e.detail)}),E(t,"remove-cue",e=>{this.da.removeCue(e.detail)})),this.K=t)}Rh(t){this.da.changeTrack({cues:[...t.cues],regions:[...t.regions]})}}let xu=class extends W{constructor(){super(...arguments),this.bc=-1}onSetup(){this.a=at(),this.setAttributes({"aria-hidden":Bt(this.Ub.bind(this))})}onAttach(t){t.style.setProperty("pointer-events","none")}onConnect(t){const e=this.a.player;if(e&&E(e,"vds-font-change",this.Cm.bind(this)),this.da){m(this.Sh.bind(this));return}ie(()=>import("./prod-Dyxyokbu.js").then(i=>i.d),__vite__mapDeps([3,1,2])).then(i=>{this.connectScope&&Dt(()=>{this.M=i;const{CaptionsRenderer:n}=this.M;this.da=new n(t),this.Cb=new I6(this.da),m(this.Sh.bind(this))},this.connectScope)})}onDestroy(){var t;this.Cb&&(this.Cb.detach(),this.a.textRenderers.remove(this.Cb)),(t=this.da)==null||t.destroy()}Ub(){const{textTrack:t,remotePlaybackState:e,iOSControls:i}=this.a.$state,n=t();return i()||e()==="connected"||!n||!It(n)}Sh(){const{viewType:t}=this.a.$state;return t()==="audio"?this.Dm():this.Em()}Dm(){return m(this.qc.bind(this)),()=>{this.el.textContent=""}}qc(){if(this.Ub())return;const{textTrack:t}=this.a.$state;this.Th(),E(t(),"cue-change",this.Th.bind(this)),m(this.Fm.bind(this))}Th(){this.el.textContent="",this.bc>=0&&this.Gf();const{realCurrentTime:t,textTrack:e}=this.a.$state,{renderVTTCueString:i}=this.M,n=$(t),r=$(e).activeCues;for(const o of r){const l=this.Uh(),c=this.Vh();c.innerHTML=i(o,n),l.append(c),this.el.append(c)}}Fm(){const{realCurrentTime:t}=this.a.$state,{updateTimedVTTCueNodes:e}=this.M;e(this.el,t())}Em(){return m(this.Gm.bind(this)),m(this.Hm.bind(this)),this.a.textRenderers.add(this.Cb),()=>{this.el.textContent="",this.Cb.detach(),this.a.textRenderers.remove(this.Cb)}}Gm(){this.da.dir=this.$props.textDir()}Hm(){var i;if(this.Ub())return;const{realCurrentTime:t,textTrack:e}=this.a.$state;this.da.currentTime=t(),this.bc>=0&&((i=e())!=null&&i.activeCues[0])&&this.Gf()}Cm(){var e,i;if(this.bc>=0){this.Wh();return}const{textTrack:t}=this.a.$state;(e=t())!=null&&e.activeCues[0]?(i=this.da)==null||i.update(!0):this.Im()}Im(){var i,n;const t=this.Uh();G(t,"data-example","");const e=this.Vh();G(e,"data-example",""),e.textContent=this.$props.exampleText(),t==null||t.append(e),(i=this.el)==null||i.append(t),(n=this.el)==null||n.setAttribute("data-example",""),this.Wh()}Wh(){window.clearTimeout(this.bc),this.bc=window.setTimeout(this.Gf.bind(this),2500)}Gf(){var t,e;(t=this.el)==null||t.removeAttribute("data-example"),(e=this.el)!=null&&e.querySelector("[data-example]")&&(this.el.textContent=""),this.bc=-1}Uh(){const t=document.createElement("div");return G(t,"data-part","cue-display"),t}Vh(){const t=document.createElement("div");return G(t,"data-part","cue"),t}};xu.props={textDir:"ltr",exampleText:"Captions look like this."};let oo=class extends W{constructor(){super(...arguments),this.Yh=""}onSetup(){this.a=at(),this.Nb(),this.Xh(),this.Da(),this.Fa()}onAttach(t){t.style.setProperty("pointer-events","none"),m(this.lf.bind(this)),m(this.Nb.bind(this)),m(this.Xh.bind(this)),m(this.Da.bind(this)),m(this.Fa.bind(this));const{started:e}=this.a.$state;this.setAttributes({"data-visible":()=>!e()&&!this.$state.hidden(),"data-loading":this.Qc.bind(this),"data-error":this.gb.bind(this),"data-hidden":this.$state.hidden})}onConnect(t){m(this.Jm.bind(this)),m(this.Na.bind(this))}gb(){const{error:t}=this.$state;return!bs(t())}Jm(){const{canLoadPoster:t,poster:e}=this.a.$state;!t()&&e()&&bi(e(),"preconnect")}Fa(){const{src:t}=this.$props,{poster:e,nativeControls:i}=this.a.$state;this.el&&G(this.el,"display",i()?"none":null),this.$state.hidden.set(this.gb()||!(t()||e())||i())}Qc(){const{loading:t,hidden:e}=this.$state;return!e()&&t()}lf(){const t=this.$state.img();t&&(E(t,"load",this.hd.bind(this)),E(t,"error",this.R.bind(this)))}Nb(){const{poster:t}=this.a.$props,{canLoadPoster:e,providedPoster:i,inferredPoster:n}=this.a.$state,r=this.$props.src()||"",o=r||t()||n();this.Yh===i()&&i.set(r),this.$state.src.set(e()&&o.length?o:null),this.Yh=r}Xh(){const{src:t}=this.$props,{alt:e}=this.$state,{poster:i}=this.a.$state;e.set(t()||i()?this.$props.alt():null)}Da(){const{crossOrigin:t}=this.$props,{crossOrigin:e}=this.$state,{crossOrigin:i,poster:n}=this.a.$state,r=t()!==null?t():i();e.set(/ytimg\.com|vimeo/.test(n()||"")?null:r===!0?"anonymous":r)}Na(){const{loading:t,error:e}=this.$state,{canLoadPoster:i,poster:n}=this.a.$state;t.set(i()&&!!n()),e.set(null)}hd(){const{loading:t,error:e}=this.$state;t.set(!1),e.set(null)}R(t){const{loading:e,error:i}=this.$state;e.set(!1),i.set(t)}};oo.props={src:null,alt:null,crossOrigin:null};oo.state=new ys({img:null,src:null,alt:null,crossOrigin:null,loading:!0,error:null,hidden:!1});let lo=class extends W{constructor(){super(...arguments),this.Yc=H(null),this.Mc=H(!0),this.Nc=H(!0)}onSetup(){this.a=at(),this.Zh();const{type:t}=this.$props;this.setAttributes({"data-type":t,"data-remainder":this._h.bind(this)}),new wu({callback:this.hf.bind(this)}).attach(this)}onAttach(t){t.hasAttribute("role")||m(this.Km.bind(this)),m(this.Zh.bind(this))}onConnect(t){Y(Uc(t,this.Mc.set)),m(this.Fa.bind(this)),m(this.Lm.bind(this))}hf(t){this.Nc.set(t[0].isIntersecting)}Fa(){const{hidden:t}=this.$props;this.$state.hidden.set(t()||!this.Mc()||!this.Nc())}Lm(){if(!this.$props.toggle()){this.Yc.set(null);return}this.el&&ms(this.el,this.Mm.bind(this))}Zh(){const{hidden:t,timeText:e}=this.$state,{duration:i}=this.a.$state;if(t())return;const{type:n,padHours:r,padMinutes:o,showHours:l}=this.$props,c=this.Nm(n()),u=i(),h=this._h();if(!Number.isFinite(c+u)){e.set("LIVE");return}const d=h?Math.max(0,u-c):c,f=Qi(d,{padHrs:r(),padMins:o(),showHrs:l()});e.set((h?"-":"")+f)}Km(){if(!this.el)return;const{toggle:t}=this.$props;G(this.el,"role",t()?"timer":null),G(this.el,"tabindex",t()?0:null)}Nm(t){const{bufferedEnd:e,duration:i,currentTime:n}=this.a.$state;switch(t){case"buffered":return e();case"duration":return i();default:return n()}}_h(){return this.$props.remainder()&&this.Yc()!==!1}Mm(t){if(t.preventDefault(),this.Yc()===null){this.Yc.set(!this.$props.remainder());return}this.Yc.set(e=>!e)}};lo.props={type:"current",showHours:!1,padHours:null,padMinutes:null,remainder:!1,toggle:!1,hidden:!1};lo.state=new ys({timeText:"",hidden:!1});const O6=is(),R6={clickToPlay:!0,clickToFullscreen:!0,controls:["play-large","play","progress","current-time","mute+volume","captions","settings","pip","airplay","fullscreen"],customIcons:!1,displayDuration:!1,download:null,markers:null,invertTime:!0,thumbnails:null,toggleTime:!0,translations:null,seekTime:10,speed:[.5,.75,1,1.25,1.5,1.75,2,4]};class V6 extends W{onSetup(){this.a=at(),Wt(O6,{...this.$props,previewTime:H(0)})}}V6.props=R6;class ku extends Sm{}class _6 extends nr{}class F6 extends Wa{}class B6 extends lu{}class q6 extends Im{}class H6 extends Ua{}class j6 extends mu{}class G6 extends gu{}class W6 extends Cu{}class U6 extends bu{}class z6 extends yu{}class Z6 extends pu{}class K6 extends du{}class Q6 extends fu{}class Y6 extends vu{}class X6 extends uu{}class J6 extends Rm{}class t3 extends hu{}class Pu extends Bs{}class e3 extends or{}class s3 extends Qa{}class i3 extends Ya{}class n3 extends Xa{}class r3 extends Ja{}class a3 extends Gm{}class o3 extends Ka{}class l3 extends ar{}class c3 extends Eu{}class u3 extends yi{}class h3 extends qs{}class d3 extends cr{}class f3 extends $u{}class p3 extends f6{}class m3 extends ur{}class g3 extends to{}class b3 extends xu{}class y3 extends Su{}class v3 extends oo{}class Au extends rr{}class C3 extends lo{}const Mu=a.forwardRef((s,t)=>{const{children:e,...i}=s,n=a.Children.toArray(e),r=n.find(E3);if(r){const o=r.props.children,l=n.map(c=>c===r?a.Children.count(o)>1?a.Children.only(null):a.isValidElement(o)?o.props.children:null:c);return a.createElement(da,{...i,ref:t},a.isValidElement(o)?a.cloneElement(o,void 0,l):null)}return a.createElement(da,{...i,ref:t},e)});Mu.displayName="Slot";const da=a.forwardRef((s,t)=>{const{children:e,...i}=s;return a.isValidElement(e)?a.cloneElement(e,{...$3(i,e.props),ref:t?pt(t,e.ref):e.ref}):a.Children.count(e)>1?a.Children.only(null):null});da.displayName="SlotClone";const w3=({children:s})=>a.createElement(a.Fragment,null,s);function E3(s){return a.isValidElement(s)&&s.type===w3}function $3(s,t){const e={...t};for(const i in t){const n=s[i],r=t[i];/^on[A-Z]/.test(i)?n&&r?e[i]=(...l)=>{r(...l),n(...l)}:n&&(e[i]=n):i==="style"?e[i]={...n,...r}:i==="className"&&(e[i]=[n,r].filter(Boolean).join(" "))}return{...s,...e}}const T3=["button","div","span","img","video","audio"],R=T3.reduce((s,t)=>{const e=a.forwardRef((i,n)=>{const{asChild:r,...o}=i,l=r?Mu:t;return a.createElement(l,{...o,ref:n})});return e.displayName=`Primitive.${t}`,{...s,[t]:e}},{});function S3(s){return(s==null?void 0:s.$$PROVIDER_TYPE)==="REMOTION"}function Lu(s){return(s==null?void 0:s.type)==="video/remotion"}const Al=Pu.state.record,x3=Object.keys(Al).reduce((s,t)=>({...s,[t](){return Al[t]}}),{});function jr(s,t){var i;const e=Ui(Fs);return rt((((i=t==null?void 0:t.current)==null?void 0:i.$state)||e||x3)[s])}const Ml=ku.state.record,k3=Object.keys(Ml).reduce((s,t)=>({...s,[t](){return Ml[t]}}),{});function z(s,t){var i;const e=Ui(zi);return rt((((i=t==null?void 0:t.current)==null?void 0:i.$state)||e||k3)[s])}function Ve(){return gc(Zi)}const P3=tt(K6,{domEventsRegex:/^onMedia/}),Nu=a.forwardRef(({children:s,...t},e)=>a.createElement(P3,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));Nu.displayName="AirPlayButton";const A3=tt(Z6,{domEventsRegex:/^onMedia/}),Du=a.forwardRef(({children:s,...t},e)=>a.createElement(A3,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));Du.displayName="PlayButton";const M3=tt(j6,{domEventsRegex:/^onMedia/}),Iu=a.forwardRef(({children:s,...t},e)=>a.createElement(M3,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));Iu.displayName="CaptionButton";const L3=tt(G6,{domEventsRegex:/^onMedia/}),Ou=a.forwardRef(({children:s,...t},e)=>a.createElement(L3,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));Ou.displayName="FullscreenButton";const N3=tt(U6,{domEventsRegex:/^onMedia/}),Ru=a.forwardRef(({children:s,...t},e)=>a.createElement(N3,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));Ru.displayName="MuteButton";const D3=tt(z6,{domEventsRegex:/^onMedia/}),Vu=a.forwardRef(({children:s,...t},e)=>a.createElement(D3,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));Vu.displayName="PIPButton";const I3=tt(Y6,{domEventsRegex:/^onMedia/}),_u=a.forwardRef(({children:s,...t},e)=>a.createElement(I3,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));_u.displayName="SeekButton";const O3=tt(W6,{domEventsRegex:/^onMedia/}),Fu=a.forwardRef(({children:s,...t},e)=>a.createElement(O3,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));Fu.displayName="LiveButton";const vi=["onDragStart","onDragEnd","onDragValueChange","onValueChange","onPointerValueChange"],R3=tt(o3),V3=tt(Pu,{events:vi}),Bu=a.forwardRef(({children:s,...t},e)=>a.createElement(V3,{...t,ref:e},i=>a.createElement(R.div,{...i},s)));Bu.displayName="Slider";const br=a.forwardRef((s,t)=>a.createElement(R.div,{...s,ref:t}));br.displayName="SliderThumb";const yr=a.forwardRef((s,t)=>a.createElement(R.div,{...s,ref:t}));yr.displayName="SliderTrack";const vr=a.forwardRef((s,t)=>a.createElement(R.div,{...s,ref:t}));vr.displayName="SliderTrackFill";const _3=tt(c3),co=a.forwardRef(({children:s,...t},e)=>a.createElement(_3,{...t},i=>a.createElement(R.div,{...i,ref:pt(i.ref,e)},s)));co.displayName="SliderPreview";const uo=a.forwardRef(({children:s,...t},e)=>a.createElement(R3,{...t},(i,n)=>{const r=rt(()=>n.getValueText(),n);return a.createElement(R.div,{...i,ref:e},r,s)}));uo.displayName="SliderValue";const qu=a.forwardRef(({children:s,...t},e)=>{const i=jr("min"),n=jr("max"),r=jr("step"),o=(n-i)/r;return a.createElement(R.div,{...t,ref:e},Array.from({length:Math.floor(o)+1}).map((l,c)=>s(c)))});qu.displayName="SliderSteps";const F3=tt(s3,{events:vi,domEventsRegex:/^onMedia/}),Hu=a.forwardRef(({children:s,...t},e)=>a.createElement(F3,{...t,ref:e},i=>a.createElement(R.div,{...i},s)));Hu.displayName="VolumeSlider";function B3(s=0,t=0,e=""){return P?{startTime:s,endTime:t,text:e,addEventListener:fe,removeEventListener:fe,dispatchEvent:fe}:new window.VTTCue(s,t,e)}const q3=tt(Au),ju=a.forwardRef(({children:s,...t},e)=>a.createElement(q3,{...t},i=>a.createElement(R.div,{...i,ref:pt(i.ref,e)},s)));ju.displayName="Thumbnail";const ho=a.forwardRef(({children:s,...t},e)=>{const{src:i,img:n,crossOrigin:r}=Ui(Au.state),o=rt(i),l=rt(r);return a.createElement(R.img,{crossOrigin:l,...t,src:o,ref:pt(n.set,e)},s)});ho.displayName="ThumbnailImg";const Cr=a.createContext({$chapters:H(null)});Cr.displayName="TimeSliderContext";const H3=tt(e3,{events:vi,domEventsRegex:/^onMedia/}),Gu=a.forwardRef(({children:s,...t},e)=>{const i=a.useMemo(()=>H(null),[]);return a.createElement(Cr.Provider,{value:{$chapters:i}},a.createElement(H3,{...t,ref:e},n=>a.createElement(R.div,{...n},s)))});Gu.displayName="TimeSlider";const j3=tt(u3),Wu=a.forwardRef(({children:s,...t},e)=>a.createElement(j3,{...t},(i,n)=>a.createElement(R.div,{...i,ref:pt(i.ref,e)},a.createElement(Uu,{instance:n},s))));Wu.displayName="SliderChapters";function Uu({instance:s,children:t}){const e=rt(()=>s.cues,s),i=a.useRef([]),n=a.useRef(),{$chapters:r}=a.useContext(Cr);return n.current||(n.current=B3()),a.useEffect(()=>(r.set(s),()=>void r.set(null)),[s]),a.useEffect(()=>{s.setRefs(i.current)},[e]),t(e.length?e:[n.current],o=>{if(!o){i.current.length=0;return}i.current.push(o)})}Uu.displayName="SliderChapterTracks";const zu=a.forwardRef(({children:s,...t},e)=>{const{$chapters:i}=a.useContext(Cr),[n,r]=a.useState();return a.useEffect(()=>m(()=>{const o=i(),l=(o==null?void 0:o.activePointerCue)||(o==null?void 0:o.activeCue);r((l==null?void 0:l.text)||"")}),[]),a.createElement(R.div,{...t,ref:e},n,s)});zu.displayName="SliderChapterTitle";const Zu=a.forwardRef((s,t)=>a.createElement(R.div,{...s,ref:t}));Zu.displayName="SliderProgress";const G3=tt(a3),Ku=a.forwardRef(({children:s,...t},e)=>a.createElement(G3,{...t},i=>a.createElement(R.div,{...i,ref:pt(i.ref,e)},s)));Ku.displayName="SliderThumbnail";const Ll={Root:Ku,Img:ho},W3=tt(l3,{events:["onCanPlay","onError"]}),U3=a.forwardRef(({children:s,...t},e)=>a.createElement(W3,{...t},(i,n)=>a.createElement(Qu,{...i,instance:n,ref:pt(i.ref,e)},s)));U3.displayName="SliderVideo";const Qu=a.forwardRef(({instance:s,children:t,...e},i)=>{const{canLoad:n}=Ui(zi),{src:r,video:o,crossOrigin:l}=s.$state,c=rt(r),u=rt(n),h=rt(l);return a.createElement(R.video,{style:{maxWidth:"unset"},...e,src:c||void 0,muted:!0,playsInline:!0,preload:u?"auto":"none",crossOrigin:h||void 0,ref:pt(o.set,i)},t)});Qu.displayName="SliderVideoProvider";const z3=tt(m3,{events:["onChange"]}),Xi=a.forwardRef(({children:s,...t},e)=>a.createElement(z3,{...t,ref:e},i=>a.createElement(R.div,{...i},s)));Xi.displayName="RadioGroup";const Z3=tt(g3,{events:["onChange","onSelect"]}),Ji=a.forwardRef(({children:s,...t},e)=>a.createElement(Z3,{...t},i=>a.createElement(R.div,{...i,ref:pt(i.ref,e)},s)));Ji.displayName="RadioItem";const K3=typeof document>"u",Q3=tt(h3,{events:["onOpen","onClose"],domEventsRegex:/^onMedia/}),_e=a.forwardRef(({children:s,...t},e)=>a.createElement(Q3,{...t,ref:e},(i,n)=>a.createElement(R.div,{...i,style:{display:n.isSubmenu?void 0:"contents",...i.style}},s)));_e.displayName="Menu";const Y3=tt(d3,{events:["onSelect"]}),wr=a.forwardRef(({children:s,...t},e)=>a.createElement(Y3,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));wr.displayName="MenuButton";const fo=a.forwardRef(({disabled:s=!1,children:t,...e},i)=>{let n=z("fullscreen");return K3||!(s==="fullscreen"?!n:!s)?t:Un.createPortal(a.createElement(R.div,{...e,style:{display:"contents",...e.style},ref:i},t),document.body)});fo.displayName="MenuPortal";const X3=tt(f3),Fe=a.forwardRef(({children:s,...t},e)=>a.createElement(X3,{...t},i=>a.createElement(R.div,{...i,ref:pt(i.ref,e)},s)));Fe.displayName="MenuItems";const J3=tt(p3),t2=a.forwardRef(({children:s,...t},e)=>a.createElement(J3,{...t},i=>a.createElement(R.div,{...i,ref:pt(i.ref,e)},s)));t2.displayName="MenuItem";const e2=tt(y3,{events:["onWillTrigger","onTrigger"]}),Js=a.forwardRef(({children:s,...t},e)=>a.createElement(e2,{...t,ref:e},i=>a.createElement(R.div,{...i},s)));Js.displayName="Gesture";const s2=tt(C3),Bi=a.forwardRef(({children:s,...t},e)=>a.createElement(s2,{...t},(i,n)=>a.createElement(Yu,{...i,instance:n,ref:pt(i.ref,e)},s)));Bi.displayName="Time";const Yu=a.forwardRef(({instance:s,children:t,...e},i)=>{const{timeText:n}=s.$state,r=rt(n);return a.createElement(R.div,{...e,ref:i},r,t)});Yu.displayName="TimeText";function Xu(){const s=Ve();return(s==null?void 0:s.player)||null}function i2(){const s=Ve(),{audioTracks:t,audioTrack:e}=s.$state,i=rt(t);return rt(e),a.useMemo(()=>{const n=i.map(r=>({track:r,label:r.label,value:Nl(r),get selected(){return e()===r},select(o){const l=t().indexOf(r);l>=0&&s.remote.changeAudioTrack(l,o)}}));return Object.defineProperty(n,"disabled",{get(){return n.length<=1}}),Object.defineProperty(n,"selectedTrack",{get(){return e()}}),Object.defineProperty(n,"selectedValue",{get(){const r=e();return r?Nl(r):void 0}}),n},[i])}function Nl(s){return s.label.toLowerCase()}function n2({off:s=!0}={}){const t=Ve(),{textTracks:e,textTrack:i}=t.$state,n=rt(e);return rt(i),a.useMemo(()=>{const r=n.filter(It),o=r.map(l=>({track:l,label:l.label,value:Dl(l),get selected(){return i()===l},select(c){const u=e().indexOf(l);u>=0&&t.remote.changeTextTrackMode(u,"showing",c)}}));return s&&o.unshift({track:null,label:q(s)?s:"Off",value:"off",get selected(){return!i()},select(l){t.remote.toggleCaptions(l)}}),Object.defineProperty(o,"disabled",{get(){return!r.length}}),Object.defineProperty(o,"selectedTrack",{get(){return i()}}),Object.defineProperty(o,"selectedValue",{get(){const l=i();return l?Dl(l):"off"}}),o},[n])}function Dl(s){return s.id+":"+s.kind+"-"+s.label.toLowerCase()}const r2=tt(F6,{events:["onChange"]}),Ju=a.forwardRef(({style:s,children:t,...e},i)=>a.createElement(r2,{...e},n=>a.createElement(R.div,{...n,style:{display:"contents",...s},ref:pt(n.ref,i)},t)));Ju.displayName="MediaAnnouncer";const a2=tt(B6),Er=a.forwardRef(({children:s,...t},e)=>a.createElement(a2,{...t},i=>a.createElement(R.div,{...i,ref:pt(i.ref,e)},s)));Er.displayName="Controls";const o2=tt(q6),De=a.forwardRef(({children:s,...t},e)=>a.createElement(o2,{...t},i=>a.createElement(R.div,{...i,ref:pt(i.ref,e)},s)));De.displayName="ControlsGroup";const l2=tt(X6);function th({children:s,...t}){return a.createElement(l2,{...t},s)}th.displayName="Tooltip";const c2=tt(J6),eh=a.forwardRef(({children:s,...t},e)=>a.createElement(c2,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));eh.displayName="TooltipTrigger";const u2=tt(t3),sh=a.forwardRef(({children:s,...t},e)=>a.createElement(u2,{...t},i=>a.createElement(R.div,{...i,ref:pt(i.ref,e)},s)));sh.displayName="TooltipContent";const h2=tt(Q6,{domEventsRegex:/^onMedia/}),ih=a.forwardRef(({children:s,...t},e)=>a.createElement(h2,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));ih.displayName="GoogleCastButton";const d2=tt(r3,{events:vi,domEventsRegex:/^onMedia/}),nh=a.forwardRef(({children:s,...t},e)=>a.createElement(d2,{...t,ref:e},i=>a.createElement(R.div,{...i},s)));nh.displayName="QualitySlider";const f2=tt(i3,{events:vi,domEventsRegex:/^onMedia/}),rh=a.forwardRef(({children:s,...t},e)=>a.createElement(f2,{...t,ref:e},i=>a.createElement(R.div,{...i},s)));rh.displayName="AudioGainSlider";const p2=tt(n3,{events:vi,domEventsRegex:/^onMedia/}),ah=a.forwardRef(({children:s,...t},e)=>a.createElement(p2,{...t,ref:e},i=>a.createElement(R.div,{...i},s)));ah.displayName="SpeedSlider";const oh=a.forwardRef(({children:s,...t},e)=>{const i=z("title");return a.createElement(R.span,{...t,ref:e},i,s)});oh.displayName="Title";function lh(s){const[t,e]=a.useState([]);return a.useEffect(()=>{if(!s){e([]);return}function i(){e(s.activeCues)}return i(),E(s,"cue-change",i)},[s]),t}function po(s){const t=Ve(),[e,i]=a.useState(null);return a.useEffect(()=>er(t.textTracks,s,i),[s]),e}function ch(){var e;const s=po("chapters");return((e=lh(s)[0])==null?void 0:e.text)||""}const uh=a.forwardRef(({defaultText:s="",children:t,...e},i)=>{const n=ch();return a.createElement(R.span,{...e,ref:i},n||s,t)});uh.displayName="ChapterTitle";const m2=tt(b3),mo=a.forwardRef(({children:s,...t},e)=>a.createElement(m2,{...t,ref:e},i=>a.createElement(R.div,{...i},s)));mo.displayName="Captions";const g2=a.forwardRef(({size:s=96,children:t,...e},i)=>a.createElement("svg",{width:s,height:s,fill:"none",viewBox:"0 0 120 120","aria-hidden":"true","data-part":"root",...e,ref:i},t)),b2=a.forwardRef(({width:s=8,children:t,...e},i)=>a.createElement("circle",{cx:"60",cy:"60",r:"54",stroke:"currentColor",strokeWidth:s,"data-part":"track",...e,ref:i},t)),y2=a.forwardRef(({width:s=8,fillPercent:t=50,children:e,...i},n)=>a.createElement("circle",{cx:"60",cy:"60",r:"54",stroke:"currentColor",pathLength:"100",strokeWidth:s,strokeDasharray:100,strokeDashoffset:100-t,"data-part":"track-fill",...i,ref:n},e));function Il(s,t=[]){const e=tr();return a.useMemo(()=>Dt(()=>H(s),e),[e,...t])}function Hn(s,t=[]){const e=tr();return a.useMemo(()=>Dt(()=>es(s),e),[e,...t])}function v2(s,t=[]){const e=tr();a.useEffect(()=>Dt(()=>m(s),e),[e,...t])}function C2(s){const[t,e]=a.useState([]);return a.useEffect(()=>{if(!s)return;function i(r){e([...r.cues])}const n=mi();return n.add(E(s,"add-cue",()=>i(s)),E(s,"remove-cue",()=>i(s))),i(s),()=>{n.empty(),e([])}},[s]),t}function w2(){const s=Ve(),t=po("chapters"),e=C2(t),i=rt(s.$state.clipStartTime),n=rt(s.$state.clipEndTime)||1/0;return lh(t),a.useMemo(()=>{const r=t?e.filter(o=>o.startTime<=n&&o.endTime>=i).map((o,l)=>{let c=null,u;return{cue:o,label:o.text,value:l.toString(),startTimeText:Qi(Math.max(0,o.startTime-i)),durationText:Fi(Math.min(n,o.endTime)-Math.max(i,o.startTime)),get selected(){return o===t.activeCues[0]},setProgressVar(h){if(!h||o!==t.activeCues[0]){u==null||u(),u=void 0,h==null||h.style.setProperty("--progress","0%"),c=null;return}c!==h&&(c=h,u==null||u(),u=m(()=>{const{realCurrentTime:d}=s.$state,f=d(),p=Math.max(i,o.startTime),g=Math.min(n,o.endTime)-p,b=Math.max(0,f-p)/g*100;h.style.setProperty("--progress",b.toFixed(3)+"%")}))},select(h){s.remote.seek(o.startTime-i,h)}}}):[];return Object.defineProperty(r,"selectedValue",{get(){const o=r.findIndex(l=>l.selected);return(o>=0?o:0).toString()}}),r},[e,i,n])}const go=a.forwardRef((s,t)=>{const{width:e,height:i,size:n=null,paths:r,...o}=s;return a.createElement("svg",{viewBox:"0 0 32 32",...o,width:e??n,height:i??n,fill:"none","aria-hidden":"true",focusable:"false",xmlns:"http://www.w3.org/2000/svg",ref:t,dangerouslySetInnerHTML:{__html:r}})});go.displayName="VidstackIcon";const E2=["onAbort","onAudioTrackChange","onAudioTracksChange","onAutoPlay","onAutoPlayChange","onAutoPlayFail","onCanLoad","onCanPlay","onCanPlayThrough","onControlsChange","onDestroy","onDurationChange","onEmptied","onEnd","onEnded","onError","onFindMediaPlayer","onFullscreenChange","onFullscreenError","onLiveChange","onLiveEdgeChange","onLoadedData","onLoadedMetadata","onLoadStart","onLoopChange","onOrientationChange","onPause","onPictureInPictureChange","onPictureInPictureError","onPlay","onPlayFail","onPlaying","onPlaysInlineChange","onPosterChange","onProgress","onProviderChange","onProviderLoaderChange","onProviderSetup","onQualitiesChange","onQualityChange","onRateChange","onReplay","onSeeked","onSeeking","onSourceChange","onSourceChange","onStalled","onStarted","onStreamTypeChange","onSuspend","onTextTrackChange","onTextTracksChange","onTimeUpdate","onTitleChange","onVdsLog","onVideoPresentationChange","onVolumeChange","onWaiting"],$2=tt(ku,{events:E2,eventsRegex:/^onHls/,domEventsRegex:/^onMedia/}),hh=a.forwardRef(({aspectRatio:s,children:t,...e},i)=>a.createElement($2,{...e,src:e.src,ref:i,style:{aspectRatio:s,...e.style}},n=>a.createElement(R.div,{...n},t)));hh.displayName="MediaPlayer";const T2=tt(_6),dh=a.forwardRef(({loaders:s=[],children:t,mediaProps:e,...i},n)=>{const r=a.useMemo(()=>s.map(o=>new o),s);return a.createElement(T2,{...i,loaders:r,ref:n},(o,l)=>a.createElement("div",{...o},a.createElement(fh,{...e,provider:l}),t))});dh.displayName="MediaProvider";function fh({provider:s,...t}){const{crossOrigin:e,poster:i,remotePlaybackInfo:n,nativeControls:r}=Ui(zi),{loader:o}=s.$state,{$provider:l,$providerSetup:c}=Ve(),u=rt(r),h=rt(e),d=rt(i),f=rt(o),p=rt(l),g=rt(c),b=rt(n),y=f==null?void 0:f.mediaType(),w=(f==null?void 0:f.name)==="youtube",S=(f==null?void 0:f.name)==="vimeo",N=w||S,j=(f==null?void 0:f.name)==="remotion",V=(f==null?void 0:f.name)==="google-cast",[K,ot]=a.useState("");return a.useEffect(()=>{!V||K||ie(()=>Promise.resolve().then(()=>Q2),void 0).then(C=>{ot(C.default)})},[V]),V?a.createElement("div",{className:"vds-google-cast",ref:C=>{s.load(C)}},a.createElement(go,{paths:K}),b!=null&&b.deviceName?a.createElement("span",{className:"vds-google-cast-info"},"Google Cast on"," ",a.createElement("span",{className:"vds-google-cast-device-name"},b.deviceName)):null):j?a.createElement("div",{"data-remotion-canvas":!0},a.createElement("div",{"data-remotion-container":!0,ref:C=>{s.load(C)}},S3(p)&&g?a.createElement(p.render):null)):N?a.createElement(a.Fragment,null,a.createElement("iframe",{className:w?"vds-youtube":"vds-vimeo",suppressHydrationWarning:!0,tabIndex:u?void 0:-1,"aria-hidden":"true","data-no-controls":u?void 0:"",ref(C){s.load(C)}}),u?null:a.createElement("div",{className:"vds-blocker"})):y?a.createElement(y==="audio"?"audio":"video",{...t,controls:u?!0:null,crossOrigin:typeof h=="boolean"?"":h,poster:y==="video"&&u&&d?d:null,preload:"none","aria-hidden":"true",suppressHydrationWarning:!0,ref(C){s.load(C)}}):null}fh.displayName="MediaOutlet";const S2=tt(H6),x2=a.forwardRef(({children:s,...t},e)=>a.createElement(S2,{...t},i=>a.createElement(R.button,{...i,ref:pt(i.ref,e)},s)));x2.displayName="ToggleButton";const k2=tt(v3),ph=a.forwardRef(({children:s,...t},e)=>a.createElement(k2,{...t},(i,n)=>a.createElement(mh,{...i,instance:n,ref:pt(i.ref,e)},s)));ph.displayName="Poster";const mh=a.forwardRef(({instance:s,children:t,...e},i)=>{const{src:n,img:r,alt:o,crossOrigin:l,loading:c,hidden:u}=s.$state,h=rt(n),d=rt(o),f=rt(l),p=rt(c),g=rt(u);return a.createElement(R.img,{...e,src:h||"",alt:d||void 0,crossOrigin:f||void 0,ref:pt(r.set,i),style:{display:p||g?"none":void 0}},t)});mh.displayName="PosterImg";const P2=a.forwardRef(({children:s,...t},e)=>a.createElement(R.div,{translate:"yes","aria-live":"off","aria-atomic":"true",...t,ref:e},s));P2.displayName="Caption";const A2=a.forwardRef((s,t)=>{const e=z("textTrack"),[i,n]=a.useState();return a.useEffect(()=>{if(!e)return;function r(){n(e==null?void 0:e.activeCues[0])}return e.addEventListener("cue-change",r),()=>{e.removeEventListener("cue-change",r),n(void 0)}},[e]),a.createElement(R.span,{...s,"data-part":"cue",dangerouslySetInnerHTML:{__html:(i==null?void 0:i.text)||""},ref:t})});A2.displayName="CaptionText";function gh(s,t){a.useEffect(()=>{if(!s)return;t();const e=new ResizeObserver(Rs(t));return e.observe(s),()=>e.disconnect()},[s,t])}function M2(s){const[t,e]=a.useState(!1);return a.useEffect(()=>{if(!s)return;const i=mi();return i.add(E(s,"transitionstart",()=>e(!0)),E(s,"transitionend",()=>e(!1))),()=>i.empty()},[s]),t}function L2(s){const[t,e]=a.useState(!1);return a.useEffect(()=>{if(!s)return;const i=mi();return i.add(E(s,"mouseenter",()=>e(!0)),E(s,"mouseleave",()=>e(!1))),()=>i.empty()},[s]),t}function N2(s){const[t,e]=a.useState(!1);return a.useEffect(()=>{if(!s)return;const i=mi();return i.add(E(s,"focusin",()=>e(!0)),E(s,"focusout",()=>e(!1))),()=>i.empty()},[s]),t}function D2(s){const t=L2(s),e=N2(s),i=a.useRef(!1);return i.current&&!t?!1:(i.current=t,t||e)}function I2(){const[s,t]=a.useState("dark");return a.useEffect(()=>{const e=window.matchMedia("(prefers-color-scheme: light)");function i(){t(e.matches?"light":"dark")}return i(),E(e,"change",i)},[]),s}function bh(s){const t=Xu();a.useEffect(()=>{if(t)return m(()=>{const e=t.$el;return e==null||e.setAttribute("data-layout",s),()=>e==null?void 0:e.removeAttribute("data-layout")})},[t])}const O2=H(null),R2=H(null);H(null);const Ci=a.createContext({});Ci.displayName="DefaultLayoutContext";function Z(){return a.useContext(Ci)}function O(s){const{translations:t}=Z();return bo(t,s)}function bo(s,t){return(s==null?void 0:s[t])??t}function yo(s){const t=I2();return s==="default"?null:s==="system"?t:s}function yh({type:s,smLayoutWhen:t,renderLayout:e}){const i=a.forwardRef(({children:n,className:r,disableTimeSlider:o=!1,hideQualityBitrate:l=!1,icons:c,colorScheme:u="system",download:h=null,menuGroup:d="bottom",noAudioGain:f=!1,audioGains:p={min:0,max:300,step:25},noGestures:g=!1,noKeyboardAnimations:b=!1,noModal:y=!1,noScrubGesture:w,playbackRates:S={min:0,max:2,step:.25},seekStep:N=10,showMenuDelay:j,showTooltipDelay:V=700,sliderChaptersMinWidth:K=325,slots:ot,smallLayoutWhen:C=t,thumbnails:k=null,translations:_,...St},gt)=>{const ct=Ve(),et=rt(ct.$props.load),T=z("canLoad"),x=z("viewType"),D=z("streamType"),st=Hn(()=>Oi(C)?C:C(ct.player.state),[C]),At=Il(!0),Et=Il(!0),Rt=x===s,Vt=st(),_t=Oi(C),$t=et==="play"&&!T,L=T||_t||$t,I=yo(u);return rt(st),a.createElement("div",{...St,className:`vds-${s}-layout`+(I?` ${I}`:"")+(r?` ${r}`:""),"data-match":Rt?"":null,"data-sm":Vt?"":null,"data-lg":Vt?null:"","data-size":Vt?"sm":"lg","data-no-scrub-gesture":w?"":null,ref:gt},L&&Rt?a.createElement(Ci.Provider,{value:{disableTimeSlider:o,hideQualityBitrate:l,icons:c,colorScheme:u,download:h,isSmallLayout:Vt,menuGroup:d,noAudioGain:f,audioGains:p,noGestures:g,noKeyboardAnimations:b,noModal:y,noScrubGesture:w,showMenuDelay:j,showTooltipDelay:V,sliderChaptersMinWidth:K,slots:ot,seekStep:N,playbackRates:S,thumbnails:k,translations:_,userPrefersAnnouncements:At,userPrefersKeyboardAnimations:Et}},e({streamType:D,isSmallLayout:Vt,isLoadLayout:$t}),n):null)});return i.displayName="DefaultMediaLayout",i}function vh(){return a.useContext(Ci).slots}function vo(){return a.useContext(Ci).slots}function A(s,t,e){const i=s==null?void 0:s[t],n=Wi(t);return a.createElement(a.Fragment,null,s==null?void 0:s[`before${n}`],kt(i)?e:i,s==null?void 0:s[`after${n}`])}function $r(){const{userPrefersAnnouncements:s,translations:t}=Z();return rt(s)?a.createElement(Ju,{translations:t}):null}$r.displayName="DefaultAnnouncer";function ge({content:s,placement:t,children:e}){const{showTooltipDelay:i}=Z();return a.createElement(th,{showDelay:i},a.createElement(eh,{asChild:!0},e),a.createElement(sh,{className:"vds-tooltip-content",placement:t},s))}ge.displayName="DefaultTooltip";function tn({tooltip:s}){const{icons:t}=Z(),e=O("Play"),i=O("Pause"),n=z("paused"),r=z("ended");return a.createElement(ge,{content:n?e:i,placement:s},a.createElement(Du,{className:"vds-play-button vds-button","aria-label":e},r?a.createElement(t.PlayButton.Replay,{className:"vds-icon"}):n?a.createElement(t.PlayButton.Play,{className:"vds-icon"}):a.createElement(t.PlayButton.Pause,{className:"vds-icon"})))}tn.displayName="DefaultPlayButton";const Ch=a.forwardRef(({tooltip:s},t)=>{const{icons:e}=Z(),i=O("Mute"),n=O("Unmute"),r=z("muted"),o=z("volume");return a.createElement(ge,{content:r?n:i,placement:s},a.createElement(Ru,{className:"vds-mute-button vds-button","aria-label":i,ref:t},r||o==0?a.createElement(e.MuteButton.Mute,{className:"vds-icon"}):o<.5?a.createElement(e.MuteButton.VolumeLow,{className:"vds-icon"}):a.createElement(e.MuteButton.VolumeHigh,{className:"vds-icon"})))});Ch.displayName="DefaultMuteButton";function Tr({tooltip:s}){const{icons:t}=Z(),e=O("Captions"),i=O("Closed-Captions On"),n=O("Closed-Captions Off"),r=z("textTrack"),o=r&&It(r);return a.createElement(ge,{content:o?i:n,placement:s},a.createElement(Iu,{className:"vds-caption-button vds-button","aria-label":e},o?a.createElement(t.CaptionButton.On,{className:"vds-icon"}):a.createElement(t.CaptionButton.Off,{className:"vds-icon"})))}Tr.displayName="DefaultCaptionButton";function wh({tooltip:s}){const{icons:t}=Z(),e=O("PiP"),i=O("Enter PiP"),n=O("Exit PiP"),r=z("pictureInPicture");return a.createElement(ge,{content:r?n:i,placement:s},a.createElement(Vu,{className:"vds-pip-button vds-button","aria-label":e},r?a.createElement(t.PIPButton.Exit,{className:"vds-icon"}):a.createElement(t.PIPButton.Enter,{className:"vds-icon"})))}wh.displayName="DefaultPIPButton";function Co({tooltip:s}){const{icons:t}=Z(),e=O("Fullscreen"),i=O("Enter Fullscreen"),n=O("Exit Fullscreen"),r=z("fullscreen");return a.createElement(ge,{content:r?n:i,placement:s},a.createElement(Ou,{className:"vds-fullscreen-button vds-button","aria-label":e},r?a.createElement(t.FullscreenButton.Exit,{className:"vds-icon"}):a.createElement(t.FullscreenButton.Enter,{className:"vds-icon"})))}Co.displayName="DefaultFullscreenButton";function fa({backward:s,tooltip:t}){const{icons:e,seekStep:i}=Z(),n=O("Seek Forward"),r=O("Seek Backward"),o=(s?-1:1)*i,l=o>=0?n:r;return a.createElement(ge,{content:l,placement:t},a.createElement(_u,{className:"vds-seek-button vds-button",seconds:o,"aria-label":l},o>=0?a.createElement(e.SeekButton.Forward,{className:"vds-icon"}):a.createElement(e.SeekButton.Backward,{className:"vds-icon"})))}fa.displayName="DefaultSeekButton";function wo({tooltip:s}){const{icons:t}=Z(),e=O("AirPlay"),i=z("remotePlaybackState"),n=O(Wi(i)),r=`${e} ${n}`,o=(i==="connecting"?t.AirPlayButton.Connecting:i==="connected"?t.AirPlayButton.Connected:null)??t.AirPlayButton.Default;return a.createElement(ge,{content:e,placement:s},a.createElement(Nu,{className:"vds-airplay-button vds-button","aria-label":r},a.createElement(o,{className:"vds-icon"})))}wo.displayName="DefaultAirPlayButton";function Eo({tooltip:s}){const{icons:t}=Z(),e=O("Google Cast"),i=z("remotePlaybackState"),n=O(Wi(i)),r=`${e} ${n}`,o=(i==="connecting"?t.GoogleCastButton.Connecting:i==="connected"?t.GoogleCastButton.Connected:null)??t.GoogleCastButton.Default;return a.createElement(ge,{content:e,placement:s},a.createElement(ih,{className:"vds-google-cast-button vds-button","aria-label":r},a.createElement(o,{className:"vds-icon"})))}Eo.displayName="DefaultGoogleCastButton";function $o(){const s=z("live"),t=O("Skip To Live"),e=O("LIVE");return s?a.createElement(Fu,{className:"vds-live-button","aria-label":t},a.createElement("span",{className:"vds-live-button-text"},e)):null}$o.displayName="DefaultLiveButton";function Sr(){const{download:s,icons:t}=Z(),e=z("source"),i=z("title"),n=wp({title:i,src:e,download:s}),r=O("Download");return n?a.createElement(ge,{content:r,placement:"top"},a.createElement("a",{role:"button",className:"vds-download-button vds-button","aria-label":r,href:n.url+`?download=${n.name}`,download:n.name,target:"_blank"},t.DownloadButton?a.createElement(t.DownloadButton.Default,{className:"vds-icon"}):null)):null}Sr.displayName="DefaultDownloadButton";function xr(){const s=O("Captions look like this");return a.createElement(mo,{className:"vds-captions",exampleText:s})}xr.displayName="DefaultCaptions";function zt(){return a.createElement("div",{className:"vds-controls-spacer"})}zt.displayName="DefaultControlsSpacer";function To({tooltip:s,placement:t,portalClass:e=""}){const{showMenuDelay:i,noModal:n,isSmallLayout:r,icons:o,menuGroup:l,colorScheme:c}=Z(),u=O("Chapters"),h=w2(),d=!h.length,{thumbnails:f}=Z(),p=z("currentSrc"),g=z("viewType"),b=!r&&l==="bottom"&&g==="video"?26:0,y=rt(O2),w=yo(c),[S,N]=a.useState(!1);if(d)return null;function j(){Un.flushSync(()=>{N(!0)})}function V(){N(!1)}const K=a.createElement(Fe,{className:"vds-chapters-menu-items vds-menu-items",placement:t,offset:b},S?a.createElement(Xi,{className:"vds-chapters-radio-group vds-radio-group",value:h.selectedValue,"data-thumbnails":f?"":null},h.map(({cue:ot,label:C,value:k,startTimeText:_,durationText:St,select:gt,setProgressVar:ct})=>a.createElement(Ji,{className:"vds-chapter-radio vds-radio",value:k,key:k,onSelect:gt,ref:ct},f?a.createElement(ju,{src:f,className:"vds-thumbnail",time:ot.startTime},a.createElement(ho,null)):y&&Lu(p)?a.createElement(y,{className:"vds-thumbnail",frame:ot.startTime*p.fps}):null,a.createElement("div",{className:"vds-chapter-radio-content"},a.createElement("span",{className:"vds-chapter-radio-label"},C),a.createElement("span",{className:"vds-chapter-radio-start-time"},_),a.createElement("span",{className:"vds-chapter-radio-duration"},St))))):null);return a.createElement(_e,{className:"vds-chapters-menu vds-menu",showDelay:i,onOpen:j,onClose:V},a.createElement(ge,{content:u,placement:s},a.createElement(wr,{className:"vds-menu-button vds-button",disabled:d,"aria-label":u},a.createElement(o.Menu.Chapters,{className:"vds-icon"}))),n||!r?K:a.createElement(fo,{className:e+(w?` ${w}`:""),disabled:"fullscreen","data-sm":r?"":null,"data-lg":r?null:"","data-size":r?"sm":"lg"},K))}To.displayName="DefaultChaptersMenu";function de({label:s,value:t,children:e}){const i=a.useId();return s?a.createElement("section",{className:"vds-menu-section",role:"group","aria-labelledby":i},a.createElement("div",{className:"vds-menu-section-title"},a.createElement("header",{id:i},s),t?a.createElement("div",{className:"vds-menu-section-value"},t):null),a.createElement("div",{className:"vds-menu-section-body"},e)):a.createElement("div",{className:"vds-menu-section"},a.createElement("div",{className:"vds-menu-section-body"},e))}de.displayName="DefaultMenuSection";function Cs({label:s,hint:t="",Icon:e,disabled:i=!1}){const{icons:n}=a.useContext(Ci);return a.createElement(wr,{className:"vds-menu-item",disabled:i},a.createElement(n.Menu.ArrowLeft,{className:"vds-menu-close-icon vds-icon"}),e?a.createElement(e,{className:"vds-menu-item-icon vds-icon"}):null,a.createElement("span",{className:"vds-menu-item-label"},s),a.createElement("span",{className:"vds-menu-item-hint"},t),a.createElement(n.Menu.ArrowRight,{className:"vds-menu-open-icon vds-icon"}))}Cs.displayName="DefaultMenuButton";function wi({label:s,children:t}){return a.createElement("div",{className:"vds-menu-item"},a.createElement("div",{className:"vds-menu-item-label"},s),t)}wi.displayName="DefaultMenuItem";function Eh({value:s,options:t,onChange:e}){const{icons:i}=Z();return a.createElement(Xi,{className:"vds-radio-group",value:s,onChange:e},t.map(n=>a.createElement(Ji,{className:"vds-radio",value:n.value,key:n.value},a.createElement(i.Menu.RadioCheck,{className:"vds-icon"}),a.createElement("span",{className:"vds-radio-label","data-part":"label"},n.label))))}Eh.displayName="DefaultMenuRadioGroup";function V2(s){return a.useMemo(()=>xt(s)?s.map(t=>({label:t,value:t.toLowerCase()})):Object.keys(s).map(t=>({label:t,value:s[t]})),[s])}function en({label:s,value:t,UpIcon:e,DownIcon:i,children:n,isMin:r,isMax:o}){const l=s||t,c=a.createElement(a.Fragment,null,i?a.createElement(i,{className:"vds-icon down"}):null,n,e?a.createElement(e,{className:"vds-icon up"}):null);return a.createElement("div",{className:`vds-menu-item vds-menu-slider-item${l?" group":""}`,"data-min":r?"":null,"data-max":o?"":null},l?a.createElement(a.Fragment,null,a.createElement("div",{className:"vds-menu-slider-title"},s?a.createElement("div",null,s):null,t?a.createElement("div",null,t):null),a.createElement("div",{className:"vds-menu-slider-body"},c)):c)}en.displayName="DefaultMenuSliderItem";function sn(){return a.createElement(a.Fragment,null,a.createElement(yr,{className:"vds-slider-track"}),a.createElement(vr,{className:"vds-slider-track-fill vds-slider-track"}),a.createElement(br,{className:"vds-slider-thumb"}))}sn.displayName="DefaultSliderParts";function nn(){return a.createElement(qu,{className:"vds-slider-steps"},s=>a.createElement("div",{className:"vds-slider-step",key:String(s)}))}nn.displayName="DefaultSliderSteps";const So={type:"color"},_2={type:"radio",values:{"Monospaced Serif":"mono-serif","Proportional Serif":"pro-serif","Monospaced Sans-Serif":"mono-sans","Proportional Sans-Serif":"pro-sans",Casual:"casual",Cursive:"cursive","Small Capitals":"capitals"}},F2={type:"slider",min:0,max:400,step:25},xo={type:"slider",min:0,max:100,step:5},B2={type:"radio",values:["None","Drop Shadow","Raised","Depressed","Outline"]},kr=a.createContext({all:new Set});kr.displayName="FontResetContext";function $h(){const s=O("Caption Styles"),t=z("hasCaptions"),e=a.useMemo(()=>({all:new Set}),[]),i=O("Font"),n=O("Text"),r=O("Text Background"),o=O("Display Background");return t?a.createElement(kr.Provider,{value:e},a.createElement(_e,{className:"vds-font-menu vds-menu"},a.createElement(Cs,{label:s}),a.createElement(Fe,{className:"vds-font-style-items vds-menu-items"},a.createElement(de,{label:i},a.createElement(Th,null),a.createElement(Sh,null)),a.createElement(de,{label:n},a.createElement(xh,null),a.createElement(Ph,null),a.createElement(kh,null)),a.createElement(de,{label:r},a.createElement(Ah,null),a.createElement(Mh,null)),a.createElement(de,{label:o},a.createElement(Lh,null),a.createElement(Nh,null)),a.createElement(de,null,a.createElement(Ih,null))))):null}$h.displayName="DefaultFontMenu";function Th(){return a.createElement(Be,{group:"font",label:"Family",option:_2,defaultValue:"pro-sans",cssVarName:"font-family",getCssVarValue:q2})}function q2(s,t){var i;const e=s==="capitals"?"small-caps":"";return(i=t.el)==null||i.style.setProperty("--media-user-font-variant",e),H2(s)}Th.displayName="DefaultFontFamilyMenu";function Sh(){const{icons:s}=Z();return a.createElement(Be,{group:"font",label:"Size",option:{...F2,UpIcon:s.Menu.FontSizeUp,DownIcon:s.Menu.FontSizeDown},defaultValue:"100%",cssVarName:"font-size",getCssVarValue:Pr})}Sh.displayName="DefaultFontSizeSlider";function xh(){return a.createElement(Be,{group:"text",label:"Color",option:So,defaultValue:"#ffffff",cssVarName:"text-color",getCssVarValue:s=>`rgb(${ko(s)} / var(--media-user-text-opacity, 1))`})}xh.displayName="DefaultTextColorInput";function kh(){const{icons:s}=Z();return a.createElement(Be,{group:"text",label:"Opacity",option:{...xo,UpIcon:s.Menu.OpacityUp,DownIcon:s.Menu.OpacityDown},defaultValue:"100%",cssVarName:"text-opacity",getCssVarValue:Pr})}kh.displayName="DefaultTextOpacitySlider";function Ph(){return a.createElement(Be,{group:"text",label:"Shadow",option:B2,defaultValue:"none",cssVarName:"text-shadow",getCssVarValue:j2})}Ph.displayName="DefaultTextShadowMenu";function Ah(){return a.createElement(Be,{group:"text-bg",label:"Color",option:So,defaultValue:"#000000",cssVarName:"text-bg",getCssVarValue:s=>`rgb(${ko(s)} / var(--media-user-text-bg-opacity, 1))`})}Ah.displayName="DefaultTextBgInput";function Mh(){const{icons:s}=Z();return a.createElement(Be,{group:"text-bg",label:"Opacity",option:{...xo,UpIcon:s.Menu.OpacityUp,DownIcon:s.Menu.OpacityDown},defaultValue:"100%",cssVarName:"text-bg-opacity",getCssVarValue:Pr})}Mh.displayName="DefaultTextBgOpacitySlider";function Lh(){return a.createElement(Be,{group:"display-bg",label:"Color",option:So,defaultValue:"#000000",cssVarName:"display-bg",getCssVarValue:s=>`rgb(${ko(s)} / var(--media-user-display-bg-opacity, 1))`})}Lh.displayName="DefaultDisplayBgInput";function Nh(){const{icons:s}=Z();return a.createElement(Be,{group:"display-bg",label:"Opacity",option:{...xo,UpIcon:s.Menu.OpacityUp,DownIcon:s.Menu.OpacityDown},defaultValue:"0%",cssVarName:"display-bg-opacity",getCssVarValue:Pr})}Nh.displayName="DefaultDisplayBgOpacitySlider";function Be({group:s,label:t,option:e,cssVarName:i,getCssVarValue:n,defaultValue:r}){const o=Xu(),l=`${s}-${t.toLowerCase()}`,c=O(t),u=a.useContext(kr),[h,d]=a.useState(r),f=a.useCallback(y=>{var w;d(y),localStorage.setItem(`vds-player:${l}`,y),(w=o==null?void 0:o.el)==null||w.style.setProperty(`--media-user-${i}`,(n==null?void 0:n(y,o))??y)},[o]),p=a.useCallback(()=>{o==null||o.dispatchEvent(new Event("vds-font-change"))},[o]),g=a.useCallback(y=>{f(y),p()},[f,p]),b=a.useCallback(()=>{g(r)},[g]);if(a.useEffect(()=>{const y=localStorage.getItem(`vds-player:${l}`);y&&f(y)},[]),a.useEffect(()=>(u.all.add(b),()=>void u.all.delete(b)),[b]),e.type==="color"){let y=function(w){g(w.target.value)};return a.createElement(wi,{label:c},a.createElement("input",{className:"vds-color-picker",type:"color",value:h,onChange:y}))}if(e.type==="slider"){let y=function(K){g(K+"%")};const{min:w,max:S,step:N,UpIcon:j,DownIcon:V}=e;return a.createElement(en,{label:c,value:h,UpIcon:j,DownIcon:V,isMin:h===w+"%",isMax:h===S+"%"},a.createElement(Bu,{className:"vds-slider",min:w,max:S,step:N,keyStep:N,value:parseInt(h),"aria-label":c,onValueChange:y,onDragValueChange:y},a.createElement(sn,null),a.createElement(nn,null)))}return e.type==="radio"?a.createElement(Dh,{id:l,label:c,value:h,values:e.values,onChange:g}):null}Be.displayName="DefaultFontSetting";function Dh({id:s,label:t,value:e,values:i,onChange:n}){const r=V2(i),{translations:o}=Z(),l=a.useMemo(()=>{var u;const c=((u=r.find(h=>h.value===e))==null?void 0:u.label)||"";return bo(o,c)},[e,r]);return a.createElement(_e,{className:`vds-${s}-menu vds-menu`},a.createElement(Cs,{label:t,hint:l}),a.createElement(Fe,{className:"vds-menu-items"},a.createElement(Eh,{value:e,options:r,onChange:n})))}Dh.displayName="DefaultFontRadioGroup";function Ih(){const s=O("Reset"),t=a.useContext(kr);function e(){t.all.forEach(i=>i())}return a.createElement("button",{className:"vds-menu-item",role:"menuitem",onClick:e},a.createElement("span",{className:"vds-menu-item-label"},s))}Ih.displayName="DefaultResetMenuItem";function Pr(s){return(parseInt(s)/100).toString()}function ko(s){const{style:t}=new Option;return t.color=s,t.color.match(/\((.*?)\)/)[1].replace(/,/g," ")}function H2(s){switch(s){case"mono-serif":return'"Courier New", Courier, "Nimbus Mono L", "Cutive Mono", monospace';case"mono-sans":return'"Deja Vu Sans Mono", "Lucida Console", Monaco, Consolas, "PT Mono", monospace';case"pro-sans":return'Roboto, "Arial Unicode Ms", Arial, Helvetica, Verdana, "PT Sans Caption", sans-serif';case"casual":return'"Comic Sans MS", Impact, Handlee, fantasy';case"cursive":return'"Monotype Corsiva", "URW Chancery L", "Apple Chancery", "Dancing Script", cursive';case"capitals":return'"Arial Unicode Ms", Arial, Helvetica, Verdana, "Marcellus SC", sans-serif + font-variant=small-caps';default:return'"Times New Roman", Times, Georgia, Cambria, "PT Serif Caption", serif'}}function j2(s){switch(s){case"drop shadow":return"rgb(34, 34, 34) 1.86389px 1.86389px 2.79583px, rgb(34, 34, 34) 1.86389px 1.86389px 3.72778px, rgb(34, 34, 34) 1.86389px 1.86389px 4.65972px";case"raised":return"rgb(34, 34, 34) 1px 1px, rgb(34, 34, 34) 2px 2px";case"depressed":return"rgb(204, 204, 204) 1px 1px, rgb(34, 34, 34) -1px -1px";case"outline":return"rgb(34, 34, 34) 0px 0px 1.86389px, rgb(34, 34, 34) 0px 0px 1.86389px, rgb(34, 34, 34) 0px 0px 1.86389px, rgb(34, 34, 34) 0px 0px 1.86389px, rgb(34, 34, 34) 0px 0px 1.86389px";default:return""}}function rn({label:s,checked:t,storageKey:e,defaultChecked:i=!1,onChange:n}){const[r,o]=a.useState(i),[l,c]=a.useState(!1);a.useEffect(()=>{const f=e?localStorage.getItem(e):null,p=!!(f??i);o(p),n==null||n(p)},[]),a.useEffect(()=>{Oi(t)&&o(t)},[t]);function u(f){if(f&&"button"in f&&(f==null?void 0:f.button)===1)return;const p=!r;o(p),e&&localStorage.setItem(e,p?"1":""),n==null||n(p,f==null?void 0:f.nativeEvent),c(!1)}function h(f){f.button===0&&c(!0)}function d(f){xa(f.nativeEvent)&&u()}return a.createElement("div",{className:"vds-menu-checkbox",role:"menuitemcheckbox",tabIndex:0,"aria-label":s,"aria-checked":r?"true":"false","data-active":l?"":null,onPointerUp:u,onPointerDown:h,onKeyDown:d})}rn.displayName="DefaultMenuCheckbox";function Oh(){const s=O("Accessibility"),{icons:t}=Z();return a.createElement(_e,{className:"vds-accessibility-menu vds-menu"},a.createElement(Cs,{label:s,Icon:t.Menu.Accessibility}),a.createElement(Fe,{className:"vds-menu-items"},a.createElement(de,null,a.createElement(Rh,null),a.createElement(Vh,null)),a.createElement(de,null,a.createElement($h,null))))}Oh.displayName="DefaultAccessibilityMenu";function Rh(){const{userPrefersAnnouncements:s}=Z(),t=O("Announcements");function e(i){s.set(i)}return a.createElement(wi,{label:t},a.createElement(rn,{label:t,defaultChecked:!0,storageKey:"vds-player::announcements",onChange:e}))}Rh.displayName="DefaultAnnouncementsMenuCheckbox";function Vh(){const s=z("viewType"),{userPrefersKeyboardAnimations:t,noKeyboardAnimations:e}=Z(),i=O("Keyboard Animations");if(s!=="video"||e)return null;function n(r){t.set(r)}return a.createElement(wi,{label:i},a.createElement(rn,{label:i,defaultChecked:!0,storageKey:"vds-player::keyboard-animations",onChange:n}))}Vh.displayName="DefaultKeyboardAnimationsMenuCheckbox";function _h(){const s=O("Audio"),t=z("canSetAudioGain"),e=z("audioTracks"),{noAudioGain:i,icons:n}=Z(),r=t&&!i;return!r&&e.length<=1?null:a.createElement(_e,{className:"vds-audio-menu vds-menu"},a.createElement(Cs,{label:s,Icon:n.Menu.Audio}),a.createElement(Fe,{className:"vds-menu-items"},a.createElement(jh,null),r?a.createElement(Fh,null):null))}_h.displayName="DefaultAudioMenu";function Fh(){const s=z("audioGain"),t=O("Boost"),e=Math.round(((s??1)-1)*100)+"%",i=z("canSetAudioGain"),{noAudioGain:n,icons:r}=Z(),o=!i||n,l=Bh(),c=qh();return o?null:a.createElement(de,{label:t,value:e},a.createElement(en,{UpIcon:r.Menu.AudioBoostUp,DownIcon:r.Menu.AudioBoostDown,isMin:((s??1)-1)*100<=l,isMax:((s??1)-1)*100===c},a.createElement(Hh,null)))}Fh.displayName="DefaultAudioBoostMenuSection";function Bh(){const{audioGains:s}=Z();return(xt(s)?s[0]:s==null?void 0:s.min)??0}function qh(){const{audioGains:s}=Z();return(xt(s)?s[s.length-1]:s==null?void 0:s.max)??300}function G2(){const{audioGains:s}=Z();return(xt(s)?s[1]-s[0]:s==null?void 0:s.step)||25}function Hh(){const s=O("Audio Boost"),t=Bh(),e=qh(),i=G2();return a.createElement(rh,{className:"vds-audio-gain-slider vds-slider","aria-label":s,min:t,max:e,step:i,keyStep:i},a.createElement(sn,null),a.createElement(nn,null))}Hh.displayName="DefaultAudioGainSlider";function jh(){const{icons:s}=Z(),t=O("Track"),e=O("Default"),i=z("audioTrack"),n=i2();return n.disabled?null:a.createElement(_e,{className:"vds-audio-track-menu vds-menu"},a.createElement(Cs,{label:t,hint:(i==null?void 0:i.label)??e,disabled:n.disabled,Icon:s.Menu.Audio}),a.createElement(Fe,{className:"vds-menu-items"},a.createElement(Xi,{className:"vds-audio-radio-group vds-radio-group",value:n.selectedValue},n.map(({label:r,value:o,select:l})=>a.createElement(Ji,{className:"vds-audio-radio vds-radio",value:o,onSelect:l,key:o},a.createElement(s.Menu.RadioCheck,{className:"vds-icon"}),a.createElement("span",{className:"vds-radio-label"},r))))))}jh.displayName="DefaultAudioTracksMenu";function Gh(){var r;const{icons:s}=Z(),t=O("Captions"),e=O("Off"),i=n2({off:e}),n=((r=i.selectedTrack)==null?void 0:r.label)??e;return i.disabled?null:a.createElement(_e,{className:"vds-captions-menu vds-menu"},a.createElement(Cs,{label:t,hint:n,disabled:i.disabled,Icon:s.Menu.Captions}),a.createElement(Fe,{className:"vds-menu-items"},a.createElement(Xi,{className:"vds-captions-radio-group vds-radio-group",value:i.selectedValue},i.map(({label:o,value:l,select:c})=>a.createElement(Ji,{className:"vds-caption-radio vds-radio",value:l,onSelect:c,key:l},a.createElement(s.Menu.RadioCheck,{className:"vds-icon"}),a.createElement("span",{className:"vds-radio-label"},o))))))}Gh.displayName="DefaultCaptionMenu";function Wh(){const s=O("Playback"),{icons:t}=Z();return a.createElement(_e,{className:"vds-accessibility-menu vds-menu"},a.createElement(Cs,{label:s,Icon:t.Menu.Playback}),a.createElement(Fe,{className:"vds-menu-items"},a.createElement(de,null,a.createElement(Uh,null)),a.createElement(W2,null),a.createElement(Zh,null)))}Wh.displayName="DefaultPlaybackMenu";function Uh(){const{remote:s}=Ve(),t=O("Loop");function e(i,n){s.userPrefersLoopChange(i,n)}return a.createElement(wi,{label:t},a.createElement(rn,{label:t,storageKey:"vds-player::user-loop",onChange:e}))}Uh.displayName="DefaultLoopMenuCheckbox";function zh(){const{remote:s,qualities:t}=Ve(),e=z("autoQuality"),i=O("Auto");function n(r,o){r?s.requestAutoQuality(o):s.changeQuality(t.selectedIndex,o)}return a.createElement(wi,{label:i},a.createElement(rn,{label:i,checked:e,onChange:n,defaultChecked:e}))}zh.displayName="DefaultAutoQualityMenuCheckbox";function Zh(){const{hideQualityBitrate:s,icons:t}=Z(),e=z("canSetQuality"),i=z("qualities"),n=z("quality"),r=O("Quality"),o=O("Auto"),l=a.useMemo(()=>Ha(i),[i]);if(!e||i.length<=1)return null;const c=n==null?void 0:n.height,u=s?null:n==null?void 0:n.bitrate,h=u&&u>0?`${(u/1e6).toFixed(2)} Mbps`:null,d=c?`${c}p${h?` (${h})`:""}`:o,f=l[0]===n,p=l.at(-1)===n;return a.createElement(de,{label:r,value:d},a.createElement(en,{UpIcon:t.Menu.QualityUp,DownIcon:t.Menu.QualityDown,isMin:f,isMax:p},a.createElement(Kh,null)),a.createElement(zh,null))}Zh.displayName="DefaultQualityMenuSection";function Kh(){const s=O("Quality");return a.createElement(nh,{className:"vds-quality-slider vds-slider","aria-label":s},a.createElement(sn,null),a.createElement(nn,null))}Kh.displayName="DefaultQualitySlider";function W2(){const{icons:s}=Z(),t=z("playbackRate"),e=z("canSetPlaybackRate"),i=O("Speed"),n=O("Normal"),r=Qh(),o=Yh(),l=t===1?n:t+"x";return e?a.createElement(de,{label:i,value:l},a.createElement(en,{UpIcon:s.Menu.SpeedUp,DownIcon:s.Menu.SpeedDown,isMin:t===r,isMax:t===o},a.createElement(Xh,null))):null}function Qh(){const{playbackRates:s}=Z(),t=s;return(xt(t)?t[0]:t==null?void 0:t.min)??0}function Yh(){const{playbackRates:s}=Z(),t=s;return(xt(t)?t[t.length-1]:t==null?void 0:t.max)??2}function U2(){const{playbackRates:s}=Z(),t=s;return(xt(t)?t[1]-t[0]:t==null?void 0:t.step)||.25}function Xh(){const s=O("Speed"),t=Qh(),e=Yh(),i=U2();return a.createElement(ah,{className:"vds-speed-slider vds-slider","aria-label":s,min:t,max:e,step:i,keyStep:i},a.createElement(sn,null),a.createElement(nn,null))}Xh.displayName="DefaultSpeedSlider";function Po({tooltip:s,placement:t,portalClass:e="",slots:i}){const{showMenuDelay:n,icons:r,isSmallLayout:o,menuGroup:l,noModal:c,colorScheme:u}=Z(),h=O("Settings"),d=z("viewType"),f=!o&&l==="bottom"&&d==="video"?26:0,p=yo(u),[g,b]=a.useState(!1);function y(){Un.flushSync(()=>{b(!0)})}function w(){b(!1)}const S=a.createElement(Fe,{className:"vds-settings-menu-items vds-menu-items",placement:t,offset:f},g?a.createElement(a.Fragment,null,A(i,"settingsMenuStartItems",null),a.createElement(Wh,null),a.createElement(Oh,null),a.createElement(_h,null),a.createElement(Gh,null),A(i,"settingsMenuEndItems",null)):null);return a.createElement(_e,{className:"vds-settings-menu vds-menu",showDelay:n,onOpen:y,onClose:w},a.createElement(ge,{content:h,placement:s},a.createElement(wr,{className:"vds-menu-button vds-button","aria-label":h},a.createElement(r.Menu.Settings,{className:"vds-icon vds-rotate-icon"}))),c||!o?S:a.createElement(fo,{className:e+(p?` ${p}`:""),disabled:"fullscreen","data-sm":o?"":null,"data-lg":o?null:"","data-size":o?"sm":"lg","data-view-type":d},S))}Po.displayName="DefaultSettingsMenu";function Ar({tooltip:s,orientation:t,slots:e}){const i=z("pointer"),n=z("muted"),r=z("canSetVolume"),[o,l]=a.useState(null),c=D2(o),u=A(e,"muteButton",a.createElement(Ch,{tooltip:s}));return r?i==="coarse"&&!n?null:a.createElement("div",{className:"vds-volume","data-active":c?"":null,ref:l},u,a.createElement("div",{className:"vds-volume-popup"},A(e,"volumeSlider",a.createElement(Jh,{orientation:t})))):u}Ar.displayName="DefaultVolumePopup";function Jh(s){const t=O("Volume");return a.createElement(Hu,{className:"vds-volume-slider vds-slider","aria-label":t,...s},a.createElement(yr,{className:"vds-slider-track"}),a.createElement(vr,{className:"vds-slider-track-fill vds-slider-track"}),a.createElement(br,{className:"vds-slider-thumb"}),a.createElement(co,{className:"vds-slider-preview",noClamp:!0},a.createElement(uo,{className:"vds-slider-value"})))}Jh.displayName="DefaultVolumeSlider";function Mr(){const[s,t]=a.useState(null),[e,i]=a.useState(0),n=z("currentSrc"),{thumbnails:r,sliderChaptersMinWidth:o,disableTimeSlider:l,seekStep:c,noScrubGesture:u}=Z(),h=O("Seek"),d=rt(R2),f=a.useCallback(()=>{const p=s==null?void 0:s.el;p&&i(p.clientWidth)},[s]);return gh(s==null?void 0:s.el,f),a.createElement(Gu,{className:"vds-time-slider vds-slider","aria-label":h,disabled:l,noSwipeGesture:u,keyStep:c,ref:t},a.createElement(Wu,{className:"vds-slider-chapters",disabled:e<o},(p,g)=>p.map(b=>a.createElement("div",{className:"vds-slider-chapter",key:b.startTime,ref:g},a.createElement(yr,{className:"vds-slider-track"}),a.createElement(vr,{className:"vds-slider-track-fill vds-slider-track"}),a.createElement(Zu,{className:"vds-slider-progress vds-slider-track"})))),a.createElement(br,{className:"vds-slider-thumb"}),a.createElement(co,{className:"vds-slider-preview"},r?a.createElement(Ll.Root,{src:r,className:"vds-slider-thumbnail vds-thumbnail"},a.createElement(Ll.Img,null)):d&&Lu(n)?a.createElement(d,{className:"vds-slider-thumbnail vds-thumbnail"}):null,a.createElement(zu,{className:"vds-slider-chapter-title"}),a.createElement(uo,{className:"vds-slider-value"})))}Mr.displayName="DefaultTimeSlider";function td({slots:s}){return z("duration")?a.createElement("div",{className:"vds-time-group"},A(s,"currentTime",a.createElement(Bi,{className:"vds-time",type:"current"})),A(s,"timeSeparator",a.createElement("div",{className:"vds-time-divider"},"/")),A(s,"endTime",a.createElement(Bi,{className:"vds-time",type:"duration"}))):null}td.displayName="DefaultTimeGroup";function Ao({slots:s}){return z("live")?A(s,"liveButton",a.createElement($o,null)):a.createElement(td,{slots:s})}Ao.displayName="DefaultTimeInfo";function ed({slots:s}){const t=z("live"),e=z("duration");return t?A(s,"liveButton",a.createElement($o,null)):A(s,"endTime",e?a.createElement(Bi,{className:"vds-time",type:"current",toggle:!0,remainder:!0}):null)}ed.displayName="DefaultTimeInvert";yh({type:"audio",smLayoutWhen({width:s}){return s<576},renderLayout:()=>a.createElement(sd,null)});function sd(){const s=vh();return bh("audio"),a.createElement(a.Fragment,null,a.createElement($r,null),a.createElement(xr,null),a.createElement(Er,{className:"vds-controls"},a.createElement(De,{className:"vds-controls-group"},A(s,"seekBackwardButton",a.createElement(fa,{backward:!0,tooltip:"top start"})),A(s,"playButton",a.createElement(tn,{tooltip:"top center"})),A(s,"seekForwardButton",a.createElement(fa,{tooltip:"top center"})),a.createElement(nd,null),A(s,"timeSlider",a.createElement(Mr,null)),a.createElement(ed,null),a.createElement(Ar,{orientation:"vertical",tooltip:"top",slots:s}),A(s,"captionButton",a.createElement(Tr,{tooltip:"top center"})),A(s,"downloadButton",a.createElement(Sr,null)),a.createElement(id,{slots:s}))))}sd.displayName="AudioLayout";function id({slots:s}){const{isSmallLayout:t,noModal:e}=Z(),i=e?"top end":t?null:"top end";return a.createElement(a.Fragment,null,A(s,"chaptersMenu",a.createElement(To,{tooltip:"top",placement:i,portalClass:"vds-audio-layout"})),A(s,"settingsMenu",a.createElement(Po,{tooltip:"top end",placement:i,portalClass:"vds-audio-layout",slots:s})))}id.displayName="DefaultAudioMenus";function nd(){const[s,t]=a.useState(null),e=Ve(),{translations:i}=Z(),[n,r]=a.useState(!1),o=Hn(()=>{const{started:p,currentTime:g}=e.$state;return p()||g()>0}),l=rt(Hn(()=>{const{title:p,ended:g}=e.$state;if(!p())return"";const b=g()?"Replay":o()?"Continue":"Play";return`${bo(i,b)}: ${p()}`})),c=ch(),u=rt(o),h=u?c:"",d=M2(s);a.useEffect(()=>{var p;d&&document.activeElement===document.body&&((p=e.player.el)==null||p.focus())},[]);const f=a.useCallback(()=>{const p=s,g=!!p&&!d&&p.clientWidth<p.children[0].clientWidth;p&&Ef(p,"vds-marquee",g),r(g)},[s,d]);return gh(s,f),l?a.createElement("span",{className:"vds-title",title:l,ref:t},a.createElement(pa,{title:l,chapterTitle:h}),n&&!d?a.createElement(pa,{title:l,chapterTitle:h}):null):a.createElement(zt,null)}nd.displayName="DefaultAudioTitle";function pa({title:s,chapterTitle:t}){const e=vh();return a.createElement("span",{className:"vds-title-text"},A(e,"title",s),A(e,"chapterTitle",a.createElement("span",{className:"vds-chapter-title"},t)))}pa.displayName="AudioTitle";const rd=a.forwardRef(({icons:s,...t},e)=>{const[i,n]=a.useState(!1),[r,o]=a.useState(null),[l,c]=a.useState(0),u=z("lastKeyboardAction");a.useEffect(()=>{c(g=>g+1)},[u]);const h=a.useMemo(()=>{const g=u==null?void 0:u.action;return g&&i?Jn(g):null},[i,u]),d=a.useMemo(()=>`vds-kb-action${i?"":" hidden"}${t.className?` ${t.className}`:""}`,[i]),f=Hn(z2),p=rt(f);return v2(()=>{const g=Z2(s);o(()=>g)},[s]),a.useEffect(()=>{n(!!u);const g=setTimeout(()=>n(!1),500);return()=>{n(!1),window.clearTimeout(g)}},[u]),a.createElement(R.div,{...t,className:d,"data-action":h,ref:e},a.createElement("div",{className:"vds-kb-text-wrapper"},a.createElement("div",{className:"vds-kb-text"},p)),a.createElement("div",{className:"vds-kb-bezel",key:l},r?a.createElement("div",{className:"vds-kb-icon"},a.createElement(r,null)):null))});rd.displayName="DefaultKeyboardDisplay";function z2(){var i;const{$state:s}=Ct(Zi),t=(i=s.lastKeyboardAction())==null?void 0:i.action,e=s.audioGain()??1;switch(t){case"toggleMuted":return s.muted()?"0%":Ol(s.volume(),e);case"volumeUp":case"volumeDown":return Ol(s.volume(),e);default:return""}}function Ol(s,t){return`${Math.round(s*t*100)}%`}function Z2(s){var i;const{$state:t}=Ct(Zi);switch((i=t.lastKeyboardAction())==null?void 0:i.action){case"togglePaused":return t.paused()?s.Pause:s.Play;case"toggleMuted":return t.muted()||t.volume()===0?s.Mute:t.volume()>=.5?s.VolumeUp:s.VolumeDown;case"toggleFullscreen":return t.fullscreen()?s.EnterFullscreen:s.ExitFullscreen;case"togglePictureInPicture":return t.pictureInPicture()?s.EnterPiP:s.ExitPiP;case"toggleCaptions":return t.hasCaptions()?t.textTrack()?s.CaptionsOn:s.CaptionsOff:null;case"volumeUp":return s.VolumeUp;case"volumeDown":return s.VolumeDown;case"seekForward":return s.SeekForward;case"seekBackward":return s.SeekBackward;default:return null}}function Mo(){const s=z("started"),t=z("title");return po("chapters")&&(s||!t)?a.createElement(uh,{className:"vds-chapter-title"}):a.createElement(oh,{className:"vds-chapter-title"})}Mo.displayName="DefaultTitle";const K2=yh({type:"video",smLayoutWhen({width:s,height:t}){return s<576||t<380},renderLayout(s){return a.createElement(od,{...s})}});function ad(s){return a.createElement(K2,{...s})}ad.displayName="DefaultVideoLayout";function od({streamType:s,isLoadLayout:t,isSmallLayout:e}){return bh("video"),t?a.createElement(hd,null):s==="unknown"?a.createElement(an,null):e?a.createElement(cd,null):a.createElement(ld,null)}od.displayName="VideoLayout";function ld(){const{menuGroup:s}=Z(),t=vo(),e={...t,...t==null?void 0:t.largeLayout};return a.createElement(a.Fragment,null,a.createElement($r,null),a.createElement(Lo,null),a.createElement(No,null),A(e,"bufferingIndicator",a.createElement(an,null)),A(e,"captions",a.createElement(xr,null)),a.createElement(Er,{className:"vds-controls"},a.createElement(De,{className:"vds-controls-group"},A(e,"topControlsGroupStart",null),a.createElement(zt,null),A(e,"topControlsGroupCenter",null),a.createElement(zt,null),A(e,"topControlsGroupEnd",null),s==="top"&&a.createElement(jn,{slots:e})),a.createElement(zt,null),a.createElement(De,{className:"vds-controls-group"},A(e,"centerControlsGroupStart",null),a.createElement(zt,null),A(e,"centerControlsGroupCenter",null),a.createElement(zt,null),A(e,"centerControlsGroupEnd",null)),a.createElement(zt,null),a.createElement(De,{className:"vds-controls-group"},A(e,"timeSlider",a.createElement(Mr,null))),a.createElement(De,{className:"vds-controls-group"},A(e,"playButton",a.createElement(tn,{tooltip:"top start"})),a.createElement(Ar,{orientation:"horizontal",tooltip:"top",slots:e}),a.createElement(Ao,{slots:e}),A(e,"chapterTitle",a.createElement(Mo,null)),A(e,"captionButton",a.createElement(Tr,{tooltip:"top"})),s==="bottom"&&a.createElement(jn,{slots:e}),A(e,"airPlayButton",a.createElement(wo,{tooltip:"top"})),A(e,"googleCastButton",a.createElement(Eo,{tooltip:"top"})),A(e,"downloadButton",a.createElement(Sr,null)),A(e,"pipButton",a.createElement(wh,{tooltip:"top"})),A(e,"fullscreenButton",a.createElement(Co,{tooltip:"top end"})))))}ld.displayName="DefaultVideoLargeLayout";function cd(){const s=vo(),t={...s,...s==null?void 0:s.smallLayout};return a.createElement(a.Fragment,null,a.createElement($r,null),a.createElement(Lo,null),a.createElement(No,null),A(t,"bufferingIndicator",a.createElement(an,null)),A(t,"captions",a.createElement(xr,null)),a.createElement(Er,{className:"vds-controls"},a.createElement(De,{className:"vds-controls-group"},A(t,"topControlsGroupStart",null),A(t,"airPlayButton",a.createElement(wo,{tooltip:"top start"})),A(t,"googleCastButton",a.createElement(Eo,{tooltip:"top start"})),a.createElement(zt,null),A(t,"topControlsGroupCenter",null),a.createElement(zt,null),A(t,"captionButton",a.createElement(Tr,{tooltip:"bottom"})),A(t,"downloadButton",a.createElement(Sr,null)),a.createElement(jn,{slots:t}),a.createElement(Ar,{orientation:"vertical",tooltip:"bottom end",slots:t}),A(t,"topControlsGroupEnd",null)),a.createElement(zt,null),a.createElement(De,{className:"vds-controls-group",style:{pointerEvents:"none"}},A(t,"centerControlsGroupStart",null),a.createElement(zt,null),A(t,"centerControlsGroupCenter",null),A(t,"playButton",a.createElement(tn,{tooltip:"top"})),a.createElement(zt,null),A(t,"centerControlsGroupEnd",null)),a.createElement(zt,null),a.createElement(De,{className:"vds-controls-group"},a.createElement(Ao,{slots:t}),A(t,"chapterTitle",a.createElement(Mo,null)),A(t,"fullscreenButton",a.createElement(Co,{tooltip:"top end"}))),a.createElement(De,{className:"vds-controls-group"},A(t,"timeSlider",a.createElement(Mr,null)))),A(t,"startDuration",a.createElement(ud,null)))}cd.displayName="DefaultVideoSmallLayout";function ud(){return z("duration")===0?null:a.createElement("div",{className:"vds-start-duration"},a.createElement(Bi,{className:"vds-time",type:"duration"}))}ud.displayName="DefaultVideoStartDuration";function Lo(){const{noGestures:s}=Z();return s?null:a.createElement("div",{className:"vds-gestures"},a.createElement(Js,{className:"vds-gesture",event:"pointerup",action:"toggle:paused"}),a.createElement(Js,{className:"vds-gesture",event:"pointerup",action:"toggle:controls"}),a.createElement(Js,{className:"vds-gesture",event:"dblpointerup",action:"toggle:fullscreen"}),a.createElement(Js,{className:"vds-gesture",event:"dblpointerup",action:"seek:-10"}),a.createElement(Js,{className:"vds-gesture",event:"dblpointerup",action:"seek:10"}))}Lo.displayName="DefaultVideoGestures";function an(){return a.createElement("div",{className:"vds-buffering-indicator"},a.createElement(g2,{className:"vds-buffering-spinner"},a.createElement(b2,{className:"vds-buffering-track"}),a.createElement(y2,{className:"vds-buffering-track-fill"})))}an.displayName="DefaultBufferingIndicator";function jn({slots:s}){const{isSmallLayout:t,noModal:e,menuGroup:i}=Z(),n=i==="top"||t?"bottom":"top",r=`${n} end`,o=e?`${n} end`:t?null:`${n} end`;return a.createElement(a.Fragment,null,A(s,"chaptersMenu",a.createElement(To,{tooltip:r,placement:o,portalClass:"vds-video-layout"})),A(s,"settingsMenu",a.createElement(Po,{tooltip:r,placement:o,portalClass:"vds-video-layout",slots:s})))}jn.displayName="DefaultVideoMenus";function hd(){const{isSmallLayout:s}=Z(),t=vo(),e={...t,...t==null?void 0:t[s?"smallLayout":"largeLayout"]};return a.createElement("div",{className:"vds-load-container"},A(e,"bufferingIndicator",a.createElement(an,null)),A(e,"loadButton",a.createElement(tn,{tooltip:"top"})))}hd.displayName="DefaultVideoLoadLayout";function No(){const{noKeyboardAnimations:s,icons:t,userPrefersKeyboardAnimations:e}=Z(),i=rt(e);return s||!i||!t.KeyboardDisplay?null:a.createElement(rd,{icons:t.KeyboardDisplay})}No.displayName="DefaultVideoKeyboardDisplay";var dd='<path fill-rule="evenodd" clip-rule="evenodd" d="M6 7C5.63181 7 5.33333 7.29848 5.33333 7.66667V14.8667C5.33333 14.9403 5.39361 14.9999 5.46724 15.0009C10.8844 15.0719 15.2614 19.449 15.3325 24.8661C15.3334 24.9397 15.393 25 15.4667 25H26C26.3682 25 26.6667 24.7015 26.6667 24.3333V7.66667C26.6667 7.29848 26.3682 7 26 7H6ZM17.0119 22.2294C17.0263 22.29 17.0802 22.3333 17.1425 22.3333H23.3333C23.7015 22.3333 24 22.0349 24 21.6667V10.3333C24 9.96514 23.7015 9.66667 23.3333 9.66667H8.66667C8.29848 9.66667 8 9.96514 8 10.3333V13.1909C8 13.2531 8.04332 13.3071 8.10392 13.3214C12.5063 14.3618 15.9715 17.827 17.0119 22.2294Z" fill="currentColor"/> <path d="M13.2 25C13.2736 25 13.3334 24.9398 13.3322 24.8661C13.2615 20.5544 9.77889 17.0718 5.46718 17.0011C5.39356 16.9999 5.33333 17.0597 5.33333 17.1333V18.8667C5.33333 18.9403 5.39348 18.9999 5.4671 19.0015C8.67465 19.0716 11.2617 21.6587 11.3319 24.8662C11.3335 24.9399 11.393 25 11.4667 25H13.2Z" fill="currentColor"/> <path d="M5.33333 21.1333C5.33333 21.0597 5.39332 20.9998 5.46692 21.0022C7.57033 21.0712 9.26217 22.763 9.33114 24.8664C9.33356 24.94 9.27364 25 9.2 25H6C5.63181 25 5.33333 24.7015 5.33333 24.3333V21.1333Z" fill="currentColor"/>';const Q2=Object.freeze(Object.defineProperty({__proto__:null,default:dd},Symbol.toStringTag,{value:"Module"}));var Y2='<path fill-rule="evenodd" clip-rule="evenodd" d="M15.0007 28.7923C15.0007 29.0152 14.9774 29.096 14.9339 29.1775C14.8903 29.259 14.8263 29.323 14.7449 29.3665C14.6634 29.4101 14.5826 29.4333 14.3597 29.4333H12.575C12.3521 29.4333 12.2713 29.4101 12.1898 29.3665C12.1083 29.323 12.0443 29.259 12.0008 29.1775C11.9572 29.096 11.934 29.0152 11.934 28.7923V12.2993L5.97496 12.3C5.75208 12.3 5.67125 12.2768 5.58977 12.2332C5.50829 12.1896 5.44434 12.1257 5.40077 12.0442C5.35719 11.9627 5.33398 11.8819 5.33398 11.659V9.87429C5.33398 9.65141 5.35719 9.57059 5.40077 9.48911C5.44434 9.40762 5.50829 9.34368 5.58977 9.3001C5.67125 9.25652 5.75208 9.23332 5.97496 9.23332H26.0263C26.2492 9.23332 26.33 9.25652 26.4115 9.3001C26.493 9.34368 26.557 9.40762 26.6005 9.48911C26.6441 9.57059 26.6673 9.65141 26.6673 9.87429V11.659C26.6673 11.8819 26.6441 11.9627 26.6005 12.0442C26.557 12.1257 26.493 12.1896 26.4115 12.2332C26.33 12.2768 26.2492 12.3 26.0263 12.3L20.067 12.2993L20.0673 28.7923C20.0673 29.0152 20.0441 29.096 20.0005 29.1775C19.957 29.259 19.893 29.323 19.8115 29.3665C19.73 29.4101 19.6492 29.4333 19.4263 29.4333H17.6416C17.4187 29.4333 17.3379 29.4101 17.2564 29.3665C17.175 29.323 17.111 29.259 17.0674 29.1775C17.0239 29.096 17.0007 29.0152 17.0007 28.7923L17 22.7663H15L15.0007 28.7923Z" fill="currentColor"/> <path d="M16.0007 7.89998C17.4734 7.89998 18.6673 6.70608 18.6673 5.23332C18.6673 3.76056 17.4734 2.56665 16.0007 2.56665C14.5279 2.56665 13.334 3.76056 13.334 5.23332C13.334 6.70608 14.5279 7.89998 16.0007 7.89998Z" fill="currentColor"/>',X2='<path d="M5.33334 6.00001C5.33334 5.63182 5.63181 5.33334 6 5.33334H26C26.3682 5.33334 26.6667 5.63182 26.6667 6.00001V20.6667C26.6667 21.0349 26.3682 21.3333 26 21.3333H23.7072C23.4956 21.3333 23.2966 21.233 23.171 21.0628L22.1859 19.7295C21.8607 19.2894 22.1749 18.6667 22.7221 18.6667H23.3333C23.7015 18.6667 24 18.3682 24 18V8.66668C24 8.29849 23.7015 8.00001 23.3333 8.00001H8.66667C8.29848 8.00001 8 8.29849 8 8.66668V18C8 18.3682 8.29848 18.6667 8.66667 18.6667H9.29357C9.84072 18.6667 10.1549 19.2894 9.82976 19.7295L8.84467 21.0628C8.71898 21.233 8.52 21.3333 8.30848 21.3333H6C5.63181 21.3333 5.33334 21.0349 5.33334 20.6667V6.00001Z" fill="currentColor"/> <path d="M8.78528 25.6038C8.46013 26.0439 8.77431 26.6667 9.32147 26.6667L22.6785 26.6667C23.2256 26.6667 23.5398 26.0439 23.2146 25.6038L16.5358 16.5653C16.2693 16.2046 15.73 16.2047 15.4635 16.5653L8.78528 25.6038Z" fill="currentColor"/>',Rl='<path d="M17.4853 18.9093C17.4853 19.0281 17.6289 19.0875 17.7129 19.0035L22.4185 14.2979C22.6788 14.0376 23.1009 14.0376 23.3613 14.2979L24.7755 15.7122C25.0359 15.9725 25.0359 16.3946 24.7755 16.655L16.2902 25.1403C16.0299 25.4006 15.6078 25.4006 15.3474 25.1403L13.9332 23.726L13.9319 23.7247L6.86189 16.6547C6.60154 16.3944 6.60154 15.9723 6.86189 15.7119L8.2761 14.2977C8.53645 14.0373 8.95856 14.0373 9.21891 14.2977L13.9243 19.0031C14.0083 19.0871 14.1519 19.0276 14.1519 18.9088L14.1519 6.00004C14.1519 5.63185 14.4504 5.33337 14.8186 5.33337L16.8186 5.33337C17.1868 5.33337 17.4853 5.63185 17.4853 6.00004L17.4853 18.9093Z" fill="currentColor"/>',J2='<path d="M13.0908 14.3334C12.972 14.3334 12.9125 14.1898 12.9965 14.1058L17.7021 9.40022C17.9625 9.13987 17.9625 8.71776 17.7021 8.45741L16.2879 7.04319C16.0275 6.78284 15.6054 6.78284 15.3451 7.04319L6.8598 15.5285C6.59945 15.7888 6.59945 16.2109 6.8598 16.4713L8.27401 17.8855L8.27536 17.8868L15.3453 24.9568C15.6057 25.2172 16.0278 25.2172 16.2881 24.9568L17.7024 23.5426C17.9627 23.2822 17.9627 22.8601 17.7024 22.5998L12.9969 17.8944C12.9129 17.8104 12.9724 17.6668 13.0912 17.6668L26 17.6668C26.3682 17.6668 26.6667 17.3683 26.6667 17.0001V15.0001C26.6667 14.6319 26.3682 14.3334 26 14.3334L13.0908 14.3334Z" fill="currentColor"/>',Vl='<path d="M14.1521 13.0929C14.1521 12.9741 14.0085 12.9147 13.9245 12.9987L9.21891 17.7043C8.95856 17.9646 8.53645 17.9646 8.2761 17.7043L6.86189 16.29C6.60154 16.0297 6.60154 15.6076 6.86189 15.3472L15.3472 6.86195C15.6075 6.6016 16.0296 6.6016 16.29 6.86195L17.7042 8.27616L17.7055 8.27751L24.7755 15.3475C25.0359 15.6078 25.0359 16.0299 24.7755 16.2903L23.3613 17.7045C23.1009 17.9649 22.6788 17.9649 22.4185 17.7045L17.7131 12.9991C17.6291 12.9151 17.4855 12.9746 17.4855 13.0934V26.0022C17.4855 26.3704 17.187 26.6688 16.8188 26.6688H14.8188C14.4506 26.6688 14.1521 26.3704 14.1521 26.0022L14.1521 13.0929Z" fill="currentColor"/>',t0='<path d="M16.6927 25.3346C16.3245 25.3346 16.026 25.0361 16.026 24.6679L16.026 7.3346C16.026 6.96641 16.3245 6.66794 16.6927 6.66794L18.6927 6.66794C19.0609 6.66794 19.3594 6.96642 19.3594 7.3346L19.3594 24.6679C19.3594 25.0361 19.0609 25.3346 18.6927 25.3346H16.6927Z" fill="currentColor"/> <path d="M24.026 25.3346C23.6578 25.3346 23.3594 25.0361 23.3594 24.6679L23.3594 7.3346C23.3594 6.96641 23.6578 6.66794 24.026 6.66794L26.026 6.66794C26.3942 6.66794 26.6927 6.96642 26.6927 7.3346V24.6679C26.6927 25.0361 26.3942 25.3346 26.026 25.3346H24.026Z" fill="currentColor"/> <path d="M5.48113 23.9407C5.38584 24.2963 5.59689 24.6619 5.95254 24.7572L7.88439 25.2748C8.24003 25.3701 8.60559 25.159 8.70089 24.8034L13.1871 8.06067C13.2824 7.70503 13.0713 7.33947 12.7157 7.24417L10.7838 6.72654C10.4282 6.63124 10.0626 6.8423 9.96733 7.19794L5.48113 23.9407Z" fill="currentColor"/>',e0='<path fill-rule="evenodd" clip-rule="evenodd" d="M24.9266 7.57992C25.015 7.60672 25.0886 7.64746 25.2462 7.80506L26.956 9.51488C27.1136 9.67248 27.1543 9.74604 27.1811 9.83447C27.2079 9.9229 27.2079 10.0133 27.1811 10.1018C27.1543 10.1902 27.1136 10.2638 26.956 10.4214L13.1822 24.1951C13.0246 24.3527 12.951 24.3935 12.8626 24.4203C12.797 24.4402 12.7304 24.4453 12.6642 24.4357L12.7319 24.4203C12.6435 24.4471 12.553 24.4471 12.4646 24.4203C12.3762 24.3935 12.3026 24.3527 12.145 24.1951L5.04407 17.0942C4.88647 16.9366 4.84573 16.863 4.81893 16.7746C4.79213 16.6862 4.79213 16.5957 4.81893 16.5073C4.84573 16.4189 4.88647 16.3453 5.04407 16.1877L6.7539 14.4779C6.9115 14.3203 6.98506 14.2796 7.07349 14.2528C7.16191 14.226 7.25235 14.226 7.34078 14.2528C7.42921 14.2796 7.50277 14.3203 7.66037 14.4779L12.6628 19.4808L24.3397 7.80506C24.4973 7.64746 24.5709 7.60672 24.6593 7.57992C24.7477 7.55311 24.8382 7.55311 24.9266 7.57992Z" fill="currentColor"/>',s0='<path d="M17.947 16.095C17.999 16.043 17.999 15.9585 17.947 15.9065L11.6295 9.58899C11.3691 9.32864 11.3691 8.90653 11.6295 8.64618L13.2323 7.04341C13.4926 6.78306 13.9147 6.78306 14.1751 7.04341L21.0289 13.8973C21.0392 13.9064 21.0493 13.9158 21.0591 13.9257L22.6619 15.5285C22.9223 15.7888 22.9223 16.2109 22.6619 16.4713L14.1766 24.9565C13.9163 25.2169 13.4942 25.2169 13.2338 24.9565L11.631 23.3538C11.3707 23.0934 11.3707 22.6713 11.631 22.411L17.947 16.095Z" fill="currentColor"/>',_l='<path d="M8 28.0003C8 27.6321 8.29848 27.3336 8.66667 27.3336H23.3333C23.7015 27.3336 24 27.6321 24 28.0003V29.3336C24 29.7018 23.7015 30.0003 23.3333 30.0003H8.66667C8.29848 30.0003 8 29.7018 8 29.3336V28.0003Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M4.66602 6.66699C4.29783 6.66699 3.99935 6.96547 3.99935 7.33366V24.667C3.99935 25.0352 4.29783 25.3337 4.66602 25.3337H27.3327C27.7009 25.3337 27.9994 25.0352 27.9994 24.667V7.33366C27.9994 6.96547 27.7009 6.66699 27.3327 6.66699H4.66602ZM8.66659 21.3333C8.2984 21.3333 7.99992 21.0349 7.99992 20.6667V11.3333C7.99992 10.9651 8.2984 10.6667 8.66659 10.6667H13.9999C14.3681 10.6667 14.6666 10.9651 14.6666 11.3333V12.6667C14.6666 13.0349 14.3681 13.3333 13.9999 13.3333H10.7999C10.7263 13.3333 10.6666 13.393 10.6666 13.4667V18.5333C10.6666 18.607 10.7263 18.6667 10.7999 18.6667H13.9999C14.3681 18.6667 14.6666 18.9651 14.6666 19.3333V20.6667C14.6666 21.0349 14.3681 21.3333 13.9999 21.3333H8.66659ZM17.9999 21.3333C17.6317 21.3333 17.3333 21.0349 17.3333 20.6667V11.3333C17.3333 10.9651 17.6317 10.6667 17.9999 10.6667H23.3333C23.7014 10.6667 23.9999 10.9651 23.9999 11.3333V12.6667C23.9999 13.0349 23.7014 13.3333 23.3333 13.3333H20.1333C20.0596 13.3333 19.9999 13.393 19.9999 13.4667V18.5333C19.9999 18.607 20.0596 18.6667 20.1333 18.6667H23.3333C23.7014 18.6667 23.9999 18.9651 23.9999 19.3333V20.6667C23.9999 21.0349 23.7014 21.3333 23.3333 21.3333H17.9999Z" fill="currentColor"/>',Gr='<path fill-rule="evenodd" clip-rule="evenodd" d="M4.6661 6.66699C4.29791 6.66699 3.99943 6.96547 3.99943 7.33366V24.667C3.99943 25.0352 4.29791 25.3337 4.6661 25.3337H27.3328C27.701 25.3337 27.9994 25.0352 27.9994 24.667V7.33366C27.9994 6.96547 27.701 6.66699 27.3328 6.66699H4.6661ZM8.66667 21.3333C8.29848 21.3333 8 21.0349 8 20.6667V11.3333C8 10.9651 8.29848 10.6667 8.66667 10.6667H14C14.3682 10.6667 14.6667 10.9651 14.6667 11.3333V12.6667C14.6667 13.0349 14.3682 13.3333 14 13.3333H10.8C10.7264 13.3333 10.6667 13.393 10.6667 13.4667V18.5333C10.6667 18.607 10.7264 18.6667 10.8 18.6667H14C14.3682 18.6667 14.6667 18.9651 14.6667 19.3333V20.6667C14.6667 21.0349 14.3682 21.3333 14 21.3333H8.66667ZM18 21.3333C17.6318 21.3333 17.3333 21.0349 17.3333 20.6667V11.3333C17.3333 10.9651 17.6318 10.6667 18 10.6667H23.3333C23.7015 10.6667 24 10.9651 24 11.3333V12.6667C24 13.0349 23.7015 13.3333 23.3333 13.3333H20.1333C20.0597 13.3333 20 13.393 20 13.4667V18.5333C20 18.607 20.0597 18.6667 20.1333 18.6667H23.3333C23.7015 18.6667 24 18.9651 24 19.3333V20.6667C24 21.0349 23.7015 21.3333 23.3333 21.3333H18Z" fill="currentColor"/>',i0='<path d="M14.2225 13.7867C14.3065 13.8706 14.4501 13.8112 14.4501 13.6924V5.99955C14.4501 5.63136 14.7486 5.33289 15.1167 5.33289H16.8501C17.2183 5.33289 17.5167 5.63136 17.5167 5.99955V13.6916C17.5167 13.8104 17.6604 13.8699 17.7444 13.7859L19.9433 11.5869C20.2037 11.3266 20.6258 11.3266 20.8861 11.5869L22.1118 12.8126C22.3722 13.0729 22.3722 13.4951 22.1118 13.7554L16.4549 19.4123C16.1946 19.6726 15.772 19.6731 15.5116 19.4128L9.85479 13.7559C9.59444 13.4956 9.59444 13.0734 9.85479 12.8131L11.0804 11.5874C11.3408 11.3271 11.7629 11.3271 12.0233 11.5874L14.2225 13.7867Z" fill="currentColor"/> <path d="M5.99998 20.267C5.63179 20.267 5.33331 20.5654 5.33331 20.9336V25.9997C5.33331 26.3678 5.63179 26.6663 5.99998 26.6663H26C26.3682 26.6663 26.6666 26.3678 26.6666 25.9997V20.9336C26.6666 20.5654 26.3682 20.267 26 20.267H24.2666C23.8985 20.267 23.6 20.5654 23.6 20.9336V22.9333C23.6 23.3014 23.3015 23.5999 22.9333 23.5999H9.06638C8.69819 23.5999 8.39972 23.3014 8.39972 22.9333V20.9336C8.39972 20.5654 8.10124 20.267 7.73305 20.267H5.99998Z" fill="currentColor"/>',n0='<path d="M16 20C18.2091 20 20 18.2092 20 16C20 13.7909 18.2091 12 16 12C13.7909 12 12 13.7909 12 16C12 18.2092 13.7909 20 16 20Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M28 16.0058C28 18.671 23.5 25.3334 16 25.3334C8.5 25.3334 4 18.6762 4 16.0058C4 13.3354 8.50447 6.66669 16 6.66669C23.4955 6.66669 28 13.3406 28 16.0058ZM25.3318 15.9934C25.3328 16.0017 25.3328 16.0099 25.3318 16.0182C25.3274 16.0571 25.3108 16.1728 25.2485 16.3708C25.1691 16.6229 25.0352 16.9462 24.8327 17.3216C24.4264 18.0749 23.7969 18.9398 22.9567 19.754C21.2791 21.3798 18.9148 22.6667 16 22.6667C13.0845 22.6667 10.7202 21.3805 9.04298 19.7557C8.20295 18.9419 7.57362 18.0773 7.16745 17.3241C6.96499 16.9486 6.83114 16.6252 6.75172 16.3729C6.67942 16.1431 6.66856 16.0243 6.66695 16.0066L6.66695 16.005C6.66859 15.9871 6.67951 15.8682 6.75188 15.6383C6.83145 15.3854 6.96554 15.0614 7.16831 14.6853C7.57507 13.9306 8.20514 13.0644 9.04577 12.249C10.7245 10.6208 13.0886 9.33335 16 9.33335C18.9108 9.33335 21.2748 10.6215 22.9539 12.2507C23.7947 13.0664 24.4249 13.933 24.8318 14.6877C25.0346 15.0639 25.1688 15.3878 25.2483 15.6404C25.3107 15.8386 25.3274 15.9545 25.3318 15.9934Z" fill="currentColor"/>',Fl='<path d="M15.8747 8.11857C16.3148 7.79342 16.9375 8.10759 16.9375 8.65476V14.2575C16.9375 14.3669 17.0621 14.4297 17.1501 14.3647L25.6038 8.11857C26.0439 7.79342 26.6667 8.10759 26.6667 8.65476V23.3451C26.6667 23.8923 26.0439 24.2064 25.6038 23.8813L17.1501 17.6346C17.0621 17.5695 16.9375 17.6324 16.9375 17.7418L16.9375 23.3451C16.9375 23.8923 16.3147 24.2064 15.8747 23.8813L5.93387 16.5358C5.57322 16.2693 5.57323 15.7299 5.93389 15.4634L15.8747 8.11857Z" fill="currentColor"/>',Bl='<path d="M16.1253 8.11866C15.6852 7.7935 15.0625 8.10768 15.0625 8.65484V14.2576C15.0625 14.367 14.9379 14.4298 14.8499 14.3648L6.39615 8.11866C5.95607 7.7935 5.33331 8.10768 5.33331 8.65484V23.3452C5.33331 23.8923 5.9561 24.2065 6.39617 23.8813L14.8499 17.6347C14.9379 17.5696 15.0625 17.6325 15.0625 17.7419L15.0625 23.3452C15.0625 23.8923 15.6853 24.2065 16.1253 23.8813L26.0661 16.5358C26.4268 16.2694 26.4268 15.73 26.0661 15.4635L16.1253 8.11866Z" fill="currentColor"/>',ql='<path d="M19.3334 13.3333C18.9652 13.3333 18.6667 13.0349 18.6667 12.6667L18.6667 7.33333C18.6667 6.96514 18.9652 6.66666 19.3334 6.66666H21.3334C21.7015 6.66666 22 6.96514 22 7.33333V9.86666C22 9.9403 22.0597 10 22.1334 10L24.6667 10C25.0349 10 25.3334 10.2985 25.3334 10.6667V12.6667C25.3334 13.0349 25.0349 13.3333 24.6667 13.3333L19.3334 13.3333Z" fill="currentColor"/> <path d="M13.3334 19.3333C13.3334 18.9651 13.0349 18.6667 12.6667 18.6667H7.33335C6.96516 18.6667 6.66669 18.9651 6.66669 19.3333V21.3333C6.66669 21.7015 6.96516 22 7.33335 22H9.86669C9.94032 22 10 22.0597 10 22.1333L10 24.6667C10 25.0349 10.2985 25.3333 10.6667 25.3333H12.6667C13.0349 25.3333 13.3334 25.0349 13.3334 24.6667L13.3334 19.3333Z" fill="currentColor"/> <path d="M18.6667 24.6667C18.6667 25.0349 18.9652 25.3333 19.3334 25.3333H21.3334C21.7015 25.3333 22 25.0349 22 24.6667V22.1333C22 22.0597 22.0597 22 22.1334 22H24.6667C25.0349 22 25.3334 21.7015 25.3334 21.3333V19.3333C25.3334 18.9651 25.0349 18.6667 24.6667 18.6667L19.3334 18.6667C18.9652 18.6667 18.6667 18.9651 18.6667 19.3333L18.6667 24.6667Z" fill="currentColor"/> <path d="M10.6667 13.3333H12.6667C13.0349 13.3333 13.3334 13.0349 13.3334 12.6667L13.3334 10.6667V7.33333C13.3334 6.96514 13.0349 6.66666 12.6667 6.66666H10.6667C10.2985 6.66666 10 6.96514 10 7.33333L10 9.86666C10 9.9403 9.94033 10 9.86669 10L7.33335 10C6.96516 10 6.66669 10.2985 6.66669 10.6667V12.6667C6.66669 13.0349 6.96516 13.3333 7.33335 13.3333L10.6667 13.3333Z" fill="currentColor"/>',Hl='<path d="M25.3299 7.26517C25.2958 6.929 25.0119 6.66666 24.6667 6.66666H19.3334C18.9652 6.66666 18.6667 6.96514 18.6667 7.33333V9.33333C18.6667 9.70152 18.9652 10 19.3334 10L21.8667 10C21.9403 10 22 10.0597 22 10.1333V12.6667C22 13.0349 22.2985 13.3333 22.6667 13.3333H24.6667C25.0349 13.3333 25.3334 13.0349 25.3334 12.6667V7.33333C25.3334 7.31032 25.3322 7.28758 25.3299 7.26517Z" fill="currentColor"/> <path d="M22 21.8667C22 21.9403 21.9403 22 21.8667 22L19.3334 22C18.9652 22 18.6667 22.2985 18.6667 22.6667V24.6667C18.6667 25.0349 18.9652 25.3333 19.3334 25.3333L24.6667 25.3333C25.0349 25.3333 25.3334 25.0349 25.3334 24.6667V19.3333C25.3334 18.9651 25.0349 18.6667 24.6667 18.6667H22.6667C22.2985 18.6667 22 18.9651 22 19.3333V21.8667Z" fill="currentColor"/> <path d="M12.6667 22H10.1334C10.0597 22 10 21.9403 10 21.8667V19.3333C10 18.9651 9.70154 18.6667 9.33335 18.6667H7.33335C6.96516 18.6667 6.66669 18.9651 6.66669 19.3333V24.6667C6.66669 25.0349 6.96516 25.3333 7.33335 25.3333H12.6667C13.0349 25.3333 13.3334 25.0349 13.3334 24.6667V22.6667C13.3334 22.2985 13.0349 22 12.6667 22Z" fill="currentColor"/> <path d="M10 12.6667V10.1333C10 10.0597 10.0597 10 10.1334 10L12.6667 10C13.0349 10 13.3334 9.70152 13.3334 9.33333V7.33333C13.3334 6.96514 13.0349 6.66666 12.6667 6.66666H7.33335C6.96516 6.66666 6.66669 6.96514 6.66669 7.33333V12.6667C6.66669 13.0349 6.96516 13.3333 7.33335 13.3333H9.33335C9.70154 13.3333 10 13.0349 10 12.6667Z" fill="currentColor"/>',r0='<path fill-rule="evenodd" clip-rule="evenodd" d="M26.6667 5.99998C26.6667 5.63179 26.3682 5.33331 26 5.33331H11.3333C10.9651 5.33331 10.6667 5.63179 10.6667 5.99998V17.5714C10.6667 17.6694 10.5644 17.7342 10.4741 17.6962C9.91823 17.4625 9.30754 17.3333 8.66667 17.3333C6.08934 17.3333 4 19.4226 4 22C4 24.5773 6.08934 26.6666 8.66667 26.6666C11.244 26.6666 13.3333 24.5773 13.3333 22V8.66665C13.3333 8.29846 13.6318 7.99998 14 7.99998L23.3333 7.99998C23.7015 7.99998 24 8.29846 24 8.66665V14.9048C24 15.0027 23.8978 15.0675 23.8075 15.0296C23.2516 14.7958 22.6409 14.6666 22 14.6666C19.4227 14.6666 17.3333 16.756 17.3333 19.3333C17.3333 21.9106 19.4227 24 22 24C24.5773 24 26.6667 21.9106 26.6667 19.3333V5.99998ZM22 21.3333C23.1046 21.3333 24 20.4379 24 19.3333C24 18.2287 23.1046 17.3333 22 17.3333C20.8954 17.3333 20 18.2287 20 19.3333C20 20.4379 20.8954 21.3333 22 21.3333ZM8.66667 24C9.77124 24 10.6667 23.1045 10.6667 22C10.6667 20.8954 9.77124 20 8.66667 20C7.5621 20 6.66667 20.8954 6.66667 22C6.66667 23.1045 7.5621 24 8.66667 24Z" fill="currentColor"/>',jl='<path d="M17.5091 24.6594C17.5091 25.2066 16.8864 25.5208 16.4463 25.1956L9.44847 20.0252C9.42553 20.0083 9.39776 19.9991 9.36923 19.9991H4.66667C4.29848 19.9991 4 19.7006 4 19.3325V12.6658C4 12.2976 4.29848 11.9991 4.66667 11.9991H9.37115C9.39967 11.9991 9.42745 11.99 9.45039 11.973L16.4463 6.8036C16.8863 6.47842 17.5091 6.79259 17.5091 7.33977L17.5091 24.6594Z" fill="currentColor"/> <path d="M28.8621 13.6422C29.1225 13.3818 29.1225 12.9597 28.8621 12.6994L27.9193 11.7566C27.659 11.4962 27.2368 11.4962 26.9765 11.7566L24.7134 14.0197C24.6613 14.0717 24.5769 14.0717 24.5248 14.0197L22.262 11.7568C22.0016 11.4964 21.5795 11.4964 21.3191 11.7568L20.3763 12.6996C20.116 12.9599 20.116 13.382 20.3763 13.6424L22.6392 15.9053C22.6913 15.9573 22.6913 16.0418 22.6392 16.0938L20.3768 18.3562C20.1165 18.6166 20.1165 19.0387 20.3768 19.299L21.3196 20.2419C21.58 20.5022 22.0021 20.5022 22.2624 20.2418L24.5248 17.9795C24.5769 17.9274 24.6613 17.9274 24.7134 17.9795L26.976 20.2421C27.2363 20.5024 27.6585 20.5024 27.9188 20.2421L28.8616 19.2992C29.122 19.0389 29.122 18.6168 28.8616 18.3564L26.599 16.0938C26.547 16.0418 26.547 15.9573 26.599 15.9053L28.8621 13.6422Z" fill="currentColor"/>',a0='<path d="M26.6009 16.0725C26.6009 16.424 26.4302 17.1125 25.9409 18.0213C25.4676 18.8976 24.7542 19.8715 23.8182 20.7783C21.9489 22.5905 19.2662 24.0667 15.9342 24.0667C12.6009 24.0667 9.91958 22.5915 8.04891 20.78C7.11424 19.8736 6.40091 18.9 5.92758 18.0236C5.43824 17.1149 5.26758 16.4257 5.26758 16.0725C5.26758 15.7193 5.43824 15.0293 5.92891 14.1193C6.40224 13.2416 7.11558 12.2665 8.05158 11.3587C9.92224 9.54398 12.6049 8.06665 15.9342 8.06665C19.2636 8.06665 21.9449 9.54505 23.8169 11.3604C24.7529 12.2687 25.4662 13.2441 25.9396 14.1216C26.4302 15.0317 26.6009 15.7209 26.6009 16.0725Z" stroke="currentColor" stroke-width="3"/> <path d="M15.9336 20.0667C18.1427 20.0667 19.9336 18.2758 19.9336 16.0667C19.9336 13.8575 18.1427 12.0667 15.9336 12.0667C13.7245 12.0667 11.9336 13.8575 11.9336 16.0667C11.9336 18.2758 13.7245 20.0667 15.9336 20.0667Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M27.2323 25.0624L6.93878 4.76886C6.78118 4.61126 6.70762 4.57052 6.61919 4.54372C6.53077 4.51692 6.44033 4.51691 6.3519 4.54372C6.26347 4.57052 6.18991 4.61126 6.03231 4.76886L4.77032 6.03085C4.61272 6.18845 4.57198 6.26201 4.54518 6.35044C4.51838 6.43887 4.51838 6.5293 4.54518 6.61773C4.57198 6.70616 4.61272 6.77972 4.77032 6.93732L25.0639 27.2308C25.2215 27.3884 25.295 27.4292 25.3834 27.456C25.4719 27.4828 25.5623 27.4828 25.6507 27.456C25.7392 27.4292 25.8127 27.3885 25.9703 27.2309L27.2323 25.9689C27.3899 25.8113 27.4307 25.7377 27.4575 25.6493C27.4843 25.5608 27.4843 25.4704 27.4575 25.382C27.4307 25.2935 27.3899 25.22 27.2323 25.0624Z" fill="currentColor"/>',Gl='<path d="M8.66667 6.66667C8.29848 6.66667 8 6.96514 8 7.33333V24.6667C8 25.0349 8.29848 25.3333 8.66667 25.3333H12.6667C13.0349 25.3333 13.3333 25.0349 13.3333 24.6667V7.33333C13.3333 6.96514 13.0349 6.66667 12.6667 6.66667H8.66667Z" fill="currentColor"/> <path d="M19.3333 6.66667C18.9651 6.66667 18.6667 6.96514 18.6667 7.33333V24.6667C18.6667 25.0349 18.9651 25.3333 19.3333 25.3333H23.3333C23.7015 25.3333 24 25.0349 24 24.6667V7.33333C24 6.96514 23.7015 6.66667 23.3333 6.66667H19.3333Z" fill="currentColor"/>',Wl='<path d="M5.33334 26V19.4667C5.33334 19.393 5.39304 19.3333 5.46668 19.3333H7.86668C7.94031 19.3333 8.00001 19.393 8.00001 19.4667V23.3333C8.00001 23.7015 8.29849 24 8.66668 24H23.3333C23.7015 24 24 23.7015 24 23.3333V8.66666C24 8.29847 23.7015 7.99999 23.3333 7.99999H19.4667C19.393 7.99999 19.3333 7.9403 19.3333 7.86666V5.46666C19.3333 5.39302 19.393 5.33333 19.4667 5.33333H26C26.3682 5.33333 26.6667 5.63181 26.6667 5.99999V26C26.6667 26.3682 26.3682 26.6667 26 26.6667H6.00001C5.63182 26.6667 5.33334 26.3682 5.33334 26Z" fill="currentColor"/> <path d="M14.0098 8.42359H10.806C10.6872 8.42359 10.6277 8.56721 10.7117 8.6512L16.5491 14.4886C16.8094 14.7489 16.8094 15.171 16.5491 15.4314L15.3234 16.657C15.0631 16.9174 14.641 16.9174 14.3806 16.657L8.63739 10.9138C8.55339 10.8298 8.40978 10.8893 8.40978 11.0081V14.0236C8.40978 14.3918 8.1113 14.6903 7.74311 14.6903H6.00978C5.64159 14.6903 5.34311 14.3918 5.34311 14.0236L5.34311 6.02359C5.34311 5.6554 5.64159 5.35692 6.00978 5.35692L14.0098 5.35692C14.378 5.35692 14.6764 5.6554 14.6764 6.02359V7.75692C14.6764 8.12511 14.378 8.42359 14.0098 8.42359Z" fill="currentColor"/>',Ul='<path d="M16 15.3333C15.6318 15.3333 15.3333 15.6318 15.3333 16V20C15.3333 20.3682 15.6318 20.6667 16 20.6667H21.3333C21.7015 20.6667 22 20.3682 22 20V16C22 15.6318 21.7015 15.3333 21.3333 15.3333H16Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M5.33333 7.33334C5.33333 6.96515 5.63181 6.66667 5.99999 6.66667H26C26.3682 6.66667 26.6667 6.96515 26.6667 7.33334V24.6667C26.6667 25.0349 26.3682 25.3333 26 25.3333H5.99999C5.63181 25.3333 5.33333 25.0349 5.33333 24.6667V7.33334ZM7.99999 10C7.99999 9.63182 8.29847 9.33334 8.66666 9.33334H23.3333C23.7015 9.33334 24 9.63182 24 10V22C24 22.3682 23.7015 22.6667 23.3333 22.6667H8.66666C8.29847 22.6667 7.99999 22.3682 7.99999 22V10Z" fill="currentColor"/>',zl='<path d="M10.6667 6.6548C10.6667 6.10764 11.2894 5.79346 11.7295 6.11862L24.377 15.4634C24.7377 15.7298 24.7377 16.2692 24.3771 16.5357L11.7295 25.8813C11.2895 26.2065 10.6667 25.8923 10.6667 25.3451L10.6667 6.6548Z" fill="currentColor"/>',o0='<path d="M13.9213 5.53573C14.3146 5.45804 14.6666 5.76987 14.6666 6.17079V7.57215C14.6666 7.89777 14.4305 8.17277 14.114 8.24925C12.5981 8.61559 11.2506 9.41368 10.2091 10.506C9.98474 10.7414 9.62903 10.8079 9.34742 10.6453L8.14112 9.94885C7.79394 9.7484 7.69985 9.28777 7.96359 8.98585C9.48505 7.24409 11.5636 6.00143 13.9213 5.53573Z" fill="currentColor"/> <path d="M5.88974 12.5908C6.01805 12.2101 6.46491 12.0603 6.81279 12.2611L8.01201 12.9535C8.29379 13.1162 8.41396 13.4577 8.32238 13.7699C8.11252 14.4854 7.99998 15.2424 7.99998 16.0257C7.99998 16.809 8.11252 17.566 8.32238 18.2814C8.41396 18.5936 8.29378 18.9352 8.01201 19.0979L6.82742 19.7818C6.48051 19.9821 6.03488 19.8337 5.90521 19.4547C5.5345 18.3712 5.33331 17.2091 5.33331 16C5.33331 14.8078 5.5289 13.6613 5.88974 12.5908Z" fill="currentColor"/> <path d="M8.17106 22.0852C7.82291 22.2862 7.72949 22.7486 7.99532 23.0502C9.51387 24.773 11.5799 26.0017 13.9213 26.4642C14.3146 26.5419 14.6666 26.2301 14.6666 25.8291V24.4792C14.6666 24.1536 14.4305 23.8786 14.114 23.8021C12.5981 23.4358 11.2506 22.6377 10.2091 21.5453C9.98474 21.31 9.62903 21.2435 9.34742 21.4061L8.17106 22.0852Z" fill="currentColor"/> <path d="M17.3333 25.8291C17.3333 26.2301 17.6857 26.5418 18.079 26.4641C22.9748 25.4969 26.6666 21.1796 26.6666 16C26.6666 10.8204 22.9748 6.50302 18.079 5.5358C17.6857 5.4581 17.3333 5.76987 17.3333 6.17079V7.57215C17.3333 7.89777 17.5697 8.17282 17.8862 8.24932C21.3942 9.09721 24 12.2572 24 16.0257C24 19.7942 21.3942 22.9542 17.8862 23.802C17.5697 23.8785 17.3333 24.1536 17.3333 24.4792V25.8291Z" fill="currentColor"/> <path d="M14.3961 10.4163C13.9561 10.0911 13.3333 10.4053 13.3333 10.9525L13.3333 21.0474C13.3333 21.5946 13.9561 21.9087 14.3962 21.5836L21.2273 16.5359C21.5879 16.2694 21.5879 15.73 21.2273 15.4635L14.3961 10.4163Z" fill="currentColor"/>',l0='<path d="M15.6038 12.2147C16.0439 12.5399 16.6667 12.2257 16.6667 11.6786V10.1789C16.6667 10.1001 16.7351 10.0384 16.8134 10.0479C20.1116 10.4494 22.6667 13.2593 22.6667 16.6659C22.6667 20.3481 19.6817 23.3332 15.9995 23.3332C12.542 23.3332 9.69927 20.7014 9.36509 17.332C9.32875 16.9655 9.03371 16.6662 8.66548 16.6662L6.66655 16.6666C6.29841 16.6666 5.99769 16.966 6.02187 17.3334C6.36494 22.5454 10.7012 26.6667 16 26.6667C21.5228 26.6667 26 22.1895 26 16.6667C26 11.4103 21.9444 7.10112 16.7916 6.69757C16.7216 6.69209 16.6667 6.63396 16.6667 6.56372V4.98824C16.6667 4.44106 16.0439 4.12689 15.6038 4.45206L11.0765 7.79738C10.7159 8.06387 10.7159 8.60326 11.0766 8.86973L15.6038 12.2147Z" fill="currentColor"/>',c0='<path d="M16.6667 10.3452C16.6667 10.8924 16.0439 11.2066 15.6038 10.8814L11.0766 7.5364C10.7159 7.26993 10.7159 6.73054 11.0766 6.46405L15.6038 3.11873C16.0439 2.79356 16.6667 3.10773 16.6667 3.6549V5.22682C16.6667 5.29746 16.7223 5.35579 16.7927 5.36066C22.6821 5.76757 27.3333 10.674 27.3333 16.6667C27.3333 22.9259 22.2592 28 16 28C9.96483 28 5.03145 23.2827 4.68601 17.3341C4.66466 16.9665 4.96518 16.6673 5.33339 16.6673H7.3334C7.70157 16.6673 7.99714 16.9668 8.02743 17.3337C8.36638 21.4399 11.8064 24.6667 16 24.6667C20.4183 24.6667 24 21.085 24 16.6667C24 12.5225 20.8483 9.11428 16.8113 8.70739C16.7337 8.69957 16.6667 8.76096 16.6667 8.83893V10.3452Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M17.0879 19.679C17.4553 19.9195 17.8928 20.0398 18.4004 20.0398C18.9099 20.0398 19.3474 19.9205 19.7129 19.6818C20.0803 19.4413 20.3635 19.0938 20.5623 18.6392C20.7612 18.1847 20.8606 17.6373 20.8606 16.9972C20.8625 16.3608 20.764 15.8192 20.5652 15.3722C20.3663 14.9252 20.0822 14.5853 19.7129 14.3523C19.3455 14.1175 18.908 14 18.4004 14C17.8928 14 17.4553 14.1175 17.0879 14.3523C16.7224 14.5853 16.4402 14.9252 16.2413 15.3722C16.0443 15.8173 15.9449 16.3589 15.943 16.9972C15.9411 17.6354 16.0396 18.1818 16.2385 18.6364C16.4373 19.089 16.7205 19.4366 17.0879 19.679ZM19.1362 18.4262C18.9487 18.7349 18.7034 18.8892 18.4004 18.8892C18.1996 18.8892 18.0226 18.8211 17.8691 18.6847C17.7157 18.5464 17.5964 18.3372 17.5112 18.0568C17.4279 17.7765 17.3871 17.4233 17.389 16.9972C17.3909 16.3684 17.4847 15.9025 17.6703 15.5995C17.8559 15.2945 18.0993 15.1421 18.4004 15.1421C18.603 15.1421 18.7801 15.2093 18.9316 15.3438C19.0832 15.4782 19.2015 15.6828 19.2868 15.9574C19.372 16.2301 19.4146 16.5767 19.4146 16.9972C19.4165 17.6392 19.3237 18.1156 19.1362 18.4262Z" fill="currentColor"/> <path d="M13.7746 19.8978C13.8482 19.8978 13.9079 19.8381 13.9079 19.7644V14.2129C13.9079 14.1393 13.8482 14.0796 13.7746 14.0796H12.642C12.6171 14.0796 12.5927 14.0865 12.5716 14.0997L11.2322 14.9325C11.1931 14.9568 11.1693 14.9996 11.1693 15.0457V15.9497C11.1693 16.0539 11.2833 16.1178 11.3722 16.0635L12.464 15.396C12.4682 15.3934 12.473 15.3921 12.4779 15.3921C12.4926 15.3921 12.5045 15.404 12.5045 15.4187V19.7644C12.5045 19.8381 12.5642 19.8978 12.6378 19.8978H13.7746Z" fill="currentColor"/>',u0='<path d="M15.3333 10.3452C15.3333 10.8924 15.9561 11.2066 16.3962 10.8814L20.9234 7.5364C21.2841 7.26993 21.2841 6.73054 20.9235 6.46405L16.3962 3.11873C15.9561 2.79356 15.3333 3.10773 15.3333 3.6549V5.22682C15.3333 5.29746 15.2778 5.35579 15.2073 5.36066C9.31791 5.76757 4.66667 10.674 4.66667 16.6667C4.66667 22.9259 9.74078 28 16 28C22.0352 28 26.9686 23.2827 27.314 17.3341C27.3354 16.9665 27.0348 16.6673 26.6666 16.6673H24.6666C24.2984 16.6673 24.0029 16.9668 23.9726 17.3337C23.6336 21.4399 20.1937 24.6667 16 24.6667C11.5817 24.6667 8 21.085 8 16.6667C8 12.5225 11.1517 9.11428 15.1887 8.70739C15.2663 8.69957 15.3333 8.76096 15.3333 8.83893V10.3452Z" fill="currentColor"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M17.0879 19.679C17.4553 19.9195 17.8928 20.0398 18.4004 20.0398C18.9099 20.0398 19.3474 19.9205 19.7129 19.6818C20.0803 19.4413 20.3635 19.0938 20.5623 18.6392C20.7612 18.1847 20.8606 17.6373 20.8606 16.9972C20.8625 16.3608 20.764 15.8192 20.5652 15.3722C20.3663 14.9252 20.0822 14.5853 19.7129 14.3523C19.3455 14.1175 18.908 14 18.4004 14C17.8928 14 17.4553 14.1175 17.0879 14.3523C16.7224 14.5853 16.4402 14.9252 16.2413 15.3722C16.0443 15.8173 15.9449 16.3589 15.943 16.9972C15.9411 17.6354 16.0396 18.1818 16.2385 18.6364C16.4373 19.089 16.7205 19.4366 17.0879 19.679ZM19.1362 18.4262C18.9487 18.7349 18.7034 18.8892 18.4004 18.8892C18.1996 18.8892 18.0225 18.8211 17.8691 18.6847C17.7157 18.5464 17.5964 18.3372 17.5112 18.0568C17.4278 17.7765 17.3871 17.4233 17.389 16.9972C17.3909 16.3684 17.4847 15.9025 17.6703 15.5995C17.8559 15.2945 18.0992 15.1421 18.4004 15.1421C18.603 15.1421 18.7801 15.2093 18.9316 15.3438C19.0831 15.4782 19.2015 15.6828 19.2867 15.9574C19.372 16.2301 19.4146 16.5767 19.4146 16.9972C19.4165 17.6392 19.3237 18.1156 19.1362 18.4262Z" fill="currentColor"/> <path d="M13.7746 19.8978C13.8482 19.8978 13.9079 19.8381 13.9079 19.7644V14.2129C13.9079 14.1393 13.8482 14.0796 13.7746 14.0796H12.642C12.6171 14.0796 12.5927 14.0865 12.5716 14.0997L11.2322 14.9325C11.1931 14.9568 11.1693 14.9996 11.1693 15.0457V15.9497C11.1693 16.0539 11.2833 16.1178 11.3722 16.0635L12.464 15.396C12.4682 15.3934 12.473 15.3921 12.4779 15.3921C12.4926 15.3921 12.5045 15.404 12.5045 15.4187V19.7644C12.5045 19.8381 12.5642 19.8978 12.6378 19.8978H13.7746Z" fill="currentColor"/>',h0='<path fill-rule="evenodd" clip-rule="evenodd" d="M13.5722 5.33333C13.2429 5.33333 12.9629 5.57382 12.9132 5.89938L12.4063 9.21916C12.4 9.26058 12.3746 9.29655 12.3378 9.31672C12.2387 9.37118 12.1409 9.42779 12.0444 9.48648C12.0086 9.5083 11.9646 9.51242 11.9255 9.49718L8.79572 8.27692C8.48896 8.15732 8.14083 8.27958 7.9762 8.56472L5.5491 12.7686C5.38444 13.0538 5.45271 13.4165 5.70981 13.6223L8.33308 15.7225C8.3658 15.7487 8.38422 15.7887 8.38331 15.8306C8.38209 15.8867 8.38148 15.9429 8.38148 15.9993C8.38148 16.0558 8.3821 16.1121 8.38332 16.1684C8.38423 16.2102 8.36582 16.2503 8.33313 16.2765L5.7103 18.3778C5.45334 18.5836 5.38515 18.9462 5.54978 19.2314L7.97688 23.4352C8.14155 23.7205 8.48981 23.8427 8.79661 23.723L11.926 22.5016C11.9651 22.4864 12.009 22.4905 12.0449 22.5123C12.1412 22.5709 12.2388 22.6274 12.3378 22.6818C12.3745 22.7019 12.4 22.7379 12.4063 22.7793L12.9132 26.0993C12.9629 26.4249 13.2429 26.6654 13.5722 26.6654H18.4264C18.7556 26.6654 19.0356 26.425 19.0854 26.0995L19.5933 22.7801C19.5997 22.7386 19.6252 22.7027 19.6619 22.6825C19.7614 22.6279 19.8596 22.5711 19.9564 22.5121C19.9923 22.4903 20.0362 22.4862 20.0754 22.5015L23.2035 23.7223C23.5103 23.842 23.8585 23.7198 24.0232 23.4346L26.4503 19.2307C26.6149 18.9456 26.5467 18.583 26.2898 18.3771L23.6679 16.2766C23.6352 16.2504 23.6168 16.2104 23.6177 16.1685C23.619 16.1122 23.6196 16.0558 23.6196 15.9993C23.6196 15.9429 23.619 15.8866 23.6177 15.8305C23.6168 15.7886 23.6353 15.7486 23.668 15.7224L26.2903 13.623C26.5474 13.4172 26.6156 13.0544 26.451 12.7692L24.0239 8.56537C23.8592 8.28023 23.5111 8.15797 23.2043 8.27757L20.0758 9.49734C20.0367 9.51258 19.9927 9.50846 19.9569 9.48664C19.8599 9.42762 19.7616 9.37071 19.6618 9.31596C19.6251 9.2958 19.5997 9.25984 19.5933 9.21843L19.0854 5.89915C19.0356 5.57369 18.7556 5.33333 18.4264 5.33333H13.5722ZM16.0001 20.2854C18.3672 20.2854 20.2862 18.3664 20.2862 15.9993C20.2862 13.6322 18.3672 11.7132 16.0001 11.7132C13.6329 11.7132 11.714 13.6322 11.714 15.9993C11.714 18.3664 13.6329 20.2854 16.0001 20.2854Z" fill="currentColor"/>',Wr='<path d="M17.5091 24.6595C17.5091 25.2066 16.8864 25.5208 16.4463 25.1956L9.44847 20.0252C9.42553 20.0083 9.39776 19.9992 9.36923 19.9992H4.66667C4.29848 19.9992 4 19.7007 4 19.3325V12.6658C4 12.2976 4.29848 11.9992 4.66667 11.9992H9.37115C9.39967 11.9992 9.42745 11.99 9.45039 11.9731L16.4463 6.80363C16.8863 6.47845 17.5091 6.79262 17.5091 7.3398L17.5091 24.6595Z" fill="currentColor"/> <path d="M27.5091 9.33336C27.8773 9.33336 28.1758 9.63184 28.1758 10V22C28.1758 22.3682 27.8773 22.6667 27.5091 22.6667H26.1758C25.8076 22.6667 25.5091 22.3682 25.5091 22V10C25.5091 9.63184 25.8076 9.33336 26.1758 9.33336L27.5091 9.33336Z" fill="currentColor"/> <path d="M22.1758 12C22.544 12 22.8424 12.2985 22.8424 12.6667V19.3334C22.8424 19.7016 22.544 20 22.1758 20H20.8424C20.4743 20 20.1758 19.7016 20.1758 19.3334V12.6667C20.1758 12.2985 20.4743 12 20.8424 12H22.1758Z" fill="currentColor"/>',Ur='<path d="M17.5091 24.6594C17.5091 25.2066 16.8864 25.5207 16.4463 25.1956L9.44847 20.0252C9.42553 20.0083 9.39776 19.9991 9.36923 19.9991H4.66667C4.29848 19.9991 4 19.7006 4 19.3324V12.6658C4 12.2976 4.29848 11.9991 4.66667 11.9991H9.37115C9.39967 11.9991 9.42745 11.99 9.45039 11.973L16.4463 6.80358C16.8863 6.4784 17.5091 6.79258 17.5091 7.33975L17.5091 24.6594Z" fill="currentColor"/> <path d="M22.8424 12.6667C22.8424 12.2985 22.544 12 22.1758 12H20.8424C20.4743 12 20.1758 12.2985 20.1758 12.6667V19.3333C20.1758 19.7015 20.4743 20 20.8424 20H22.1758C22.544 20 22.8424 19.7015 22.8424 19.3333V12.6667Z" fill="currentColor"/>';function B(s){function t(e){return a.createElement(go,{paths:s,...e})}return t.displayName="DefaultLayoutIcon",t}const d0={AirPlayButton:{Default:B(X2)},GoogleCastButton:{Default:B(dd)},PlayButton:{Play:B(zl),Pause:B(Gl),Replay:B(l0)},MuteButton:{Mute:B(jl),VolumeLow:B(Ur),VolumeHigh:B(Wr)},CaptionButton:{On:B(_l),Off:B(Gr)},PIPButton:{Enter:B(Ul),Exit:B(Wl)},FullscreenButton:{Enter:B(Hl),Exit:B(ql)},SeekButton:{Backward:B(c0),Forward:B(u0)},DownloadButton:{Default:B(i0)},Menu:{Accessibility:B(Y2),ArrowLeft:B(J2),ArrowRight:B(s0),Audio:B(r0),Chapters:B(t0),Captions:B(Gr),Playback:B(o0),Settings:B(h0),AudioBoostUp:B(Wr),AudioBoostDown:B(Ur),SpeedUp:B(Bl),SpeedDown:B(Fl),QualityUp:B(Vl),QualityDown:B(Rl),FontSizeUp:B(Vl),FontSizeDown:B(Rl),OpacityUp:B(n0),OpacityDown:B(a0),RadioCheck:B(e0)},KeyboardDisplay:{Play:B(zl),Pause:B(Gl),Mute:B(jl),VolumeUp:B(Wr),VolumeDown:B(Ur),EnterFullscreen:B(Hl),ExitFullscreen:B(ql),EnterPiP:B(Ul),ExitPiP:B(Wl),CaptionsOn:B(_l),CaptionsOff:B(Gr),SeekForward:B(Bl),SeekBackward:B(Fl)}};function f0({videoUrl:s,thumbnailImg:t,videoTitle:e,videoType:i,setShowVideoPlayer:n,isClosable:r=!0}){return console.log("videoUrl",s),console.log("thumbnailImg",t),console.log("videoTitle",e),console.log("videoType",i),M.jsxs("div",{className:"video-player-container",children:[M.jsxs(hh,{storage:"storage-key",title:e,src:{src:s,type:"video/mp4"},aspectRatio:"16/9",className:"video-player-media-player",children:[M.jsx(dh,{}),M.jsx(ph,{className:"vds-poster",src:t,alt:e||"Video Poster"}),M.jsx(mo,{className:"vds-captions"}),M.jsx(ad,{noScrubGesture:!1,thumbnails:t,icons:d0})]}),r&&M.jsx(Jl,{color:"danger",fill:"outline",onClick:()=>{n&&n(!1)},className:"video-player-close-button",children:"close player"})]})}const fd=new Set,Kt=new WeakMap,ui=new WeakMap,Is=new WeakMap,ma=new WeakMap,p0=new WeakMap,hi=new WeakMap,Gn=new WeakMap,Ai=new WeakSet;let gs,Do=0,Io=0;const ts="__aa_tgt",qi="__aa_del",Wn="__aa_new",m0=s=>{const t=w0(s);t&&t.forEach(e=>E0(e))},g0=s=>{s.forEach(t=>{t.target===gs&&y0(),Kt.has(t.target)&&js(t.target)})};function b0(s){const t=ma.get(s);t==null||t.disconnect();let e=Kt.get(s),i=0;const n=5;e||(e=di(s),Kt.set(s,e));const{offsetWidth:r,offsetHeight:o}=gs,c=[e.top-n,r-(e.left+n+e.width),o-(e.top+n+e.height),e.left-n].map(h=>`${-1*Math.floor(h)}px`).join(" "),u=new IntersectionObserver(()=>{++i>1&&js(s)},{root:gs,threshold:1,rootMargin:c});u.observe(s),ma.set(s,u)}function js(s){clearTimeout(Gn.get(s));const t=Lr(s),e=Hi(t)?500:t.duration;Gn.set(s,setTimeout(async()=>{const i=Is.get(s);try{await(i==null?void 0:i.finished),Kt.set(s,di(s)),b0(s)}catch{}},e))}function y0(){clearTimeout(Gn.get(gs)),Gn.set(gs,setTimeout(()=>{fd.forEach(s=>bd(s,t=>pd(()=>js(t))))},100))}function v0(s){setTimeout(()=>{p0.set(s,setInterval(()=>pd(js.bind(null,s)),2e3))},Math.round(2e3*Math.random()))}function pd(s){typeof requestIdleCallback=="function"?requestIdleCallback(()=>s()):requestAnimationFrame(()=>s())}let ga,ti;const C0=typeof window<"u"&&"ResizeObserver"in window;C0&&(gs=document.documentElement,ga=new MutationObserver(m0),ti=new ResizeObserver(g0),window.addEventListener("scroll",()=>{Io=window.scrollY,Do=window.scrollX}),ti.observe(gs));function w0(s){return s.reduce((i,n)=>[...i,...Array.from(n.addedNodes),...Array.from(n.removedNodes)],[]).every(i=>i.nodeName==="#comment")?!1:s.reduce((i,n)=>{if(i===!1)return!1;if(n.target instanceof Element){if(zr(n.target),!i.has(n.target)){i.add(n.target);for(let r=0;r<n.target.children.length;r++){const o=n.target.children.item(r);if(o){if(qi in o)return!1;zr(n.target,o),i.add(o)}}}if(n.removedNodes.length)for(let r=0;r<n.removedNodes.length;r++){const o=n.removedNodes[r];if(qi in o)return!1;o instanceof Element&&(i.add(o),zr(n.target,o),ui.set(o,[n.previousSibling,n.nextSibling]))}}return i},new Set)}function zr(s,t){!t&&!(ts in s)?Object.defineProperty(s,ts,{value:s}):t&&!(ts in t)&&Object.defineProperty(t,ts,{value:s})}function E0(s){var t;const e=s.isConnected,i=Kt.has(s);e&&ui.has(s)&&ui.delete(s),Is.has(s)&&((t=Is.get(s))===null||t===void 0||t.cancel()),Wn in s?Zl(s):i&&e?T0(s):i&&!e?S0(s):Zl(s)}function Ne(s){return Number(s.replace(/[^0-9.\-]/g,""))}function $0(s){let t=s.parentElement;for(;t;){if(t.scrollLeft||t.scrollTop)return{x:t.scrollLeft,y:t.scrollTop};t=t.parentElement}return{x:0,y:0}}function di(s){const t=s.getBoundingClientRect(),{x:e,y:i}=$0(s);return{top:t.top+i,left:t.left+e,width:t.width,height:t.height}}function md(s,t,e){let i=t.width,n=t.height,r=e.width,o=e.height;const l=getComputedStyle(s);if(l.getPropertyValue("box-sizing")==="content-box"){const u=Ne(l.paddingTop)+Ne(l.paddingBottom)+Ne(l.borderTopWidth)+Ne(l.borderBottomWidth),h=Ne(l.paddingLeft)+Ne(l.paddingRight)+Ne(l.borderRightWidth)+Ne(l.borderLeftWidth);i-=h,r-=h,n-=u,o-=u}return[i,r,n,o].map(Math.round)}function Lr(s){return ts in s&&hi.has(s[ts])?hi.get(s[ts]):{duration:250,easing:"ease-in-out"}}function gd(s){if(ts in s)return s[ts]}function Oo(s){const t=gd(s);return t?Ai.has(t):!1}function bd(s,...t){t.forEach(e=>e(s,hi.has(s)));for(let e=0;e<s.children.length;e++){const i=s.children.item(e);i&&t.forEach(n=>n(i,hi.has(i)))}}function Ro(s){return Array.isArray(s)?s:[s]}function Hi(s){return typeof s=="function"}function T0(s){const t=Kt.get(s),e=di(s);if(!Oo(s))return Kt.set(s,e);let i;if(!t)return;const n=Lr(s);if(typeof n!="function"){const r=t.left-e.left,o=t.top-e.top,[l,c,u,h]=md(s,t,e),d={transform:`translate(${r}px, ${o}px)`},f={transform:"translate(0, 0)"};l!==c&&(d.width=`${l}px`,f.width=`${c}px`),u!==h&&(d.height=`${u}px`,f.height=`${h}px`),i=s.animate([d,f],{duration:n.duration,easing:n.easing})}else{const[r]=Ro(n(s,"remain",t,e));i=new Animation(r),i.play()}Is.set(s,i),Kt.set(s,e),i.addEventListener("finish",js.bind(null,s))}function Zl(s){Wn in s&&delete s[Wn];const t=di(s);Kt.set(s,t);const e=Lr(s);if(!Oo(s))return;let i;if(typeof e!="function")i=s.animate([{transform:"scale(.98)",opacity:0},{transform:"scale(0.98)",opacity:0,offset:.5},{transform:"scale(1)",opacity:1}],{duration:e.duration*1.5,easing:"ease-in"});else{const[n]=Ro(e(s,"add",t));i=new Animation(n),i.play()}Is.set(s,i),i.addEventListener("finish",js.bind(null,s))}function Kl(s,t){var e;s.remove(),Kt.delete(s),ui.delete(s),Is.delete(s),(e=ma.get(s))===null||e===void 0||e.disconnect(),setTimeout(()=>{if(qi in s&&delete s[qi],Object.defineProperty(s,Wn,{value:!0,configurable:!0}),t&&s instanceof HTMLElement)for(const i in t)s.style[i]=""},0)}function S0(s){var t;if(!ui.has(s)||!Kt.has(s))return;const[e,i]=ui.get(s);Object.defineProperty(s,qi,{value:!0,configurable:!0});const n=window.scrollX,r=window.scrollY;if(i&&i.parentNode&&i.parentNode instanceof Element?i.parentNode.insertBefore(s,i):e&&e.parentNode?e.parentNode.appendChild(s):(t=gd(s))===null||t===void 0||t.appendChild(s),!Oo(s))return Kl(s);const[o,l,c,u]=k0(s),h=Lr(s),d=Kt.get(s);(n!==Do||r!==Io)&&x0(s,n,r,h);let f,p={position:"absolute",top:`${o}px`,left:`${l}px`,width:`${c}px`,height:`${u}px`,margin:"0",pointerEvents:"none",transformOrigin:"center",zIndex:"100"};if(!Hi(h))Object.assign(s.style,p),f=s.animate([{transform:"scale(1)",opacity:1},{transform:"scale(.98)",opacity:0}],{duration:h.duration,easing:"ease-out"});else{const[g,b]=Ro(h(s,"remove",d));(b==null?void 0:b.styleReset)!==!1&&(p=(b==null?void 0:b.styleReset)||p,Object.assign(s.style,p)),f=new Animation(g),f.play()}Is.set(s,f),f.addEventListener("finish",Kl.bind(null,s,p))}function x0(s,t,e,i){const n=Do-t,r=Io-e,o=document.documentElement.style.scrollBehavior;if(getComputedStyle(gs).scrollBehavior==="smooth"&&(document.documentElement.style.scrollBehavior="auto"),window.scrollTo(window.scrollX+n,window.scrollY+r),!s.parentElement)return;const c=s.parentElement;let u=c.clientHeight,h=c.clientWidth;const d=performance.now();function f(){requestAnimationFrame(()=>{if(!Hi(i)){const p=u-c.clientHeight,g=h-c.clientWidth;d+i.duration>performance.now()?(window.scrollTo({left:window.scrollX-g,top:window.scrollY-p}),u=c.clientHeight,h=c.clientWidth,f()):document.documentElement.style.scrollBehavior=o}})}f()}function k0(s){const t=Kt.get(s),[e,,i]=md(s,t,di(s));let n=s.parentElement;for(;n&&(getComputedStyle(n).position==="static"||n instanceof HTMLBodyElement);)n=n.parentElement;n||(n=document.body);const r=getComputedStyle(n),o=Kt.get(n)||di(n),l=Math.round(t.top-o.top)-Ne(r.borderTopWidth),c=Math.round(t.left-o.left)-Ne(r.borderLeftWidth);return[l,c,e,i]}function P0(s,t={}){return ga&&ti&&(window.matchMedia("(prefers-reduced-motion: reduce)").matches&&!Hi(t)&&!t.disrespectUserMotionPreference||(Ai.add(s),getComputedStyle(s).position==="static"&&Object.assign(s.style,{position:"relative"}),bd(s,js,v0,n=>ti==null?void 0:ti.observe(n)),Hi(t)?hi.set(s,t):hi.set(s,{duration:250,easing:"ease-in-out",...t}),ga.observe(s,{childList:!0}),fd.add(s))),Object.freeze({parent:s,enable:()=>{Ai.add(s)},disable:()=>{Ai.delete(s)},isEnabled:()=>Ai.has(s)})}const A0=()=>{const[s,t]=a.useState(null),[e,i]=a.useState([]),[n,r]=a.useState(!0),[o,l]=a.useState(null),[c,u]=a.useState(null),[h,d]=a.useState(null),[f,p]=a.useState(!1),[g,b]=a.useState(!1),[y,w]=a.useState({}),[S,N]=a.useState({}),j=a.useRef(null),V=a.useRef(null),K=()=>{j.current&&j.current.scrollToTop()};a.useEffect(()=>{const{dir:T}=Ql();u(T)},[]),a.useEffect(()=>{},[h]),a.useEffect(()=>{c&&k(c)},[c]);function ot(){p(!1)}const C=async T=>{try{return await $n("browse","POST",{item_index:T})}catch(x){throw console.error("Error making browse request:",x),x}},k=async T=>{try{if(r(!0),T&&S[T])t(S[T]);else{const x=await C(T);if(x.status!==200)throw new Error("Unauthorized");const D=x.data;t(D),T&&N({...S,[T]:D})}}catch(x){l("Error fetching browse data "+x.message)}finally{r(!1)}},_=async(T=c,x,D)=>{if(x==="drive#folder")try{r(!0);const st=await C(T);if(st.status!==200)throw new Error("Unauthorized");const At=st.data;t(At),i(Et=>[...Et,D]),T&&N({...S,[T]:At})}catch(st){l("Error fetching browse data "+st.message)}finally{r(!1)}},St=()=>{const T=[...e],x=T.pop();i(T),x&&S[x]?t(S[x]):k(x||"")},gt=M.jsxs(M.Fragment,{children:[M.jsx("b",{children:" Try sample Magnet:"}),"  ",M.jsx(Nr,{style:{wordBreak:"break-all"},children:"magnet:?xt=urn:btih:12D47B6836FD7787531393069ED5ADE7F53DF7D8"}),M.jsx("br",{}),M.jsx(Jl,{color:"primary",fill:"outline",onClick:()=>{window.navigator.clipboard.writeText("magnet:?xt=urn:btih:12D47B6836FD7787531393069ED5ADE7F53DF7D8"),l("Magnet copied, Go to Create Task Section")},children:"copy"})]});a.useEffect(()=>{V.current&&P0(V.current,{duration:500,disrespectUserMotionPreference:!1})},[V]);const ct=T=>{if(s){const x=s.files.filter(st=>st.id!==T),D={...s,files:x};t(D)}},et=({item:T})=>{const x=T.kind==="drive#folder"?T.icon_link:T.thumbnail_link,D=()=>M.jsx(Dd,{src:x,className:"thumbnail-img",alt:T.name,onError:st=>{st.currentTarget.src=T.icon_link}});return M.jsx(M.Fragment,{children:T.kind==="drive#folder"?M.jsx(jo,{className:"thumbnail",children:D()}):M.jsx(tf,{src:T.thumbnail_link,children:M.jsx(jo,{className:"thumbnail",children:D()})})})};return M.jsxs(M.Fragment,{children:[M.jsx(Id,{title:"Browse Folders"}),M.jsx(Sd,{loading:n,children:M.jsx(xd,{fullscreen:!0,ref:j,scrollEvents:!0,children:M.jsxs(M.Fragment,{children:[o&&M.jsx(Xl,{position:"top",isOpen:!!o,onDidDismiss:()=>l(null),message:o,duration:2e3}),g&&M.jsx(f0,{videoUrl:y.videoUrl||"",thumbnailImg:y.thumbnailImg,videoTitle:y.videoTitle,videoType:y.videoType||"video/mp4",setShowVideoPlayer:b}),M.jsx("div",{className:"browse-list",children:M.jsxs(Yl,{ref:V,children:[e.length>0&&M.jsxs(Zr,{onClick:St,className:"hover-effect",children:[M.jsx(Kr,{icon:kd})," ",M.jsx(Ho,{children:M.jsx(Nr,{children:"Folder Up"})})]}),n||(s==null?void 0:s.files.length)!==0?s==null?void 0:s.files.map(T=>M.jsxs(Zr,{onClick:()=>_(T.id,T.kind,T.parent_id),className:T.kind==="drive#folder"?"hover-effect":"",children:[T.kind!=="drive#folder"&&M.jsx(Jd,{speed:()=>400,easing:x=>x===2?"cubic-bezier(0.36, 0, 0.66, -0.56)":"cubic-bezier(0.34, 1.56, 0.64, 1)",maskOpacity:.5,bannerVisible:!1,children:M.jsx(et,{item:T})}),T.kind==="drive#folder"&&M.jsx(et,{item:T}),M.jsx(Ho,{children:T.name}),M.jsx(Kr,{color:"primary",icon:Pd,size:"default",className:"hover-effect",slot:"end",onClick:x=>{x.stopPropagation(),d(T),p(!0)}})]},T.id)):M.jsx(Od,{cardTitle:"No Content to Browse",cardSubtitle:M.jsx(Nr,{children:"Try again after adding content"}),cardSubTitleStyle:{display:"flex",flexDirection:"column",textAlign:"justify"},cardContent:gt,icon:Ad,titleColor:"primary"})]})}),M.jsxs(Md,{isOpen:f,keepContentsMounted:!0,onDidDismiss:ot,initialBreakpoint:1,breakpoints:[0,1],children:[M.jsx(Ld,{color:"light",children:M.jsxs("h3",{className:"ion-text",children:[h==null?void 0:h.name,(h==null?void 0:h.kind)!=="drive#folder"&&M.jsx(M.Fragment,{children:M.jsx(Nd,{color:"warning",children:`${h&&h.size&&(parseInt(h.size)||0)>1024*1024?Qr(parseInt(h.size)):Qr(parseInt((h==null?void 0:h.size)||"")||0)}`})})]})}),M.jsx(sf,{item:h,setShowModal:p,setIsLoading:r,setVideoDetails:w,setShowVideoPlayer:b,scrollToTop:K,handleDeleteItem:ct})]})]})})})]})},sg=Object.freeze(Object.defineProperty({__proto__:null,default:A0},Symbol.toStringTag,{value:"Module"}));export{yp as A,O0 as B,Y as C,lt as D,ya as E,sg as F,U1 as H,wc as I,X as L,he as Q,Yc as R,ai as T,Xc as V,Z1 as a,$ as b,Aa as c,Q as d,m as e,kt as f,An as g,Cp as h,q as i,ee as j,Jn as k,E as l,ri as m,kn as n,H as o,bi as p,ea as q,xt as r,Dt as s,pf as t,Zt as u,Oi as v,R0 as w,P as x,V0 as y,Pa as z};

import{t as a,ar as j,aR as h,aS as l,aT as u,j as e,d as S,e as y,f as D,h as g,b as k,aU as p,F as C,E,aV as L,aW as b,aX as O,aF as F}from"./index-YQZYyPkh.js";const T=({title:m})=>{const[s,n]=a.useState(null),[i,c]=a.useState(!1);let o=j("darkMode");const t=h(),r=()=>o==="true"?L:b,[I,d]=a.useState(r());a.useEffect(()=>{},[i]),a.useEffect(()=>{o===null&&(o=t,d(r()),console.log("initialDarkMode",t),t==="true"?l("darkMode","true"):l("darkMode","false")),u()},[]);async function f(){try{c(!0),O("auth"),F(),n({message:"Sign-out successful",color:"success"}),window.location.href="/login"}catch(x){n({message:`Error during  sign-out:, ${x}`,color:"danger"})}finally{c(!1)}}const M=()=>{o=(o!=="true").toString(),l("darkMode",o),d(r()),console.log("darkMode",o),u()};return console.log("darkMode",o),e.jsxs(e.Fragment,{children:[e.jsxs(S,{children:[e.jsxs(y,{color:"light",children:[e.jsx(D,{children:m}),e.jsx(g,{slot:"end",fill:"clear",onClick:M,children:e.jsx(k,{color:o==="true"?"warning":"danger",icon:I,slot:"end",size:"icon-only"})}),e.jsx(g,{color:"danger",slot:"end",onClick:f,"aria-label":"Logout",children:e.jsx(k,{icon:p,size:"icon-only"})})]}),e.jsx(C,{type:"indeterminate",style:{display:i?"block":"none"}})]}),e.jsx(E,{isOpen:!!s,onDidDismiss:()=>n(null),message:s==null?void 0:s.message,duration:3e3,color:s==null?void 0:s.color})]})},H=T;export{H as C};

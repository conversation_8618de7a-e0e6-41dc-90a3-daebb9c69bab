import{j as e,t as o,B as g,E as d,L as m}from"./index-YQZYyPkh.js";import{A as p,a as x}from"./AuthButtons-axJc_ndf.js";import"./index-BLf7waug.js";function f({callbackFunc:s}){return e.jsx(e.Fragment,{children:e.jsx(p,{titleHeading:"Sign Up",nextTitle:{text:"Login",redirect:"/login"},callbackFunc:s})})}function U(){const[s,a]=o.useState(null),[i,n]=o.useState(!1);async function c(l,u){try{n(!0);const t=await m("signup","POST",{email:l,password:u});if(t.status!==200){const r=t.data;a({message:r.error,color:"danger"})}else{const r=await t.data;a({message:r.result+", check your mail",color:"success"})}}catch(t){a({message:t,color:"danger"})}finally{n(!1)}}return e.jsxs(e.Fragment,{children:[e.jsxs(g,{loading:i,children:[e.jsx(x,{}),e.jsx(f,{callbackFunc:c})]}),e.jsx(d,{isOpen:!!s,onDidDismiss:()=>a(null),message:s==null?void 0:s.message,duration:3e3,color:s==null?void 0:s.color})]})}export{U as default};

import{t as p,j as e,aC as x,h as m,b as f}from"./index-YQZYyPkh.js";function h({handleSubmit:s,icon:a,inputStyle:u,buttonText:l,customPlaceholder:o,onSubmitClearInput:i=!1}){const t=p.useRef(null);return e.jsx("form",{onSubmit:c=>{var n,r;c.preventDefault(),s((n=t.current)==null?void 0:n.value),i&&((r=t.current)!=null&&r.value)&&(t.current.value="")},children:e.jsxs("div",{className:"container ",children:[e.jsx(x,{autoGrow:!0,style:{...u},placeholder:o,ref:t,fill:"outline"})," ",e.jsxs(m,{type:"submit","aria-label":"submit",shape:"round",expand:"full",style:{minHeight:"44px",margin:0},children:[e.jsx(f,{icon:a}),l]})]})})}export{h as C};

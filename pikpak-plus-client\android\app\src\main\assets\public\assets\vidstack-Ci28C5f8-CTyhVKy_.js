const __vite__fileDeps=["assets/vidstack-DOSbApF_-l5CC1Gmm.js","assets/BrowseFolders-B5zrTqBv.js","assets/index-YQZYyPkh.js","assets/index-Dd_C_NnE.css","assets/CustomIonHeader-RqF7V-cv.js","assets/HelperCard-t_yvTUPv.js","assets/HelperCard-xinFkzDJ.css","assets/BrowseFolders-Bq76TqZ_.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{ab as g}from"./index-YQZYyPkh.js";import{y as p,I as u,z as f,A as h,b as m,x as C,h as E}from"./BrowseFolders-B5zrTqBv.js";import"./CustomIonHeader-RqF7V-cv.js";import"./HelperCard-t_yvTUPv.js";function w(){return"https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1"}function v(){var a;return!!((a=window.cast)!=null&&a.framework)}function y(){var a,e;return!!((e=(a=window.chrome)==null?void 0:a.cast)!=null&&e.isAvailable)}function l(){return s().getCastState()===cast.framework.CastState.CONNECTED}function s(){return window.cast.framework.CastContext.getInstance()}function d(){return s().getCurrentSession()}function _(){var a;return(a=d())==null?void 0:a.getSessionObj().media[0]}function O(a){var t;return((t=_())==null?void 0:t.media.contentId)===(a==null?void 0:a.src)}function S(){return{language:"en-US",autoJoinPolicy:chrome.cast.AutoJoinPolicy.ORIGIN_SCOPED,receiverApplicationId:chrome.cast.media.DEFAULT_MEDIA_RECEIVER_APP_ID,resumeSavedSession:!0,androidReceiverCompatible:!0}}function I(a){return`Google Cast Error Code: ${a}`}function R(a,e){return p(s(),a,e)}class A{constructor(){this.name="google-cast"}get cast(){return s()}mediaType(){return"video"}canPlay(e){return u&&!f&&h(e)}async prompt(e){var i;let t,o,r;try{t=await this.Fj(e),this.f||(this.f=new cast.framework.RemotePlayer,new cast.framework.RemotePlayerController(this.f)),o=e.player.createEvent("google-cast-prompt-open",{trigger:t}),e.player.dispatchEvent(o),this.De(e,"connecting",o),await this.Gj(m(e.$props.googleCast)),e.$state.remotePlaybackInfo.set({deviceName:(i=d())==null?void 0:i.getCastDevice().friendlyName}),l()&&this.De(e,"connected",o)}catch(n){const c=n instanceof Error?n:this.Ee((n+"").toUpperCase(),"Prompt failed.");throw r=e.player.createEvent("google-cast-prompt-error",{detail:c,trigger:o??t,cancelable:!0}),e.player.dispatch(r),this.De(e,l()?"connected":"disconnected",r),c}finally{e.player.dispatch("google-cast-prompt-close",{trigger:r??o??t})}}async load(e){if(C)throw Error("[vidstack] can not load google cast provider server-side");if(!this.f)throw Error("[vidstack] google cast player was not initialized");return new(await g(()=>import("./vidstack-DOSbApF_-l5CC1Gmm.js"),__vite__mapDeps([0,1,2,3,4,5,6,7]))).GoogleCastProvider(this.f,e)}async Fj(e){if(v())return;const t=e.player.createEvent("google-cast-load-start");e.player.dispatch(t),await E(w()),await customElements.whenDefined("google-cast-launcher");const o=e.player.createEvent("google-cast-loaded",{trigger:t});if(e.player.dispatch(o),!y())throw this.Ee("CAST_NOT_AVAILABLE","Google Cast not available on this platform.");return o}async Gj(e){this.Hj(e);const t=await this.cast.requestSession();if(t)throw this.Ee(t.toUpperCase(),I(t))}Hj(e){var t;(t=this.cast)==null||t.setOptions({...S(),...e})}De(e,t,o){const r={type:"google-cast",state:t};e.delegate.c("remote-playback-change",r,o)}Ee(e,t){const o=Error(t);return o.code=e,o}}var j=Object.freeze({__proto__:null,GoogleCastLoader:A});export{s as a,d as b,I as c,j as d,_ as g,O as h,R as l};

import{t as o,L as x,j as e,B as S,D as h,q as a,h as f,aP as i,al as g,b as y,U as C,aQ as I,W as l,Z as b}from"./index-YQZYyPkh.js";import{C as E}from"./CustomIonHeader-RqF7V-cv.js";import{H as O}from"./HelperCard-t_yvTUPv.js";function T(){const[s,c]=o.useState({}),[t,d]=o.useState(localStorage.getItem("selectedServer")||""),[m,n]=o.useState(!1);o.useEffect(()=>{const r=localStorage.getItem("serverOptions");r?c(JSON.parse(r)):u()},[]),o.useEffect(()=>{},[t]),o.useEffect(()=>{localStorage.setItem("selectedServer",t)},[t]);const u=async()=>{try{n(!0);const j=(await x("getServers","GET",{})).data;c(j)}catch(r){console.error(r)}finally{n(!1)}},p=r=>e.jsxs(e.Fragment,{children:[e.jsxs(a,{color:"black",children:["Drive:",l(s[r].drive_used||0)+" / "+l(s[r].limit)]})," ",e.jsxs(a,{color:"black",children:["Expiry: ",b(r)]})]}),v=r=>{d(r)};return e.jsxs(e.Fragment,{children:[e.jsx(E,{title:"Settings"}),e.jsx(S,{loading:m,children:e.jsxs(h,{fullscreen:!0,children:[e.jsx("h2",{color:"primary",className:"title-server",children:e.jsx(a,{color:"primary",children:"Select #Server"})}),e.jsx("div",{className:"card-divider",children:s&&Object.keys(s).map(r=>e.jsx(O,{cardTitle:"Premium #"+s[r].server_id,cardSubtitle:p(r),cardSubTitleStyle:{display:"flex",flexDirection:"column",textAlign:"justify"},cardContent:e.jsx(e.Fragment,{children:e.jsx(f,{slot:"end",onClick:()=>v(r),fill:t===r?"clear":"solid",className:"ion-button-server",children:t===r?"Connected":"Connect"})}),icon:i,titleColor:"tertiary"},r))}),e.jsx("div",{className:"server-contact",children:s[t]&&s[t].contact&&e.jsx(e.Fragment,{children:e.jsxs(g,{color:"tertiary",className:"ion-chip-server",outline:!0,children:["#",t,"   ",e.jsx(y,{icon:i}),e.jsxs(a,{children:["Support/Queries ",e.jsx("br",{})," contact:"," ",s[t].contact]})]})})}),e.jsx(C,{className:"ion-item-divider"}),e.jsx(I,{})]})})]})}export{T as default};

import{M as K,N as Y,O as $,P as J,Q as S,t as c,K as ee,j as e,b as r,q as i,R as te,S as T,D as ae,B as se,T as re,U as ie,V as ne,W as L,X as P,Y as oe,Z as le,_ as ce,$ as de,a0 as D,h as p,a1 as ue,a2 as he,i as me,a3 as pe,a4 as xe,a5 as ke,a6 as ge,E as fe,L as I,a7 as je,F as be}from"./index-YQZYyPkh.js";import{C as ve}from"./CustomInput-BkMvF80D.js";import{G as B}from"./index-BLf7waug.js";import{C as ye}from"./CustomIonHeader-RqF7V-cv.js";import{H as h}from"./HelperCard-t_yvTUPv.js";const Ce="data:image/svg+xml,%3c?xml%20version='1.0'%20encoding='utf-8'?%3e%3c!--%20Uploaded%20to:%20SVG%20Repo,%20www.svgrepo.com,%20Generator:%20SVG%20Repo%20Mixer%20Tools%20--%3e%3csvg%20xmlns='http://www.w3.org/2000/svg'%20width='14'%20height='14'%20viewBox='0%200%2020%2020'%3e%3cpath%20d='M9%203.5C7.4178%203.5%205.87103%203.96919%204.55544%204.84824C3.23985%205.72729%202.21447%206.97672%201.60897%208.43853C1.00347%209.9003%200.84504%2011.5089%201.15372%2013.0607C1.4624%2014.6126%202.22433%2016.038%203.34315%2017.1569C4.46197%2018.2757%205.88743%2019.0376%207.4393%2019.3463C8.9911%2019.655%2010.5997%2019.4965%2012.0615%2018.891C13.5233%2018.2855%2014.7727%2017.2602%2015.6518%2015.9446C16.5308%2014.629%2017%2013.0823%2017%2011.5C17%209.37827%2016.1571%207.34344%2014.6569%205.84315C13.1566%204.34285%2011.1217%203.5%209%203.5ZM12.93%208.98L11.62%2015.17C11.52%2015.61%2011.26%2015.71%2010.89%2015.51L8.89%2014.03L7.89%2014.96C7.8429%2015.0215%207.7824%2015.0715%207.7131%2015.1062C7.6438%2015.1408%207.5675%2015.1592%207.49%2015.16L7.63%2013.16L11.33%209.81C11.5%209.67%2011.33%209.59%2011.09%209.73L6.55%2012.58L4.55%2011.96C4.12%2011.83%204.11%2011.53%204.64%2011.33L12.35%208.33C12.73%208.22%2013.05%208.44%2012.93%208.98Z'%3e%3c/path%3e%3c/svg%3e",we=[{link:"Support US",value:"https://www.buymeacoffee.com/bharathganji",icon:K},{link:"PikPak Invitation (premium)",value:"https://mypikpak.com/drive/activity/invited?invitation-code=47295398",icon:Y},{link:"Github",value:"https://github.com/bharathganji/pikpak-plus",icon:$},{link:"PikPak-Plus APK",value:"https://icedrive.net/s/wFkBVtNPkFR8b42vyX8X91F6xiP2",icon:J},{link:"Telegram",value:"https://t.me/pikpak_plus",icon:Ce},{link:"Navi Downloader Android",value:"https://github.com/TachibanaGeneralLaboratories/download-navi/releases/",icon:S},{link:"VLC Media Player",value:"https://www.videolan.org/vlc/",icon:S}],Se=["Download torrent links to Cloud ⚡","Cumulative download quota 4TB/month","Storage capacity of 10TB","Search multiple Torrent Indexers","Share files with your friends"],Te=["Dedicated username and password","one-time payment, Lifetime validity","Premium support"],Ee=()=>{var b,v,y,C;const[x,E]=c.useState(null),[A,F]=c.useState(null),[o,d]=c.useState(null),[G,k]=c.useState(!1),[n,O]=c.useState(null),[R,g]=c.useState(!1);c.useEffect(()=>{const{email:t,dir:a}=ee();E(t),F(a)},[]);const N=async t=>{k(!0);const a=/magnet:\?xt=urn:btih:[a-zA-Z0-9]*/,Q=/https?:\/\/(www\.)?twitter\.com\/.*/,X=/https?:\/\/(www\.)?tiktok\.com\/.*/,Z=/https?:\/\/(www\.)?facebook\.com\/.*/,m=Array.from(new Set(t.split(`
`).map(s=>s.trim()).filter(s=>s!=="")));let w=!0;for(let s=0;s<m.length;s++){const l=m[s];let u=!1;if((a.test(l)||Q.test(l)||X.test(l)||Z.test(l))&&(u=!0),!u){d({message:"Invalid link format at link number "+(s+1),color:"danger"}),w=!1;break}}if(w)for(let s=0;s<m.length;s++)try{const u=(await I("addURL","POST",{url:m[s],email:x,user_dir:A})).data.result;u&&u.upload_type==="UPLOAD_TYPE_URL"?d({message:"Task Created for link number "+(s+1),color:"success"}):d({message:"Error adding task for link number "+(s+1),color:"danger"})}catch(l){console.error("Error adding task for link number",s+1,":",l),d({message:"Error adding task for link number "+(s+1),color:"danger"})}k(!1)},M=Te.map((t,a)=>e.jsxs("div",{className:"usefull-links",children:[e.jsx(r,{icon:D})," ",e.jsx(i,{color:"dark",children:t},a)]},a)),U=`https://wa.me/8801529549?text=I'm Intrested in PikPak-Plus WebDav, 
  need more info`,_="https://wa.me/8801529549?text=I'm Intrested in Purchasing PikPak-Plus WebDav",V=we.map((t,a)=>e.jsxs("div",{className:"usefull-links",children:[e.jsx(r,{color:"dark",icon:t.icon}),e.jsx(i,{color:"dark",children:e.jsxs("a",{href:t==null?void 0:t.value,rel:"noopener noreferrer",target:"_blank",children:["  ",t==null?void 0:t.link]})},a)]},a)),H=Se.map((t,a)=>e.jsx("div",{className:"usefull-links",children:e.jsxs(i,{color:"dark",children:[e.jsx(r,{icon:je})," ",t]},a)},a)),f=["success","tertiary","primary","secondary","warning","danger"],z=f[Math.floor(Math.random()*f.length)],j=({value:t,size:a})=>e.jsxs(e.Fragment,{children:[e.jsx(be,{color:"secondary",value:t/a}),e.jsxs(i,{children:[t.toFixed(2)," GB / ",a," GB"]})]}),q=e.jsx(e.Fragment,{children:R&&n?e.jsx(te,{name:"lines"}):e.jsxs(e.Fragment,{children:[e.jsx(i,{color:"dark",children:"Cloud Download Traffic 40 TB / Month"}),e.jsx(j,{value:T(((b=n==null?void 0:n.offline)==null?void 0:b.size)??0),size:4e4}),e.jsx("br",{}),e.jsx(i,{color:"dark",children:"Downstream Traffic 4 TB / Month"}),e.jsx(j,{value:T(((v=n==null?void 0:n.download)==null?void 0:v.size)??0),size:4e3})]})}),W=async()=>{try{g(!0);const a=(await I("serverstats","POST",{})).data;O(a)}catch(t){console.error("Error:",t)}finally{g(!1)}};return e.jsxs(e.Fragment,{children:[e.jsx(ye,{title:"Create Cloud Task"}),e.jsx(ae,{fullscreen:!0,children:e.jsx(se,{loading:G,children:e.jsxs("div",{style:{display:"flex",flexDirection:"column",paddingBottom:"6rem"},children:[e.jsxs("div",{className:"custom-container",children:[e.jsxs("div",{className:"container-welcome",children:[e.jsx("span",{className:"email-welcome",children:e.jsx(i,{children:e.jsxs("span",{children:["Welcome..",e.jsx(r,{color:z,size:"default",icon:re})]})})}),x,e.jsx(ie,{style:{visibility:"hidden"}}),e.jsxs(i,{className:"text-flex-style",children:[e.jsxs("span",{children:[e.jsx(r,{color:"dark",icon:ne}),"  ",L(((y=P())==null?void 0:y.available)||0)+" / "+L(((C=P())==null?void 0:C.limit)||0)," ","used","  "]}),e.jsxs("span",{children:[e.jsx(r,{color:"dark",icon:oe})," ","Expiry: "+(le(ce())||"Contact Admin")]})]}),e.jsxs("div",{className:"github-btn-container",children:[e.jsx(B,{href:"https://github.com/sponsors/bharathganji","data-color-scheme":"no-preference: light; light: light; dark: dark;","data-icon":"octicon-heart","data-size":"large","aria-label":"Sponsor @bharathganji on GitHub",children:"Sponsor"}),e.jsx(B,{href:"https://github.com/bharathganji/pikpak-plus","data-color-scheme":"no-preference: light; light: light; dark: dark;","data-icon":"octicon-star","data-size":"large","data-show-count":"true","aria-label":"Star bharathganji/pikpak-plus on GitHub",children:"Star"})]})]}),e.jsx(ve,{handleSubmit:N,inputStyle:{minHeight:"184px"},buttonText:"Create Task",onSubmitClearInput:!0,icon:de,customPlaceholder:`Supported link formats:
- Magnet URI (magnet:?xt=urn:btih)
- X(Twitter)
- TikTok
- Facebook      

Multiple links can be added at once by line break.`})]}),e.jsxs("div",{className:"container",children:[e.jsx(h,{cardTitle:"Purchase [WEBDAV]",cardSubtitle:M,cardSubTitleStyle:{display:"flex",flexDirection:"column",textAlign:"justify"},icon:D,titleColor:"danger",cardContent:e.jsxs(e.Fragment,{children:[e.jsxs(p,{fill:"outline",href:_,target:"_blank",color:"tertiary",children:[e.jsx(r,{icon:ue})," ",e.jsx(i,{children:e.jsx("strong",{children:" Purchase 5$"})})]}),e.jsx(p,{fill:"outline",href:U,target:"_blank",color:"tertiary",children:e.jsx(r,{icon:he})})]})})," ",e.jsx(h,{cardTitle:"Helper Card",cardSubtitle:H,cardSubTitleStyle:{display:"flex",flexDirection:"column",textAlign:"justify"},icon:me,titleColor:"primary"}),e.jsx(h,{cardTitle:"Useful Links",cardSubtitle:V,cardSubTitleStyle:{display:"flex",flexDirection:"column",textAlign:"justify"},icon:pe,titleColor:"success"}),e.jsx(h,{cardTitle:"Transfer Quota Details",titleColor:"tertiary",icon:xe,cardContent:n!==null?q:e.jsx(p,{fill:"outline",onClick:W,children:"Load stats . . ."})}),e.jsx(h,{cardTitle:"Frequently Asked Questions",titleColor:"tertiary",icon:ke,cardContent:e.jsxs(p,{fill:"outline",routerLink:"/faq",children:["FAQ PAGE   ",e.jsx(r,{icon:ge})]})})]}),e.jsx(fe,{isOpen:!!o,onDidDismiss:()=>d(null),message:o==null?void 0:o.message,duration:3e3,color:o==null?void 0:o.color})]})})})]})};export{Ee as default};

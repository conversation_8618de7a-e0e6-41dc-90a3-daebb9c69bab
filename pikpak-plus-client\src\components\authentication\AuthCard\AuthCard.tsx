import React, { useRef, useState, use<PERSON>emo } from 'react'
import {
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardSubtitle,
  IonCardTitle,
  IonInput,
  IonButton,
  IonIcon,
  IonToast,
  IonText,
} from '@ionic/react'
import './AuthCard.css'
import { logInOutline, personCircleOutline, warningOutline } from 'ionicons/icons'
// @ts-expect-error Description of why the @ts-expect-error is necessary
import { ReactComponent as Logo } from '../../../assets/pikpkak_plus.svg'

type nextTitleType = {
  text: string
  redirect: string
}

type AuthProps = {
  titleHeading: string
  nextTitle: nextTitleType
  callbackFunc: any
}

const AuthCard: React.FC<AuthProps> = ({
  titleHeading,
  nextTitle,
  callbackFunc,
}: AuthProps) => {
  const emailRef = useRef<HTMLIonInputElement>(null)
  const passwordRef = useRef<HTMLIonInputElement>(null)
  const [showToast, setShowToast] = useState<{
    message: string
    color: string
  } | null>(null)

  const isSignUp = titleHeading === 'Sign Up'

  const handleSignIn = () => {
    if (isSignUp) {
      setShowToast({
        message: 'Sign up is disabled. Service will be discontinued on July 30, 2025.',
        color: 'warning',
      })
      return
    }

    const email = emailRef.current?.value || ''
    const password = passwordRef.current?.value || ''

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const passwordRegex = /^.{6,}$/

    if (!emailRegex.test(email.toString().trim())) {
      setShowToast({
        message: 'Invalid Email',
        color: 'danger',
      })
      return
    }

    if (!passwordRegex.test(password.toString())) {
      passwordRef.current!.value = '' // Clear the password field
      setShowToast({
        message: 'Password should be at least 6 characters',
        color: 'danger',
      })
      return
    }

    callbackFunc(email, password)
  }

  const handleOnSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    handleSignIn()
  }

  const MemoizedLogo = useMemo(() => <Logo />, [])
  const MemoizedIonCardSubtitle = useMemo(
    () => <IonCardSubtitle>Enter your credentials</IonCardSubtitle>,
    [],
  )

  const MemoizedClearIonButton = useMemo(
    () => (
      <IonButton
        fill="clear"
        expand="full"
        color="tertiary"
        shape="round"
        className="content-container-title1"
        href={nextTitle.redirect}
      >
        <span>{nextTitle.text}</span>
        <IonIcon icon={logInOutline}></IonIcon>
      </IonButton>
    ),
    [nextTitle.redirect, nextTitle.text],
  )

  return (
    <div className="backg">
      {MemoizedLogo}
  
        {/* Discontinuation Notice */}
        <IonCardContent>
          <div style={{ 
            backgroundColor: '#fff3cd', 
            border: '1px solid #ffeaa7', 
            borderRadius: '8px', 
            padding: '12px', 
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <IonIcon icon={warningOutline} color="warning" />
            <IonText color="dark">
              <p style={{ margin: 0, fontSize: '14px', fontWeight: '500' }}>
                <strong>Service Discontinuation Notice:</strong> PikPak-Plus will be permanently discontinued on July 30, 2025. 
                Please backup your data before this date.
              </p>
            </IonText>
          </div>
        </IonCardContent>
    <IonCard color="light" className="custom-auth-container ">
        <IonCardHeader className="content-container">
          <IonCardTitle className="content-container-title">
            {titleHeading}
            <IonIcon size="default" icon={personCircleOutline}></IonIcon>
          </IonCardTitle>
          {MemoizedIonCardSubtitle}
        </IonCardHeader>

        <form onSubmit={handleOnSubmit}>
          <IonCardContent className="content-container">
            <IonInput
              placeholder="Enter Email"
              label="Email"
              labelPlacement="floating"
              autocomplete="on"
              fill="outline"
              ref={emailRef}
              disabled={isSignUp}
            />
            <IonInput
              placeholder="Enter Password"
              label="Password"
              labelPlacement="floating"
              fill="outline"
              type="password"
              ref={passwordRef}
              disabled={isSignUp}
            />
            <IonButton
              shape="round"
              expand="full"
              color={isSignUp ? "medium" : "tertiary"}
              type="submit"
              disabled={isSignUp}
            >
              {isSignUp ? "Sign Up Disabled" : "Submit"}
            </IonButton>
            {!isSignUp && MemoizedClearIonButton}
          </IonCardContent>
        </form>

        <IonToast
          isOpen={!!showToast}
          onDidDismiss={() => setShowToast(null)}
          message={showToast?.message}
          duration={2000}
          color={showToast?.color}
        />
      </IonCard>
    </div>
  )
}

export default AuthCard

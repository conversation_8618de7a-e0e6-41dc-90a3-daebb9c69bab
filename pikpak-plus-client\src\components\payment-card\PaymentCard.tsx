import {
  IonButton,
  IonContent,
  IonIcon,
  IonImg,
  IonInput,
  IonText,
} from '@ionic/react'
import {
  copyOutline,
  checkmarkOutline,
  footstepsOutline,
  megaphoneOutline,
  arrowForwardCircleOutline,
  cashOutline,
  diamondOutline,
  warningOutline,
  logoAppleAr,
  logoBitcoin,
  // logoPaypal,
} from 'ionicons/icons'
import { useState } from 'react'
import { writeToClipboard } from '../../helpers/helpers'
import HelperCard from '../HelperCard/HelperCard'
import pikpakplusupi from '../../../src/assets/pikpak-plus-upi.png'
import upiSVG from '../../../src/assets/upi-logo.svg'
import {
  oneDollarPlan,
  WEBDAV,
  ANNOUNCEMENT_OF_END,
} from '../../constants/constants'

function PaymentCard() {
  const [copied, setCopied] = useState(false)

  const renderCardList = (items: any[], Icon: any) => {
    return items.map((item, index) => (
      <div className="usefull-links" key={index}>
        <IonIcon color={'dark'} icon={Icon} />
        &nbsp;
        <IonText color={'dark'} key={index}>
          {item}
        </IonText>
      </div>
    ))
  }

  const handleCopy = (value: string) => {
    writeToClipboard(value)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <IonContent fullscreen={true}>
      <div>
        <HelperCard
          cardTitle="Anouncement"
          cardStyle={{
            margin: '0.5rem 3rem',
          }}
          cardSubtitle={renderCardList(ANNOUNCEMENT_OF_END, warningOutline)}
          cardSubTitleStyle={{
            display: 'flex',
            flexDirection: 'column',
          }}
          icon={megaphoneOutline}
          titleColor="danger"
          cardContent={
            <IonButton
              fill="outline"
              href={'/faq'}
              target="_blank"
              color={'tertiary'}
            >
              <IonText>
                <strong> FAQ </strong>
              </IonText>
              &nbsp;
              <IonIcon icon={arrowForwardCircleOutline} />
            </IonButton>
          }
        />
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          margin: '2rem 0rem 0rem 0rem',
        }}
      >
        <IonText
          color={'primary'}
          style={{
            fontSize: '1.5rem',
            textDecoration: 'underline',
          }}
        >
          Plans
        </IonText>
      </div>
      <div
        style={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap' }}
      >
        <div>
          <HelperCard
            cardTitle={
              <IonText
                style={{
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <b>Basic Plan</b>(100gb)
              </IonText>
            }
            cardSubtitle={renderCardList(oneDollarPlan, diamondOutline)}
            cardSubTitleStyle={{
              display: 'flex',
              flexDirection: 'column',
              textAlign: 'justify',
            }}
            titleColor="success"
            cardContent={
              <IonButton
                fill="outline"
                href={'/payment'}
                target="_blank"
                color={'tertiary'}
              >
                <IonIcon icon={cashOutline} />
                &nbsp;
                <IonText>
                  <strong>Purchase 5$</strong>
                </IonText>
              </IonButton>
            }
          />
        </div>
        <div>
          <HelperCard
            cardTitle="PRO PLAN (500gb)"
            cardSubtitle={renderCardList(WEBDAV, diamondOutline)}
            cardSubTitleStyle={{
              display: 'flex',
              flexDirection: 'column',
              textAlign: 'justify',
            }}
            icon={diamondOutline}
            titleColor="warning"
            cardContent={
              <>
                <IonButton
                  fill="outline"
                  href={'/payment'}
                  target="_blank"
                  color={'tertiary'}
                >
                  <IonIcon icon={cashOutline} />
                  &nbsp;
                  <IonText>
                    <strong>Purchase 10$</strong>
                  </IonText>
                </IonButton>
              </>
            }
          />
        </div>
      </div>

      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          margin: '1rem',
          flexDirection: 'column',
        }}
      >
        <IonText
          color={'primary'}
          style={{
            fontSize: '1.5rem',
            textDecoration: 'underline',
          }}
        >
          Payment methods
        </IonText>

        {/* <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: 'auto auto',
          }}
        >
          <div style={{ display: 'flex', flexDirection: 'column' }}>
          <IonText
              color={'tertiary'}
              style={{
                fontSize: '1.5rem',
                marginTop: '2rem',
                textAlign: 'center',
              }}
            >
              PayPal
            </IonText>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                border: '1rem solid #f5f5f5',
                borderRadius: '1rem',
                backgroundColor: '#f5f5f5',
                margin: '0.5rem 0rem',
              }}
            >
              <IonInput
                readonly
                value={'bharathganji1'}
                style={{
                  width: '8rem',
                }}
              >
                <IonIcon
                  slot="start"
                  icon={logoPaypal}
                  aria-hidden="true"
                ></IonIcon>
              </IonInput>
              <IonButton
                size="small"
                fill="clear"
                slot="end"
                onClick={() => handleCopy('bharathganji1')}
              >
                <IonIcon
                  slot="icon-only"
                  size="small"
                  color={copied ? 'success' : 'primary'}
                  icon={copied ? checkmarkOutline : copyOutline}
                  aria-hidden="true"
                ></IonIcon>
              </IonButton>
            </div>
          </div>
        </div> */}
        <IonText
          color={'tertiary'}
          style={{
            fontSize: '1.5rem',
            marginTop: '2rem',
            textAlign: 'center',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <IonIcon icon={logoBitcoin} aria-hidden="true"></IonIcon> CRYPTO -
          USDT (International users)
        </IonText>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'column',
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              border: '1rem solid #f5f5f5',
              borderRadius: '1rem',
              backgroundColor: '#f5f5f5',
              margin: '0.5rem 0rem',
            }}
          >
            <IonInput
              readonly
              value={'TRC20 (only)'}
              style={{
                width: '8rem',
                breakWord: 'break-all',
              }}
            >
              <IonIcon
                slot="start"
                icon={logoAppleAr}
                aria-hidden="true"
              ></IonIcon>
            </IonInput>
            <IonButton
              size="small"
              fill="clear"
              slot="end"
              onClick={() => handleCopy('TMMRA5aJ75d3of9Vb1ogeJ15fBRMr8Qq6A')}
            >
              <IonIcon
                slot="icon-only"
                size="small"
                color={copied ? 'success' : 'primary'}
                icon={copied ? checkmarkOutline : copyOutline}
                aria-hidden="true"
              ></IonIcon>
            </IonButton>
          </div>
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            border: '1rem solid #f5f5f5',
            borderRadius: '1rem',
            backgroundColor: '#f5f5f5',
            margin: '0.5rem 0rem',
          }}
        >
          <IonInput
            readonly
            value={'BEP20 (only)'}
            style={{
              width: '8rem',
              breakWord: 'break-all',
            }}
          >
            <IonIcon
              slot="start"
              icon={logoAppleAr}
              aria-hidden="true"
            ></IonIcon>
          </IonInput>
          <IonButton
            size="small"
            fill="clear"
            slot="end"
            onClick={() =>
              handleCopy('0x66b24f7d3d4d6494bae86871f56e8a4bd3e22d4b')
            }
          >
            <IonIcon
              slot="icon-only"
              size="small"
              color={copied ? 'success' : 'primary'}
              icon={copied ? checkmarkOutline : copyOutline}
              aria-hidden="true"
            ></IonIcon>
          </IonButton>
        </div>

        <IonText
          color={'tertiary'}
          style={{
            fontSize: '1.5rem',
            marginTop: '2rem',
            textAlign: 'center',
          }}
        >
          UPI - Indian users
        </IonText>
        <IonImg
          src={pikpakplusupi}
          style={{
            width: '200px',
            maxWidth: '100vw',
            height: 'auto',
            marginBottom: '0.5rem',
          }}
        />
        <IonButton
          expand="full"
          color="primary"
          fill="outline"
          href={pikpakplusupi}
          download={'pikpak-plus-upi.png'}
        >
          Download QR
        </IonButton>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            border: '1rem solid #f5f5f5',
            borderRadius: '1rem',
            backgroundColor: '#f5f5f5',
            margin: '0.5rem 0rem',
          }}
        >
          <IonInput
            readonly
            value={'pikpak-plus@ybl'}
            style={{
              width: '11rem',
            }}
          >
            <IonImg
              slot="start"
              src={upiSVG}
              style={{
                width: '1.5rem',
                height: '1.5rem',
                marginLeft: '0.5rem',
              }}
            />
          </IonInput>
          <IonButton
            size="small"
            fill="clear"
            slot="end"
            onClick={() => handleCopy('pikpak-plus@ybl')}
          >
            <IonIcon
              slot="icon-only"
              size="small"
              color={copied ? 'success' : 'primary'}
              icon={copied ? checkmarkOutline : copyOutline}
              aria-hidden="true"
            ></IonIcon>
          </IonButton>
        </div>
        <IonText
          color={'tertiary'}
          style={{
            fontSize: '1.5rem',
            marginTop: '2rem',
            textAlign: 'center',
          }}
        >
          Google Pay - International enabled UPI
        </IonText>
        <div
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            border: '1rem solid #f5f5f5',
            borderRadius: '1rem',
            backgroundColor: '#f5f5f5',
            margin: '0.5rem 0rem',
          }}
        >
          <IonInput
            readonly
            value={'bharathganji1@okaxis'}
            style={{
              width: '13.5rem',
            }}
          >
            <IonImg
              slot="start"
              src={upiSVG}
              style={{
                width: '1.5rem',
                height: '1.5rem',
                marginLeft: '0.5rem',
              }}
            />
          </IonInput>
          <IonButton
            size="small"
            fill="clear"
            slot="end"
            onClick={() => handleCopy('bharathganji1@okaxis')}
          >
            <IonIcon
              slot="icon-only"
              size="small"
              color={copied ? 'success' : 'primary'}
              icon={copied ? checkmarkOutline : copyOutline}
              aria-hidden="true"
            ></IonIcon>
          </IonButton>
        </div>
      </div>
      <HelperCard
        cardTitle="Steps to follow"
        cardSubtitle="convertion: 1$ = 80.00 INR"
        titleColor="success"
        cardContent={
          <>
            <ul>
              <li>
                For every 100GB extra storage, it will cost 2$ (160.00 INR)
              </li>
              <li>
                Similarly, for 200GB, the cost is 4$, and this pattern continues
                for larger storage capacities. The cost increments by 2$ for
                every 100GB of additional storage.
              </li>
              <li>
                post payment, send payment screenshot and your pikpak-plus
                email, to telegram or email
              </li>
            </ul>
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              <IonButton
                color="primary"
                fill="solid"
                href="https://t.me/bharathganji1"
              >
                Telegram
              </IonButton>
              <IonButton
                color="primary"
                fill="solid"
                href="mailto:<EMAIL>"
              >
                Mail
              </IonButton>
            </div>
          </>
        }
        icon={footstepsOutline}
        cardStyle={{
          margin: '0.5rem 3rem',
          fontSize: '1.2rem',
          color: 'var(--ion-color-primary)',
        }}
      />
    </IonContent>
  )
}

export default PaymentCard

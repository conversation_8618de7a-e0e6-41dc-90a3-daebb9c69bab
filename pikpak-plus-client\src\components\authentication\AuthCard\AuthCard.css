.custom-auth-container {
  position: relative;
  width: 90%;
}
.content-container {
  display: flex;
  gap: 1.125rem;
  flex-direction: column;
}
.content-container-title {
  display: flex;
  align-items: center;
  justify-content: center;
  column-gap: 0.25rem;
}

/* Media query for devices larger than tablets (e.g., desktops) */
@media (min-width: 768px) {
  .custom-auth-container {
    width: 50%;
  }
}

@media (min-width: 425px) {
  .backg {
    background-image: url('../../../assets/blob-scene-horizontal.svg');
  }
}

@media (max-width: 424px) {
  .backg {
    background-image: url('../../../assets/layered-waves.svg');
  }
}

.backg {
  background-size: cover;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
